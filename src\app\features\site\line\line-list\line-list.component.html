<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1 *ngIf="title">{{ title }}</h1>
        <div class="add-layout">
            <button class="add" mat-flat-button (click)="addLine()">
                <i class="material-icons">add</i>新增
            </button>
        </div>
        <div class="contents">
            <section class="block" *ngFor="let item of lineList">
                <span class="cont">
                    <span class="title">
                        {{ item.title }}
                    </span>
                    <span class="time">
                        {{item.startTime }}
                    </span>
                    <span class="description">
                        消息狀態 :&nbsp;{{ item.newStatus }}
                    </span>
                    <span class="description"> 建立者 :&nbsp;{{ item.createUser }} </span>
                    <span class="description"> 最後編輯者 :&nbsp;{{ item.editUser }} </span>
                    <span style="margin-left: auto">
                        <button mat-flat-button (click)="editLine(item.typeGroupId)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="delete(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
<form
  class="faq-edit-layout"
  [formGroup]="form"
  (ngSubmit)="submit(form.value)"
>
  <div class="contents">
    <span class="block">
      <span class="title">地址</span>
      <input type="text" formControlName="address" />
    </span>
    <span class="block">
      <span class="title">地圖類型</span>
      <mat-form-field>
        <mat-label>地圖類型</mat-label>
        <mat-select formControlName="provider">
          <mat-option
            *ngFor="let provider of providerList"
            [value]="provider.type"
          >
            {{ provider.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </span>
  </div>
  <div class="btns">
    <button mat-stroked-button mat-dialog-close type="button">取消</button>
    <button mat-flat-button color="primary" type="submit">確定</button>
  </div>
</form>

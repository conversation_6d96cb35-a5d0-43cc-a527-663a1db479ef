import { defaultItem } from './share.interface';

export interface getDonateItemListReq {
  keyword: string;
  currentPage: number;
  pageSize: number;
}

export interface getDonateItemListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: donateItem[];
  };
}
export interface donateItem {
  id: string;
  donateItem: string;
  enable: boolean;
}

export interface addDonateItemReq {
  donateItem: string;
}

export interface updateDonateItemReq extends addDonateItemReq {
  id: string;
  enable: boolean;
}

export interface getDonateAccountReconciliationListReq {
  startDate: string;
  endDate: string;
  donateItem: string;
  currentPage: number;
  pageSize: number;
}

export interface getDonateAccountReconciliationListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: donateAccountReconciliationItem[];
  };
}

export interface getDonateAccountReconciliationResp extends defaultItem {
  data: donateAccountReconciliationItem;
}
export interface donateAccountReconciliationItem {
  donateInfoId: string;
  donateItem: string;
  donateNumber: string;
  donateAmount: number;
  donateWay: string;
  donorInfo: string;
  donorName: string;
  donateTime: string;
  idNumber: string;
  phoneNumber: number;
  email: string;
  address: string;
  date: string;
  trueAmount: number;
  receiptNumber: number;
  isPublish: boolean;
  isNoName: boolean;
  isCheck: boolean;
  sort: number;
}

export interface addOrUpdateDonateAccountReconciliationReq {
  donateDataId?: string;
  donateItem: string;
  donorName: string;
  isNoName: boolean;
  donateNumber: string;
  donateAmount: number;
  donorInfo: string;
  donateWay: string;
  donateTime: string;
  idNumber: string;
  email: string;
  address: string;
  trueAmount: number;
  receiptNumber: string;
}

export interface exportDonateAccountReconciliationListReq {
  startDate: string;
  endDate: string;
  donateItem: string;
}

export interface setDonateAccountReconciliationSortReq {
  donateInfoId: string;
  sort: number;
}

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>財團法人原住民族語言研究發展基金會</title>
  <base href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet">

  <link href="assets/css/layout-style.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
  <script src="https://accounts.google.com/gsi/client" async defer></script>


</head>

<body>
  <app-root> </app-root>
  <script>


    // window.setWebSiteToken = function (token) {
    //   var website = location.pathname.split("/")[1];
    //   var tokenObjStr = sessionStorage.getItem("token");

    //   var obj = {};
    //   if (tokenObjStr) {
    //     obj = JSON.parse(tokenObjStr);
    //   }

    //   obj[website] = token;

    //   sessionStorage.setItem("token", JSON.stringify(obj));
    // };

    // window.getWebSiteToken = function (token) {
    //   var website = location.pathname.split("/")[1];
    //   var tokenObjStr = sessionStorage.getItem("token");

    //   var obj = {};
    //   if (tokenObjStr) {
    //     obj = JSON.parse(tokenObjStr);
    //   }

    //   var token = obj[website];

    //   if (!token) {
    //     var jsonObj = JSON.parse(tokenObjStr);
    //     for (var name in jsonObj) {
    //       var role = JSON.parse(
    //         atob(jsonObj[name].split(" ")[1].split(".")[1])
    //       )["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"];

    //       if (role === "Administrator" || role === "SeniorManager") {
    //         token = jsonObj[name];
    //         break;
    //       }
    //     }
    //   }

    //   return token;
    // };
    // var enable = [
    //   "<",
    //   ">",
    //   "=",
    //   "\\",
    //   "javascript:",
    //   "script",
    //   "%3C",
    //   "%3E",
    //   "vbscript:",
    //   "data:",
    // ];
    // //console.log(window.location.href.toLocaleLowerCase())
    // for (var i = 0; i < enable.length; i++) {
    //   if (window.location.href.toLocaleLowerCase().indexOf(enable[i]) != -1) {
    //     //console.log(window.location.href.toLocaleLowerCase().indexOf(enable[i])+enable[i])
    //     sessionStorage.setItem("token", "");
    //     window.location.href = "./manage/home";
    //   }

    //   //console.log(window.location.href.toLocaleLowerCase().indexOf(enable[i])+enable[i])
    // }
  </script>

  <script id="sessionStorageSet">
    sessionStorage.setItem(
      "webSiteId",
      "C52CD7E3-2EFB-478E-BEAB-CBF98DB8309D"
    );
    sessionStorage.setItem("webSiteName", "世壯運_日文版");

  </script>
</body>


</html>
.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  justify-content: center;
  position: relative;

  h2 {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 0;
  }

  .type-layout {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    height: 100%;
    margin-bottom: 10px;

    .block {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin: 0.5em;
      width: 200px;
      height: 200px;
      border-radius: 10px;
      padding: 10px;
      border: 2px solid transparent;

      &.selected {
        box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3),
          0 1px 3px 1px rgba(60, 64, 67, 0.15);
        border: 2px solid #ffff00;
      }

      &:hover {
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3),
          0 1px 3px 1px rgba(60, 64, 67, 0.15);
      }

      img {
        width: 100%;
      }
    }
  }

  .close-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px;
  }
}

.tags {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  overflow: hidden;

  button {
    border: 1px solid #ccc;
    background-color: #ffffff;
    border-radius: 15px;
    cursor: pointer;
    outline: 0;
    padding: 5px;
    margin: 5px;
    transition: 0.3s ease-in-out;

    &.selected {
      background-color: #ccc;
    }

    &:hover {
      border-color: #989898;
    }
  }
}

.tagsBar {
  display: block;
}

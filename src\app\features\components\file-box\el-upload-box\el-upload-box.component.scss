.upload-box-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: white;
  overflow-y: auto;
}

.upload-box {
  border: 2px dashed #ccc;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-box:hover {
  background-color: #f9f9f9;
}

.upload-link {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
}

// =======================
.dialog-title {
  display: flex;
  align-items: baseline;
}

mat-dialog-content,
mat-dialog-actions {
  background-color: white;
}

p {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 100%;
  width: 100%;
  margin-top: 10px;
}
.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f2f2f2;
  border-radius: 4px;
  width: 100%; // 避免被擠壓
  box-sizing: border-box;
  justify-content: space-between;
}

.name {
  max-width: calc(100% - 24px); // 預留刪除按鈕寬度
  overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  text-align: left;
  font-size: 14px;
  display: inline-block;
}

.file-remove {
  flex-shrink: 0; // 不允許被壓縮
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #888;

  &:hover {
    color: #f44336;
  }
}

.close-btn-layout {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import {
  createOrUpdateTextCloudReq,
  textCloudItem,
} from '../../../interface/textCloud.interface';
import { TextCloudService } from '../../../core/services/text-cloud.service';
import { ShareService } from '../../../core/services/share.service';
import { defaultItem } from '../../../interface/share.interface';

@Component({
  selector: 'app-text-cloud-setting-dialog',
  standalone: false,

  templateUrl: './text-cloud-setting-dialog.component.html',
  styleUrl: './text-cloud-setting-dialog.component.scss',
})
export class TextCloudSettingDialogComponent {
  text: string = '';
  wordCloudId: string = '';
  weight?: number;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: textCloudItem;
    },
    private dialogRef: MatDialogRef<TextCloudSettingDialogComponent>,
    private textCloudService: TextCloudService,
    private shareService: ShareService
  ) {
    if (this.data.data) {
      this.text = this.data.data.text;
      this.weight = this.data.data.weight;
      this.wordCloudId = this.data.data.wordCloudId;
    }
  }

  ngOnInit() {}

  submit() {
    if (this.text && this.weight) {
      let status: string = '新增';
      let req: createOrUpdateTextCloudReq = {
        text: this.text,
        weight: this.weight,
        lang: this.shareService.getLang(),
      };
      if (this.wordCloudId) {
        status = '編輯';
        req.wordCloudId = this.wordCloudId;
      }

      this.textCloudService.createOrUpdateTextCloud(req).subscribe({
        next: (resp: defaultItem) => {
          resp.code === 200
            ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
                this.dialogRef.close();
              })
            : Swal.fire('錯誤', resp.message, 'error');
        },
      });
    } else {
      Swal.fire('警告', '請輸入名稱和權重', 'warning');
    }
  }

  close() {
    this.dialogRef.close();
  }
}

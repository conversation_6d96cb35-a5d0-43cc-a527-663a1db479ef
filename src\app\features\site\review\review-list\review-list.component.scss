// 變數定義
$primary-blue: #005a9c;
$border-color: #e0e0e0;
$font-family: "Microsoft JhengHei", "微軟正黑體", sans-serif;

// 狀態顏色
$status-pending-color: #d9534f; // 未審核 (紅)
$status-approved-color: #5cb85c; // 已通過 (綠)
$status-rejected-color: #f0ad4e; // 已退回 (橘)
$status-editing-color: #6f5bde; // 編輯中 (藍)

.list-container {
    padding: 10px 20px;
    max-width: 1200px;
    width: 100%;
    align-items: center;
    margin: 0 auto;
}

.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;

    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;

        h1 {
            color: #2eaddb;
        }

        .title-description {
            max-width: 50%;
        }

        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;

            .block {
                min-height: 150px;
                display: flex;
                border-bottom: 1px solid #ccc;
                cursor: pointer;
                transition: 0.3s ease-in-out;
                padding: 1em 0;

                &:hover {
                    box-shadow: 0px 0px 9px 0px #ccc;
                }

                &.add {
                    justify-content: center;
                    align-items: center;
                    background-color: #b8d3f3;
                }

                .titles {
                    min-width: 250px;
                    display: flex;
                    flex-direction: column;
                    padding: 10px;

                    img {
                        width: 250px;
                    }

                    h4 {
                        line-height: 1.5;
                    }
                }

                .cont {
                    line-height: 2;
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .title {
                        font-size: 25px;
                        font-weight: bold;
                    }

                    .time {
                        color: #a57868;
                        font-style: italic;
                    }
                }
            }
        }
    }
}

.table-container {
    width: 100%;
    overflow-x: auto; // 在超大表格時提供水平滾動條
    background-color: #fff;
    border: 1px solid $border-color;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
        padding: 12px 15px;
        text-align: left;
        vertical-align: middle;
    }

    // 表格標頭
    thead {
        background-color: $primary-blue;
        color: white;

        th {
            font-weight: 600;
        }
    }

    // 表格內容
    tbody tr {
        border-bottom: 1px solid $border-color;
        &:last-child {
            border-bottom: none;
        }
    }

    // 狀態標籤
    .status-pending {
        color: $status-pending-color;
    }
    .status-approved {
        color: $status-approved-color;
    }
    .status-rejected {
        color: $status-rejected-color;
    }
    .status-editing {
        color: $status-editing-color;
    }

    // "審核" 圖示 (使用 CSS 繪製)
    .review-icon {
        display: inline-block;
        width: 20px;
        height: 24px;
        border: 2px solid #000; /* 預設黑色 */
        border-radius: 3px;
        position: relative;
        cursor: pointer;

        &::before {
            content: "";
            position: absolute;
            top: 4px;
            left: 3px;
            width: 10px;
            height: 2px;
            background-color: #000;
            box-shadow:
                0 4px 0 #000,
                0 8px 0 #000;
        }

        /* disabled 狀態 */
        &.disabled {
            border-color: #ccc; /* 灰色邊框 */
            cursor: not-allowed;

            &::before {
                background-color: #ccc;
                box-shadow:
                    0 4px 0 #ccc,
                    0 8px 0 #ccc;
            }
        }
    }
}

// 切換開關 (Toggle Switch)
.toggle-switch {
    position: relative;
    display: inline-flex;
    align-items: center;
    width: 80px;
    height: 30px;
    cursor: pointer;

    input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        border-radius: 34px;
        transition: 0.4s;

        &:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            border-radius: 50%;
            transition: 0.4s;
        }
    }

    .label-text {
        position: absolute;
        right: 15px;
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        transition: 0.4s;
    }

    input:checked + .slider {
        background-color: $status-approved-color;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    input:checked ~ .label-text {
        left: 15px;
        right: auto;
    }
}

// RWD - 手機版樣式 (寬度小於 768px)
@media screen and (max-width: 768px) {
    .review-table {
        thead {
            display: none; // 隱藏桌面版的標頭
        }

        tr {
            display: block;
            margin-bottom: 15px;
            border: 1px solid $border-color;
            border-radius: 4px;
        }

        td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            text-align: right; // 將內容推到右邊
            border-bottom: 1px solid #f5f5f5;
            &::before {
                content: attr(data-label); // 使用 data-label 作為標籤
                font-weight: bold;
                text-align: left;
                margin-right: 10px;
            }
            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.review-status-group {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 0 10px;
    .review-status {
        margin-right: 10px;
        padding: 5px 20px;
        border-radius: 15px;
        border: 1px #005a9c;
        cursor: pointer;
        transition: 0.3s ease-in-out;
        &.active {
            background-color: #005a9c;
            color: #ffff;
        }
    }
}

.user-search {
    padding: 10px 0;
}

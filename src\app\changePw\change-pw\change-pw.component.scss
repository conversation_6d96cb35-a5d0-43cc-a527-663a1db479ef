/* 基礎重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Arial", "微軟正黑體", sans-serif;
}

.body {
    min-height: 100vh;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 400px;
    text-align: center;
}

.logo {
    margin-bottom: 1.5rem;
}

h1 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.sports-icon {
    width: 80px;
    margin: 0 auto 1rem;
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: #34495e;
    font-weight: 600;
}

input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input:focus {
    outline: none;
    border-color: #4ecdc4;
}

.submit-btn {
    background: linear-gradient(45deg, #4ecdc4, #45b7af);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition:
        transform 0.2s,
        box-shadow 0.2s;
    text-transform: uppercase;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.back-login {
    margin-top: 1.5rem;
    color: #7f8c8d;
}

.back-login a {
    color: #4ecdc4;
    text-decoration: none;
    font-weight: 600;
}

@media (max-width: 480px) {
    .container {
        padding: 1.5rem;
    }

    h1 {
        font-size: 1.5rem;
    }
}

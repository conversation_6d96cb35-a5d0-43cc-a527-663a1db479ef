import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Video } from '../../shared/models/video.model';
import { Observable } from 'rxjs';
import { createUpdateResp, defaultItem, setSortReq } from '../../interface/share.interface';
import {
  getVideoListReq,
  getVideoListResp,
  createUpdateVideoReq,
  getVideoResp,
} from '../../interface/video.interface';

@Injectable({
  providedIn: 'root',
})
export class VideoService {
  private mngVideoApi: string = '/api/Manage/Video';

  constructor(private http: HttpClient) { }

  /**
   * 刪除指定實例
   *
   * @param id 唯一識別號
   */
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${this.mngVideoApi}/${id}`);
  }

  /**
   * 更新實例
   *
   * @param instance 實例內容
   */
  update(instance: Video): Observable<Video> {
    return this.http.put<Video>(this.mngVideoApi, instance);
  }

  getVideoList(req: getVideoListReq): Observable<getVideoListResp> {
    return this.http.get<getVideoListResp>('api/Manage/Video/GetVideoList', {
      params: {
        ...req,
      },
    });
  }

  getVideo(id: string): Observable<getVideoResp> {
    return this.http.get<getVideoResp>(`api/Manage/Video/GetVideoInfo`, {
      params: {
        typeGroupId: id,
      },
    });
  }

  getVideoForView(id: string): Observable<any> {
    return this.http.get(`api/Video/GetBackVideoForView`, {
      params: {
        typeGroupId: id,
      },
    });
  }

  getVideoAfterReviewForView(id: string): Observable<any> {
    return this.http.get(`api/Video/GetFrontVideoForView`, {
      params: {
        typeGroupId: id,
      },
    });
  }

  deleteVideo(id: string) {
    return this.http.delete('api/Manage/Video/DeleteVideo', {
      params: {
        typeGroupId: id,
      },
    });
  }

  createUpdateVideo(req: createUpdateVideoReq): Observable<createUpdateResp> {
    return this.http.post<createUpdateResp>(
      'api/Manage/Video/InsertOrUpdateVideo',
      req
    );
  }

  setSort(req: setSortReq): Observable<defaultItem> {
    return this.http.post<defaultItem>('api/Manage/Video/UpdateVideoSort', req);
  }
}

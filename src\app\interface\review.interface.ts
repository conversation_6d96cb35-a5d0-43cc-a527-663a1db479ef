import { defaultItem } from './share.interface';

export interface getReviewListReq {
  startDate: string;
  endDate: string;
  approvalLevel: number | null;
  status: number | null;
  enable: boolean | null;
  type: string;
  title: string;
  pageSize: number;
  currentPage: number;
  lang: string;
}

export interface getReviewListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: reviewItem[];
  };
}

export interface reviewItem {
  id: string;
  date: string;
  menuType: string;
  menuItemId: string;
  menuTypeText: string;
  name: string;
  levelDecision: string;
  status: string;
  enable: boolean;
  typeGroupId: string;
  isApprovedByManager: boolean;
}

export interface approvalReq {
  typeGroupId: string;
  approval: boolean;
  menuItemType: string;
  reason: string;
  file: File | null;
}

export interface batchApprovalReq {
  batchApprovalContent: approvalItem[];
}
export interface approvalItem {
  typeGroupId: string;
  menuItemType: string;
}

import {
  createMenuNameData,
  createMenuNameDialog,
  typeMenuItem,
} from './../../../shared/models/dialog.model';
import { Component, Inject, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { ManageService } from '../../../core/services/manage.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { WebSiteLang } from '../../../shared/models/webSiteLang.model';
import {
  createMenuItemReq,
  MenuItem,
} from '../../../shared/models/menuItem.model';
import { MenuItemService } from '../../../core/services/menuItem.service';
import Swal from 'sweetalert2';
import { TypeList } from '../../../enum/share.enum';

@Component({
  selector: 'app-create-menu-name',
  standalone: false,

  templateUrl: './create-menu-name.component.html',
  styleUrl: './create-menu-name.component.scss',
})
export class CreateMenuNameComponent {
  name: string = '';
  englishName: string = '';
  amisName: string = '';
  atayaiName: string = '';
  paiwanName: string = '';
  bununName: string = '';
  puyumaName: string = '';
  rukaiName: string = '';
  tsouName: string = '';
  saisiyatName: string = '';
  taoName: string = '';
  thaoName: string = '';
  kavalanName: string = '';
  trukuName: string = '';
  sakizayaName: string = '';
  seediqName: string = '';
  saaroaName: string = '';
  kanakanavuName: string = '';

  method = 'add';
  parentId: string | null = null;
  isSubmit = false;

  langList: WebSiteLang[] = [];
  olbLang = 'null';

  loading = false;

  menuItem: typeMenuItem | MenuItem | null = null;
  treeMoveable: MenuItem[] = [];
  // selecetedParent = null;

  @ViewChild(MatMenuTrigger) trigger!: MatMenuTrigger;
  dialogData!: createMenuNameData;

  constructor(
    private _manageService: ManageService,
    private _menuItemService: MenuItemService,
    public dialogRef: MatDialogRef<CreateMenuNameComponent>,
    @Inject(MAT_DIALOG_DATA) public data: createMenuNameDialog
  ) {
    console.log(this.data);
  }

  ngOnInit(): void {
    this.dialogData = this.data.dataGroup;
    console.log(this.dialogData);
    this.menuItem = this.dialogData.selectedData;
    this.name = this.dialogData.selectedData.name as string;
    this.englishName = (this.dialogData.selectedData as MenuItem)
      .englishName as string;
      this.amisName = (this.dialogData.selectedData as MenuItem)
      .amisName as string;
      this.atayaiName = (this.dialogData.selectedData as MenuItem)
      .atayaiName as string;
      this.paiwanName = (this.dialogData.selectedData as MenuItem)
      .paiwanName as string;
      this.bununName = (this.dialogData.selectedData as MenuItem)
      .bununName as string;
      this.puyumaName = (this.dialogData.selectedData as MenuItem)
      .puyumaName as string;
      this.rukaiName = (this.dialogData.selectedData as MenuItem)
      .rukaiName as string;
      this.tsouName = (this.dialogData.selectedData as MenuItem)
      .tsouName as string;
      this.saisiyatName = (this.dialogData.selectedData as MenuItem)
      .saisiyatName as string;
      this.taoName = (this.dialogData.selectedData as MenuItem)
      .taoName as string;
      this.thaoName = (this.dialogData.selectedData as MenuItem)
      .thaoName as string;
      this.kavalanName = (this.dialogData.selectedData as MenuItem)
      .kavalanName as string;
      this.trukuName = (this.dialogData.selectedData as MenuItem)
      .trukuName as string;
      this.sakizayaName = (this.dialogData.selectedData as MenuItem)
      .sakizayaName as string;
      this.seediqName = (this.dialogData.selectedData as MenuItem)
      .seediqName as string;
      this.saaroaName = (this.dialogData.selectedData as MenuItem)
      .saaroaName as string;
      this.kanakanavuName = (this.dialogData.selectedData as MenuItem)
      .kanakanavuName as string;
    this.method = this.dialogData.method;
    if (this.dialogData.parentId) {
      this.parentId = this.dialogData.parentId;
    }
  }

  submit() {
    if (!this.englishName && !this.name) {
      Swal.fire('請輸入選單名稱', '', 'warning');
      return;
    }
    this.isSubmit = true;
    let data: MenuItem;

    if (this.method === 'add') {
      const selectedData = this.dialogData.selectedData as typeMenuItem;
      let data: createMenuItemReq = {
        webSiteId: sessionStorage.getItem('webSiteId') as string,
        type: selectedData.type,
        name: this.name,
        englishName: this.englishName,
        amisName: this.amisName,
        atayaiName: this.atayaiName,
        paiwanName: this.paiwanName,
        bununName: this.bununName,
        puyumaName: this.puyumaName,
        rukaiName: this.rukaiName,
        tsouName: this.tsouName,
        saisiyatName: this.saisiyatName,
        taoName: this.taoName,
        thaoName: this.thaoName,
        kavalanName: this.kavalanName,
        trukuName: this.trukuName,
        sakizayaName: this.sakizayaName,
        seediqName: this.seediqName,
        saaroaName: this.saaroaName,
        kanakanavuName: this.kanakanavuName,

        visibility: 'Visable',
        parentId: this.parentId as string,
        menuItem_R_Type: [
          {
            id: '00000000-0000-0000-0000-000000000000',
            menuItemId: selectedData.MenuItemId as any,
            menu_Type: selectedData.MenuItemType,
          },
        ],
      };
      this._menuItemService.create(data).subscribe((x) => {
        this._manageService.menuItemChange.next({
          data: data,
          method: this.method,
        });
      });
    } else {
      const selectedData = this.dialogData.selectedData as MenuItem;
      data = selectedData;
      data.parentId = selectedData.parentId;
      data.name = this.name;
      data.englishName = this.englishName;
      data.amisName = this.amisName;
      data.atayaiName = this.atayaiName;
      data.paiwanName = this.paiwanName;
      data.bununName = this.bununName;
      data.puyumaName = this.puyumaName;
      data.rukaiName = this.rukaiName;
      data.tsouName = this.tsouName;
      data.saisiyatName = this.saisiyatName;
      data.taoName = this.taoName;
      data.thaoName = this.thaoName;
      data.kavalanName = this.kavalanName;
      data.trukuName = this.trukuName;
      data.sakizayaName = this.sakizayaName;
      data.seediqName = this.seediqName;
      data.saaroaName = this.saaroaName;
      data.kanakanavuName = this.kanakanavuName;
      this._menuItemService.update(data).subscribe((x) => {
        this._manageService.menuItemChange.next({
          data: data,
          method: this.method,
        });
      });
    }
    this.dialogRef.close();
  }
}

<div class="auth-layout">
    <div class="block">
        <div class="title-ctn">
            <span class="title">Desktop版Banner圖</span>
        </div>
        <div>
            <button mat-flat-button class="btn" (click)="selectGalleryImage(deviceType.PC)">
                {{this.status}}
            </button>&nbsp;&nbsp;&nbsp;
        </div>
        @if(cover){
        <img [src]="cover" alt="" width="100px">
        }
    </div>
    <div class="block">
        <div class="title-ctn">
            <span class="title">Moblie版Banner圖</span>
        </div>
        <div style="display: flex;">
            <div>
                <button mat-flat-button class="btn" (click)="selectGalleryImage(deviceType.MOBLIE)">
                    {{this.status}}
                </button>&nbsp;&nbsp;&nbsp;
            </div>
            <div>
                <button mat-flat-button class="btn" (click)="cuteBannerImg()" [disabled]="!croppedImage">
                    裁切
                </button>&nbsp;&nbsp;&nbsp;
            </div>
            @if(croppedImage){
            <img [src]="croppedImage" alt="" width="100px">
            }
        </div>

    </div>
    <div class="block">
        <mat-form-field class="example-form-field" appearance="outline">
            <mat-label>標題</mat-label>
            <input matInput type="text" [(ngModel)]="content">
        </mat-form-field>
    </div>
    <div class="block">
        <mat-form-field class="example-form-field" appearance="outline">
            <mat-label>連結</mat-label>
            <input matInput type="text" [(ngModel)]="meta">
        </mat-form-field>
    </div>

    <div class="btn-group">
        <button mat-flat-button (click)="close()">取消</button>
        <button mat-flat-button (click)="create()">確定</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
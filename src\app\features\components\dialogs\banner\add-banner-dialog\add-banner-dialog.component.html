<div class="auth-layout">
    <div class="block">
        <div class="title-ctn">
            <span class="title">Banner</span>
        </div>
        <div>
            <button mat-flat-button class="btn" (click)="selectGalleryImage()">
                {{this.status}}
            </button>&nbsp;&nbsp;&nbsp;
        </div>
        @if(cover){
        @if(type === 'Image'){
        <img [src]="cover" alt="" width="100px">
        }@else {
        <video width="100%" [src]="cover" controls></video>
        }
        }
    </div>
    <div class="block">
        <mat-form-field class="example-form-field" appearance="outline">
            <mat-label>標題</mat-label>
            <input matInput type="text" [(ngModel)]="title">
        </mat-form-field>
    </div>
    <div class="block">
        <mat-form-field class="example-form-field" appearance="outline">
            <mat-label>連結</mat-label>
            <input matInput type="text" [(ngModel)]="link">
        </mat-form-field>
    </div>
    <div class="toggle-block">
        <mat-slide-toggle [(ngModel)]="enable">
            <mat-label>啟用</mat-label>
        </mat-slide-toggle>
    </div>

    <div class="btn-group">
        <button mat-flat-button (click)="close()">取消</button>
        <button mat-flat-button (click)="create()">確定</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
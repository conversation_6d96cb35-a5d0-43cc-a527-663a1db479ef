import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { ShareService } from '../../../../core/services/share.service';
import {
  defaultItem,
  getTypeListResp,
} from '../../../../interface/share.interface';
import { FileBoxComponent } from '../../file-box/file-box.component';
import {
  createUpdateShareResourceReq,
  shareResourceItem,
} from '../../../../interface/shareResource.interface';
import { ShareResourceService } from '../../../../core/services/shareResource.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-share-resource-dialog',
  standalone: false,

  templateUrl: './share-resource-dialog.component.html',
  styleUrl: './share-resource-dialog.component.scss',
})
export class ShareResourceDialogComponent {
  loading = false;

  typeList: {
    typeValue: string;
    typeName: string;
  }[] = [];

  name: string = '';
  file: string = '';
  fileId: string = '';
  shareResourceId: string = '';

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      status: string;
      title: number;
      menuItemId: string;
      item?: shareResourceItem;
    },
    private dialogRef: MatDialogRef<DialogComponent>,
    private shareService: ShareService,
    public dialog: MatDialog,
    private shareResourceService: ShareResourceService
  ) {
    if (this.data.item) {
      this.name = this.data.item.name;
      this.file = this.data.item.fileName;
      this.fileId = this.data.item.fileId;
      this.shareResourceId = this.data.item.shareResourceId;
    }
  }

  ngOnInit(): void {
    this.getTypeList();
  }

  getTypeList() {
    this.shareService.getTypeList('運動種類').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
      },
    });
  }

  selectGalleryFile() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'File',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.file = resp.data.name;
          this.fileId = resp.data.previewImageDataId;
        }
      });
  }

  create() {
    let req: createUpdateShareResourceReq = {
      menuItemId: this.data.menuItemId,
      name: this.name,
      fileId: this.fileId,
      shareResourceId: this.shareResourceId,
      lang: this.shareService.getLang(),
    };
    this.shareResourceService.createUpdateShareResource(req).subscribe({
      next: (resp: defaultItem) => {
        resp.code === 200
          ? this.dialogRef.close(true)
          : Swal.fire(
              '失敗',
              `${this.data.status === 'ADD' ? '新增' : '編輯'}失敗`,
              'error'
            );
      },
      error: () => {},
    });
  }
  close() {
    this.dialogRef.close(false);
  }
}

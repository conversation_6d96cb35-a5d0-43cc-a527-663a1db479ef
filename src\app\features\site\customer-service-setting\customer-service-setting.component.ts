import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { ChatService } from '../../../core/services/chat.service';
import {
  addCustomerServiceReq,
  customerServiceItem,
  getCustomerServiceListResp,
} from '../../../interface/chat.interface';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { CustomerServiceDialogComponent } from '../../components/customer-service-dialog/customer-service-dialog.component';
import { defaultItem } from '../../../interface/share.interface';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-customer-service-setting',
  standalone: false,

  templateUrl: './customer-service-setting.component.html',
  styleUrl: './customer-service-setting.component.scss',
})
export class CustomerServiceSettingComponent {
  lang: string = '中文';
  loading: boolean = false;

  keyword: string = '';
  customerServicesList: customerServiceItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService,
    private chatService: ChatService,
    private dialog: MatDialog
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.getCustomerServiceList();
  }

  searchlist() {
    this.getCustomerServiceList();
  }

  getCustomerServiceList() {
    this.chatService.getCustomerServiceList().subscribe({
      next: (resp: getCustomerServiceListResp) => {
        this.customerServicesList = resp.data;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.getCustomerServiceList();
  }

  add() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: CustomerServiceDialogComponent,
          showHeader: true,
          title: '新增',
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getCustomerServiceList();
      });
  }

  edit(item: customerServiceItem) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: CustomerServiceDialogComponent,
          showHeader: true,
          title: '編輯',
          data: item,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getCustomerServiceList();
      });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.chatService.deleteCustomerService(id).subscribe({
          next: (resp: defaultItem) => {
            this.loading = false;
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                  this.getCustomerServiceList();
                })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  activeChange(item: customerServiceItem) {
    const originalEnable = item.enable;
    let req: addCustomerServiceReq = {
      aiQuestionId: item.id,
      aiQuestion: item.question,
      aiAnswer: item.answer,
      enable: !item.enable,
    };
    this.chatService.addCustomerService(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code !== 200) {
          Swal.fire('失敗', resp.message, 'error');
          item.enable = !originalEnable;
        } else {
          item.enable = !item.enable;
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
        item.enable = !originalEnable;
      },
    });
  }
}

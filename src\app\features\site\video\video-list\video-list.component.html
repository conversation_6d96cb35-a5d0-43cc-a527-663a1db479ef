<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1 *ngIf="title">{{ title }}</h1>
        <div class="add-layout">
            <button class="add" mat-flat-button (click)="addKeyword()">
                <i class="material-icons">add</i>關鍵字管理
            </button> &nbsp;&nbsp;
            <button class="add" mat-flat-button (click)="addVideo()">
                <i class="material-icons">add</i>新增
            </button>
        </div>

        <div class="contents">
            <section class="block" *ngFor="let item of videoList; let index = index">
                <span class="titles">
                    <img *ngIf="item?.coverUrl" [src]="item.coverUrl" />
                </span>
                <span class="cont">
                    <div *ngIf="item.isTop" class="top">
                        <span>置頂</span>
                    </div>
                    <span class="title">
                        {{ item.title }}
                    </span>
                    <span class="description">
                        排序 :&nbsp;{{ item.sort }}
                    </span>
                    <span style="margin-left: auto">
                        <button mat-flat-button (click)="setSort(item)">
                            排序</button>&nbsp;
                        <button mat-flat-button (click)="editVideo(item)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="deleteVideo(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
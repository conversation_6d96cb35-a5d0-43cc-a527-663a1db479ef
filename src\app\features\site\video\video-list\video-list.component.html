<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1 *ngIf="title">{{ title }}</h1>
        <div class="add-layout">
            <button class="add" mat-flat-button (click)="addVideo()">
                <i class="material-icons">add</i>新增
            </button>
        </div>

        <div class="contents">
            <section class="block" *ngFor="let item of videoList; let index = index">
                <span class="titles">
                    <img *ngIf="item?.coverUrl" [src]="item.coverUrl" />
                </span>
                <span class="cont">
                    <div *ngIf="item.isTop" style="
                    display: flex;
                    align-self: start; 
                    border: solid #00bcd4;
                    border-radius: 20px;
                    width: 60px;
                    align-items: center;
                    justify-content: center;
                    color: #005CBB;">
                        <span>置頂</span>
                    </div>
                    <span class="title">
                        {{ item.title }}
                    </span>
                    <span style="margin-left: auto">
                        @if(!item.isTop){
                        <button mat-flat-button (click)="changeSort(item.videoId,false)" [disabled]="index===0">
                            向上</button>&nbsp;
                        <button mat-flat-button (click)="changeSort(item.videoId,true)"
                            [disabled]="(index + (nowPage - 1) * pageSize +1)===totalCount">
                            向下</button>&nbsp;
                        }
                        <button mat-flat-button (click)="editVideo(item)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="deleteVideo(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
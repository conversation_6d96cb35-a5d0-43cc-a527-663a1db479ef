import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { ContentEditorComponent } from '../../../components/content-editor/content-editor.component';
import { contentData } from '../../../../interface/editor.interface';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { GroupReportDialogComponent } from '../../../components/group-report-dialog/group-report-dialog.component';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import Swal from 'sweetalert2';
import { ENewsletterService } from '../../../../core/services/e-newsletter.service';
import {
  createUpdateResp,
  defaultItem,
} from '../../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';
import {
  createUpdateENewsletterReq,
  getENewsletterResp,
} from '../../../../interface/eNewsletter.interface';
import { EDITORTYPE } from '../../../../enum/editor.enum';
import { v4 as uuidv4 } from 'uuid';
import { format, toZonedTime } from 'date-fns-tz';
import { ApprovalStatus } from '../../../../enum/share.enum';

@Component({
  selector: 'app-e-newsletter-edit',
  standalone: false,

  templateUrl: './e-newsletter-edit.component.html',
  styleUrl: './e-newsletter-edit.component.scss',
})
export class ENewsletterEditComponent implements OnInit, AfterViewInit {
  @ViewChild(ContentEditorComponent)
  ContentEditorComponent: ContentEditorComponent | undefined;
  loading: boolean = false;
  today: Date = new Date();
  status: string = '新增';
  isPendingApproval: boolean = false;
  menuItemId: string = '';
  contentData: contentData[] = [];
  typeGroupId: string = '';

  name: string = '';
  publishTime: string = '';
  levelDecision: number = 2;
  groupReport: number = 1;
  template: number = 1;
  groupReportList: { new_NewsId: string; title: string }[] = [];
  createUser: string = '';
  editUser: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private eNewsLetterService: ENewsletterService
  ) {}

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id')) {
        this.typeGroupId = queryParams.get('id')!;
        this.getENewsletter();
      }
    });
  }
  ngAfterViewInit(): void {
    if (!this.typeGroupId && this.ContentEditorComponent) {
      setTimeout(() => {
        this.ContentEditorComponent!.addEditorBlock('content');
      });
    }
  }

  getENewsletter() {
    this.eNewsLetterService.getENewsletter(this.typeGroupId).subscribe({
      next: (resp: getENewsletterResp) => {
        this.name = resp.data.name;
        this.createUser = resp.data.creater;
        this.editUser = resp.data.editor;
        this.reason = resp.data.reason;
        this.reviewFileName = resp.data.reviewFileName;
        this.reviewFileUrl = resp.data.reviewFileUrl;
        this.isPendingApproval = resp.data.isPendingApproval;
        this.publishTime = resp.data.e_NewsletterPublishDateTime;
        this.levelDecision = resp.data.levelDecision;
        this.template = resp.data.emailTemplate;
        this.groupReportList = resp.data.new_NewsDatas.map((item) => {
          return { new_NewsId: item.new_NewsId, title: item.title };
        });
        resp.data.e_NewsletterTagDatas.map((item) => {
          this.contentData.push({
            id: uuidv4(),
            type: item.tagName as EDITORTYPE,
            data: item.tagData,
          });
        });
        if (this.contentData && this.contentData.length > 0) {
          if (this.ContentEditorComponent) {
            this.ContentEditorComponent.initializeForm(this.contentData);
          }
        } else {
          this.ContentEditorComponent!.addEditorBlock('content');
        }
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  getContentData(contentData: contentData[]) {
    this.contentData = contentData;
  }

  addGroupReport() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '900px',
          height: '400px',
          contentTemplate: GroupReportDialogComponent,
          showHeader: true,
          data: {
            menuType: this.groupReport,
            // groupReportList: [...this.groupReportList],
          },
          title: '組報',
        },
      })
      .afterClosed()
      .subscribe(
        (
          resp: {
            new_NewsId: string;
            title: string;
          }[]
        ) => {
          if (resp) {
            const newItemsToAdd = resp.filter(
              (newItem) =>
                !this.groupReportList.some(
                  (existingItem) =>
                    existingItem.new_NewsId === newItem.new_NewsId
                )
            );
            this.groupReportList = [...this.groupReportList, ...newItemsToAdd];
          }
        }
      );
  }

  deleteGroupReport(item: { new_NewsId: string; title: string }) {
    this.groupReportList = this.groupReportList.filter(
      (x) => x.new_NewsId !== item.new_NewsId
    );
  }

  save() {
    Swal.fire({
      html: `
          <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
          <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
        `,
      showCancelButton: true,

      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        if (!this.name) {
          Swal.fire('警告', `尚未填寫電子報名稱`, 'warning');
          return;
        }
        if (!this.publishTime) {
          Swal.fire('警告', `尚未填寫出刊日期`, 'warning');
          return;
        }

        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateENewsletterReq = {
          menuitemId: this.menuItemId,
          e_NewsletterTagDatas: newsData,
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
          typeGroupId: this.typeGroupId,
          name: this.name,
          e_NewsletterPublishDateTime: this.publishTime
            ? format(
                toZonedTime(this.publishTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          emailTemplate: this.template,
          new_NewsIds: this.groupReportList.map((item) => item.new_NewsId),
        };
        this.loading = true;
        this.eNewsLetterService.createUpdateENewsletter(req).subscribe({
          next: (resp: createUpdateResp) => {
            this.loading = false;
            if (resp.code === 200) {
              Swal.fire('成功', `儲存成功`, 'success').then(() => {
                this.router.navigate([
                  `/manage/${this.menuItemId}/eNewsletter/list`,
                ]);
              });
            } else {
              Swal.fire('失敗', `${resp.message}`, 'error');
            }
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', `${err.error.message}`, 'error');
          },
        });
      }
    });
  }
  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/viewENewsletter'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'Content',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    Swal.fire({
      html: `
          <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
          <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
        `,
      showCancelButton: true,

      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        if (!this.name) {
          Swal.fire('警告', `尚未填寫電子報名稱`, 'warning');
          return;
        }
        if (!this.publishTime) {
          Swal.fire('警告', `尚未填寫出刊日期`, 'warning');
          return;
        }

        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateENewsletterReq = {
          menuitemId: this.menuItemId,
          e_NewsletterTagDatas: newsData,
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
          typeGroupId: this.typeGroupId,
          name: this.name,
          e_NewsletterPublishDateTime: this.publishTime
            ? format(
                toZonedTime(this.publishTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          emailTemplate: this.template,
          new_NewsIds: this.groupReportList.map((item) => item.new_NewsId),
        };
        this.loading = true;
        this.eNewsLetterService.createUpdateENewsletter(req).subscribe({
          next: (resp: createUpdateResp) => {
            this.loading = false;
            resp.code === 200
              ? this.router.navigate(['manage/viewENewsletter'], {
                  queryParams: {
                    menuItemId: this.menuItemId,
                    typeGroupId: resp.data,
                    type: 'ENewsletter',
                    status: ApprovalStatus.BeforeApproval,
                  },
                })
              : Swal.fire('失敗', `${resp.message}`, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', `${err.error.message}`, 'error');
          },
        });
      }
    });
  }

  cancel() {
    history.back();
  }
}

import { Injectable, NgZone } from '@angular/core';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
declare const google: any;

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private clientId = environment.googleLoginClientID;
  private tokenClient: any;
  private googleTokenSubject = new Subject<any>();

  constructor(private ngZone: NgZone, private http: HttpClient) {}

  GooglePopup(): Observable<string> {
    // 檢查 Google 函式庫是否已載入
    if (
      typeof google === 'undefined' ||
      !google.accounts ||
      !google.accounts.oauth2
    ) {
      // 當 Google SDK 未載入時，發送錯誤
      this.googleTokenSubject.error(
        new Error('Google Identity SDK not loaded.')
      );
      return this.googleTokenSubject.asObservable();
    }

    this.tokenClient = google.accounts.oauth2.initTokenClient({
      client_id: this.clientId,
      scope:
        'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
      callback: (tokenResponse: any) => {
        this.ngZone.run(() => {
          if (tokenResponse && tokenResponse.access_token) {
            // 成功時，將 tokenResponse 發送出去
            this.googleTokenSubject.next(tokenResponse.access_token);
          } else {
            // 失敗時，發送錯誤
            this.googleTokenSubject.error(
              new Error('Invalid Google token response.')
            );
          }
        });
      },
    });
    // 觸發彈出式視窗
    this.tokenClient.requestAccessToken();

    // 回傳 Subject 的 Observable
    return this.googleTokenSubject.asObservable();
  }

  getUserEmail(accessToken: string): Observable<any> {
    return this.http.get('https://www.googleapis.com/oauth2/v3/userinfo', {
      params: {
        access_token: accessToken,
      },
    });
  }

  signOut() {
    // 登出邏輯，例如清除本地使用者狀態
  }
}

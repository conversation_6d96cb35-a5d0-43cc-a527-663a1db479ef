<div>
  <div class="contents">
    <div class="block">
      <mat-form-field>
        <mat-label>影片類型</mat-label>
        <mat-select [(ngModel)]="form.type">
          @if(!data.isENewsletter){
          <mat-option value="Upload">檔案</mat-option>
          }
          <mat-option value="Youtube">Youtube</mat-option>
        </mat-select>
      </mat-form-field>
      @if(form.type=='Youtube'){
      <mat-form-field>
        <mat-label>Youtube網址</mat-label>
        <input matInput type="text" placeholder="Youtube網址" [(ngModel)]="form.youtubeUrl" autocomplete="off" />
      </mat-form-field>
      }@else{
      <div class="select-image">
        <button mat-flat-button color="primary" type="button" (click)="selectVideo()">
          選擇影片</button>&nbsp;※&nbsp;只能上傳mp4
        <video width="100%" [src]="form.videoDataId" autoplay controls></video>
      </div>
      }
    </div>
    <div class="block row active-group">
      <span class="title">啟用</span>
      <mat-slide-toggle color="primary" [(ngModel)]="form.enable"></mat-slide-toggle>
      <span class="title">置頂</span>
      <mat-slide-toggle color="primary" [(ngModel)]="form.onHomepage"></mat-slide-toggle>
    </div>
  </div>
  <div class="btns">
    <button mat-stroked-button mat-dialog-close type="button">取消</button>
    <button mat-flat-button class="danger" (click)="delete()">刪除</button>
    <button mat-flat-button color="primary" (click)="submit()">確定</button>
  </div>
</div>
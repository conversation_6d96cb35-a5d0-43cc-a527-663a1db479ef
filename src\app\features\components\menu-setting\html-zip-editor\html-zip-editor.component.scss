.contents {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 98%;
    .title-ctn {
        display: flex;
        flex-direction: column;
        input[type="file"] {
            display: none;
        }
        button {
            width: 130px;
            margin: 10px 0;
        }
        .file-name {
            margin-left: 10px;
        }
    }
}

.block {
    margin: 10px 10px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #cccccc33;
    width: 100%;
    .title {
        line-height: 2;
    }
}

.close-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px;
}

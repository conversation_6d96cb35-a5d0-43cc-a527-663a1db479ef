<div class="banner-layout">
  <ngx-hm-carousel
    *ngIf="banners.length"
    [autoplay-speed]="3000"
    [autoplay]="autoplay"
    [infinite]="infinite"
    [between-delay]="2000"
    class="carousel c-accent"
  >
    <section ngx-hm-carousel-container class="content">
      <article
        class="item cursor-pointer"
        ngx-hm-carousel-item
        *ngFor="let banner of banners"
      >
        <!-- <div *ngIf="banner.type === 'Image'" class="img" [style.backgroundImage]="'url(' + banner?.url + ')'">
        </div>
        <div *ngIf="banner.type === 'Video'" class="video"  [style.backgroundImage]="'url(' + banner?.url + ')'">
          <video style="width:100vw" [src]="banner.url" autoplay muted loop (loadeddata)="videoLoad($event)"  ></video>
          {{banner.url}}
        </div>
        <div *ngIf="banner.type === 'Youtube'" class="img" [style.backgroundImage]="'url(' + banner?.url + ')'">
        </div> -->

        <div ng-container [ngSwitch]="banner.type" class="img">
          <img
            *ngSwitchCase="'Image'"
            style="width: 100vw"
            [src]="banner.pcUrl"
          />
          <!-- <img *ngSwitchCase="'Youtube'" style="width:100vw;" [src]="banner.url" > -->
          <iframe
            *ngSwitchCase="'Youtube'"
            style="width: 100vw; height: 45vh"
            [src]="banner.bannerDataId! | safe : 'resourceUrl'"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
          ></iframe>
          <video
            *ngSwitchCase="'Video'"
            style="width: 100vw"
            [src]="banner.pcUrl"
            autoplay
            muted
            loop
            (loadeddata)="videoLoad($event)"
          ></video>
        </div>
      </article>
    </section>
  </ngx-hm-carousel>

  <app-setting-block
    requireFunctionPolicy="BannerManage"
    [isLayout]="false"
    [move]="false"
    (setting)="setting()"
  >
  </app-setting-block>
  <app-loading [loading]="loading"></app-loading>
</div>

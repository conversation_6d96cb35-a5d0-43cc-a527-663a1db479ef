import { defaultItem } from "../../interface/share.interface";

export interface getBannerListResp extends defaultItem {
  data: {
    isBannerEnable: boolean;
    bannerList: Banner[];
  }
}
export interface Banner {
  id: string;
  bannerDataId: string;
  url: string;
  type?: string;
  enable: boolean;
  sort?: number;
  title: string;
  link: string;
}

export interface createrorUpdateBannerReq {
  id?: string;
  bannerDataId?: string;
  title?: string;
  link?: string;
  type: string;
  enable: boolean;
  lang: string;
}

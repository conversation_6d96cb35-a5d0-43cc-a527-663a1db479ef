export interface Banner {
  id: string;
  webSiteId?: string;
  sort?: number;
  type?: string;
  enable?: boolean;
  meta: string;
  bannerContent: string;
  bannerDataId: string;
  pcUrl: string;
  bannerMobileDataId: string;
  mobileURL: string;
}

export interface createrBannerReq {
  bannerId?: string;
  websiteId: string;
  pcDataId: string;
  content?: string;
  url?: string;
  mobileDataId: string;
  mobileFile?: Blob;
  uploadType?: string; //cut or dataId
}

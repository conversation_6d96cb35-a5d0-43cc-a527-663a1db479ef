import { Component, Inject } from '@angular/core';
import Swal from 'sweetalert2';
import { VideoService } from '../../../core/services/video.service';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { videoCreateDialog } from '../../../shared/models/dialog.model';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../file-box/file-box.component';
import { environment } from '../../../../environments/environment';

export interface contentItem {
  id: string;
  videoId: string;
  title: string;
  content: string;
  isEdit?: boolean;
  selected?: boolean;
  sort?: number;
}

@Component({
  selector: 'app-video-create',
  standalone: false,

  templateUrl: './video-create.component.html',
  styleUrl: './video-create.component.scss',
})
export class VideoCreateComponent {
  OnlyYoutube: boolean = true;
  form: any = {
    id: '00000000-0000-0000-0000-000000000000',
    title: null,
    description: '',
    enable: true,
    videoDataId: null,
    menuItemId: null,
    videoContent: [],
    videoDocument: [],
    videoUrl: null,
    time: Math.floor(new Date().getTime() / 1000),
    type: 'Upload',
    onHomepage: true,
    youtubeUrl: null,
    sort: 0,
    previewImageUrl: null,
    coverDataId: null,
  };
  froalaOptions = environment.froalaConfig;
  contentList: contentItem[] = [
    {
      id: '00000000-0000-0000-0000-000000000000',
      videoId: '00000000-0000-0000-0000-000000000000',
      title: '新頁籤',
      content: '',
      isEdit: false,
      selected: true,
    },
  ];
  contentIndex: number = 0;
  content: string = '';
  loading: boolean = false;
  documents: File[] = [];
  popupData!: any;
  videoFile: any;

  constructor(
    private _videoService: VideoService,
    @Inject(MAT_DIALOG_DATA) public data: videoCreateDialog,
    public dialogRef: MatDialogRef<DialogComponent>,
    public dialog: MatDialog
  ) {
    if (this.data.isENewsletter) {
      this.form.type = 'Youtube';
    }
  }

  ngOnInit(): void {
    this.popupData = this.data.data;
    if (this.popupData) {
      this.form.title = this.popupData.title;
      this.form.type = this.popupData.type;
      this.form.videoDataId = this.popupData.videoDataId;
      this.form.enable = this.popupData.enable;
      this.form.menuItemId = this.popupData.menuItemId;
      this.form.id = this.popupData.id;
      this.form.coverDataId = this.popupData.coverDataId;
      this.form.youtubeUrl = this.popupData.youtubeUrl;
    }

    // var popLength = this._pop.popupList.length - 1;
    // console.log(this._pop)
    // if (this._pop.popupList[popLength].option.title == '設定Youtube網址') {
    //   this.OnlyYoutube = false;
    // }
  }

  /** 選擇影片 */
  selectVideo() {
    const fileBoxDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '500px',
        contentTemplate: FileBoxComponent,
        type: 'Video',
      },
    });
    fileBoxDialog.afterClosed().subscribe((res) => {
      if (res) {
        this.videoFile = res.data;
        this.form.videoDataId = this.videoFile.url;
        this.form.previewImageUrl = this.videoFile.previewImageUrl;
        this.form.coverDataId = res.data.previewImageDataId;
      }
    });
  }

  /** 編輯頁籤 */
  editTag(item: contentItem, index: number) {
    this.contentList.map((x) => {
      x.isEdit = false;
      x.selected = false;
      return x;
    });
    item.isEdit = true;
    item.selected = true;
    this.contentIndex = index;
    this.content = item.content;
  }

  /** 頁籤輸入完 */
  inputTag($event: any, item: contentItem) {
    item.title = $event.target.value;
  }

  /** 新增頁籤 */
  createTag() {
    this.contentList.push({
      id: '00000000-0000-0000-0000-000000000000',
      videoId: '00000000-0000-0000-0000-000000000000',
      title: '新頁籤',
      content: '',
      isEdit: false,
      selected: false,
    });
  }

  deleteTag(index: number) {
    this.contentList.splice(index, 1);
  }

  inputTagContent(editor: any) {
    this.contentList[this.contentIndex].content = this.content;
  }

  /** 選擇檔案 */
  // uploadFile($event: Event) {
  //   const target = $event.target as HTMLInputElement;
  //   const files = target.files;
  //   if (files && files.length) {
  //     for (let i = 0; i < files.length; i++) {
  //       this.documents.push(files[i]);
  //     }
  //   }
  // }

  // deleteFiles(index: number) {
  //   this.documents.splice(index, 1);
  // }

  delete() {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        if (this.popupData) {
          this._videoService.delete(this.popupData.id).subscribe((x) => {});
        } else {
          Swal.fire('錯誤', '請選擇影片', 'error');
        }
        this.dialogRef.close();
      }
    });
  }

  submit() {
    if (this.contentList[this.contentIndex].content.length > 0) {
      Swal.fire({
        title:
          '<h3 class="accessibility-reminder-title">內容編輯是否有符合無障礙AA規範</h3>',
        html: `<p class="accessibility-reminder-important">
      <span class="accessibility-reminder-important-icon"><img src="../../../../../assets/images/caution.png" alt=""></span>
      注意事項
    </p>
    <ul class="accessibility-reminder-cont">
      <li class="accessibility-reminder-list">
        <span class="accessibility-reminder-list-title">圖片</span>
        <span>是否有替代說明文字(排版美編圖則不用)</span>
      </li>
      <li class="accessibility-reminder-list">
        <span class="accessibility-reminder-list-title">表格</span>
        <span>是否有區分標題欄位與資料欄位(用來排版的則不用)</span>
      </li>
      <li class="accessibility-reminder-list">
        <span class="accessibility-reminder-list-title">連結</span>
        <span>外連網站是否有提示另開新視窗或另開新頁</span>
      </li>
    </ul>
      `,
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
      }).then((res) => {
        if (res.value) {
          if (this.form.type === 'Upload') {
            if (!this.form.videoDataId) {
              Swal.fire('錯誤', '請選擇影片', 'error');
              return;
            }
          }

          if (this.form.type === 'Youtube') {
            if (!this.form.youtubeUrl) {
              Swal.fire('錯誤', '請輸入Youtube網址', 'error');
              return;
            }
          }

          const temp: contentItem[] = [];
          this.contentList.map((res, index) => {
            temp.push({
              id: res.id,
              videoId: res.videoId,
              title: res.title,
              content: res.content,
              sort: index,
            });
          });
          this.form.videoContent = temp;
          this.dialogRef.close({
            data: this.form,
            documents: this.documents,
          });
        }
      });
    } else {
      if (this.form.type === 'Upload') {
        if (!this.form.videoDataId) {
          Swal.fire('錯誤', '請選擇影片', 'error');
          return;
        }
      }

      if (this.form.type === 'Youtube') {
        if (!this.form.youtubeUrl) {
          Swal.fire('錯誤', '請輸入Youtube網址', 'error');
          return;
        } else {
          if (this.form.youtubeUrl.indexOf('www.youtube.com') == -1) {
            Swal.fire('錯誤', '請輸入正確Youtube網址', 'error');
            return;
          }
        }
      }
      const temp: contentItem[] = [];
      this.form.videoContent = temp;
      if (this.popupData) {
        if (this.popupData.id == null) {
        } else {
          this._videoService.update(this.form).subscribe((x) => {});
        }
      }
      this.dialogRef.close({
        data: this.form,
        documents: this.documents,
      });
    }
  }
}

<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>項目管理</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
            <div>
                <button mat-flat-button (click)="add()">新增</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">捐款項目</th>
                            <th width="70px">啟用狀態</th>
                            <th width="70px">功能</th>
                    </thead>
                    <tbody>
                        @for (item of donateItemList; track item) {
                        <tr>
                            <td data-label="捐款項目">{{item.donateItem}}</td>
                            <td data-label="啟用狀態">
                                <mat-slide-toggle class="example-margin" [checked]="item.enable"
                                    (change)="activeChange(item)">
                                </mat-slide-toggle>
                            </td>
                            <td data-label="功能">
                                <button mat-flat-button (click)="edit(item)">編輯</button>&nbsp;
                                <button mat-flat-button class="danger" (click)="delete(item.id)">刪除</button>
                            </td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="3" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
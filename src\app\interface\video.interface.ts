import { defaultItem } from './share.interface';

export interface getVideoListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
  streamDateTime?: string;
  lang: string;
}

export interface getVideoListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: videoItem[];
  };
}
export interface videoItem {
  videoId: string;
  typeGroupId: string;
  isPendingApproval: boolean; //審核中
  publishDateTime: string; //是否有發布版及當前發布版發布時間null為無
  title: string;
  coverUrl: string;
  youtubeUrl: string;
  startTime: string;
  videoStatus: string;
  createUser: string;
  editUser: string;
  isTop: boolean;
  sort: number;
}

export interface getVideoResp extends defaultItem {
  data: {
    menuitemId: string;
    typeGroupId: string;
    keyword: string;
    isPendingApproval: boolean; //審核中
    publishDateTime: string; //是否有發布版及當前發布版發布時間null為無
    levelDecision: number;
    titleName: string;
    youtubeUrl: string;
    startTime: string;
    endTime: string;
    description: string;
    isTop: boolean;
    createUser: string;
    createTime: string;
    editUser: string;
    editTime: string;
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface createUpdateVideoReq {
  menuItemId: string;
  typeGroupId?: string;
  title: string;
  keyword: string;
  youtubeUrl: string;
  isTop: boolean;
  startTime: string;
  lang: string;
  levelDecision: number;
}

export interface uploadVideoStreamReq {
  menuItemId: string;
  data: string;
}

.createWebs-layout {
    width: 100vw;
    height: 100vh;
    background-size: 100% 50%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    .header {
        display: flex;
        justify-content: flex-end;
        /*align-items: center;*/
        width: 100vw;

        .logout {
            padding: 0 1em;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            .text {
                transition: .3s ease-in-out;
            }

            &:hover {
                .text {
                    font-weight: bold;
                    text-shadow: 3px 3px 5px;
                }
            }
        }
    }

    .createWebs-contents {
        display: flex;
        // justify-content: center;
        // align-items: center;
        flex-wrap: wrap;
        margin: calc(50vh - 175px) 10% 0 10%;
        position: relative;

        .block {
            width: 200px;
            height: 200px;
            margin: 1em;
            border-radius: 10px;
            background-color: #fff;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            color: #000000db;
            font-weight: bold;
            outline: 0;
            cursor: pointer;
            transition: .3s ease-in-out;

            &.add {
                color: #039be5;
            }

            .add-icon {
                font-size: 5em;
            }

            &:hover {
                font-weight: bold;
                color: #ff861e;
                box-shadow: 0 0 10px #969696;
            }
        }
    }

    .user-layout {
        position: fixed;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        left: -300px;
        top: 200px;
        transition: .3s ease-in-out;
        width: 340px;

        &.isShow {
            left: 0;
        }

        .list-block {
            width: 300px;
            height: 350px;
            background-color: #00000080;
            color: #ffffff;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;

            ul {
                padding: 10px;
                margin: 0;
                overflow: auto;

                /* Track */
                &::-webkit-scrollbar-track {
                    background: transparent;
                }

                /* Handle */
                &::-webkit-scrollbar-thumb {
                    background: transparent;
                }

                /* Handle on hover */
                &::-webkit-scrollbar-thumb:hover {
                    background: #555;
                }

                li {
                    line-height: 2;
                    cursor: pointer;
                    transition: .3s ease-in-out;

                    &.selected {
                        font-weight: bold;
                        text-decoration: underline;
                    }

                    &:hover {
                        text-shadow: 3px 3px 5px;
                    }
                }
            }

            .add {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 5px 0;
                background-color: rgba(255, 134, 30, 0.5);
                cursor: pointer;
                position: absolute;
                width: 100%;
                bottom: 0;
                left: 0;
                transition: .3s ease-in-out;

                &:hover {
                    background-color: rgba(255, 134, 30, 0.8);
                }
            }
        }

        .tag {
            background: linear-gradient(-135deg, transparent 10px, #039be5 0) top right;
            padding: 10px;
            color: #fff;
            flex-direction: column;
            cursor: pointer;
            transition: .3s ease-in-out;
            writing-mode: vertical-lr;
            text-align: center;
            letter-spacing: 5px;

            &:hover {
                background: linear-gradient(-135deg, transparent 10px, #039be5b3 0) top right;
            }
        }
    }
}
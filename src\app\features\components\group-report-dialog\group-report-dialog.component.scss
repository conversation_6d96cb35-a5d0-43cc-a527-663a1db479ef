// 變數定義
$primary-blue: #005a9c;
$border-color: #e0e0e0;
$font-family: "Microsoft JhengHei", "微軟正黑體", sans-serif;

.block {
    margin: 5px 0;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #cccccc33;
    width: 95%;
}
.tabs-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-bottom: 1px solid $border-color; /* 新增底部邊框 */
    margin-bottom: 10px; /* 與表格間的間距 */
}
.tab {
    flex: 1;
    text-align: center;
    padding: 10px 0;
    cursor: pointer;
    color: #555;
    font-size: 16px;
    position: relative;
}

/* Style for the active tab */
.tab.active {
    color: #007bff;
}

/* Underline for the active tab */
.tab.active::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #007bff;
}

.table-container {
    width: 100%;
    overflow-x: auto; // 在超大表格時提供水平滾動條
    background-color: #fff;
    border: 1px solid $border-color;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-table {
    width: 100%;
    border-collapse: collapse;
    th,
    td {
        padding: 12px 15px;
        text-align: left;
        vertical-align: middle;
    }

    // 表格標頭
    thead {
        background-color: $primary-blue;
        color: white;

        th {
            font-weight: 600;
        }
    }

    // 表格內容
    tbody tr {
        border-bottom: 1px solid $border-color;
        &:last-child {
            border-bottom: none;
        }
    }

    // "審核" 圖示 (使用 CSS 繪製)
    .review-icon {
        display: inline-block;
        width: 20px;
        height: 24px;
        border: 2px solid #000; /* 預設黑色 */
        border-radius: 3px;
        position: relative;
        cursor: pointer;

        &::before {
            content: "";
            position: absolute;
            top: 4px;
            left: 3px;
            width: 10px;
            height: 2px;
            background-color: #000;
            box-shadow:
                0 4px 0 #000,
                0 8px 0 #000;
        }

        /* disabled 狀態 */
        &.disabled {
            border-color: #ccc; /* 灰色邊框 */
            cursor: not-allowed;

            &::before {
                background-color: #ccc;
                box-shadow:
                    0 4px 0 #ccc,
                    0 8px 0 #ccc;
            }
        }
    }
}

// RWD - 手機版樣式 (寬度小於 768px)
@media screen and (max-width: 768px) {
    .review-table {
        thead {
            display: none; // 隱藏桌面版的標頭
        }

        tr {
            display: block;
            margin-bottom: 15px;
            border: 1px solid $border-color;
            border-radius: 4px;
        }

        td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            text-align: right; // 將內容推到右邊
            border-bottom: 1px solid #f5f5f5;
            &::before {
                content: attr(data-label); // 使用 data-label 作為標籤
                font-weight: bold;
                text-align: left;
                margin-right: 10px;
            }
            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.btn-group {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    button {
        margin: 0 10px;
    }
}

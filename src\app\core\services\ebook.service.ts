import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  addEbookReq,
  createUpdateEbookResp,
  getEbookListReq,
  getEbookListResp,
  getEbookResp,
  updateEbookReq,
} from '../../interface/ebook.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class EbookService {
  constructor(private httpCleint: HttpClient) {}

  getEbookList(req: getEbookListReq): Observable<getEbookListResp> {
    return this.httpCleint.post<getEbookListResp>(
      'api/Manage/Ebook/EbookList',
      req
    );
  }

  getEbook(id: string): Observable<getEbookResp> {
    return this.httpCleint.get<getEbookResp>(`api/Manage/Ebook/${id}`);
  }

  addEbook(req: addEbookReq): Observable<createUpdateEbookResp> {
    let formData = new FormData();
    formData.append('menuItemId', req.menuItemId);
    formData.append('title', req.title);
    formData.append('author', req.author);
    formData.append('introduction', req.introduction);
    formData.append('outline', req.outline);
    formData.append('littleTitle', req.littleTitle);
    formData.append('littleTitleZh', req.littleTitleZh);
    formData.append('period', req.period);
    formData.append('coverDataId', req.coverDataId);
    formData.append('isTop', req.isTop.toString());
    formData.append('startTime', req.startTime);
    formData.append('endTime', req.endTime);
    formData.append('lang', req.lang);
    formData.append('levelDecision', req.levelDecision.toString());
    formData.append('FromFile', req.FromFile as File);
    return this.httpCleint.post<createUpdateEbookResp>(
      'api/Manage/Ebook/CreateEbook',
      formData
    );
  }

  updateEbook(req: updateEbookReq): Observable<createUpdateEbookResp> {
    let formData = new FormData();
    formData.append('id', req.id);
    formData.append('typeGroupId', req.typeGroupId);
    formData.append('menuItemId', req.menuItemId);
    formData.append('title', req.title);
    formData.append('author', req.author);
    formData.append('introduction', req.introduction);
    formData.append('outline', req.outline);
    formData.append('littleTitle', req.littleTitle);
    formData.append('littleTitleZh', req.littleTitleZh);
    formData.append('period', req.period);
    formData.append('coverDataId', req.coverDataId);
    formData.append('isTop', req.isTop.toString());
    formData.append('startTime', req.startTime);
    formData.append('endTime', req.endTime);
    formData.append('levelDecision', req.levelDecision.toString());
    formData.append('lang', req.lang);
    if (req.FromFile) {
      formData.append('FromFile', req.FromFile as File);
    }
    return this.httpCleint.put<createUpdateEbookResp>(
      'api/Manage/Ebook/UpdateBook',
      formData
    );
  }

  deleteEbook(id: string, typeGroupId: string): Observable<defaultItem> {
    return this.httpCleint.post<defaultItem>(`api/Manage/Ebook`, {
      id: id,
      typeGroupId: typeGroupId,
    });
  }
}

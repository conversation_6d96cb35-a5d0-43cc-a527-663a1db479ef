import { Component, Inject } from '@angular/core';
import {
  AppCategoryLinkDto,
  appCategoryList,
  AppCategoryRequest,
} from '../../../../shared/models/appcategory.model';
import { AppCategoryService } from '../../../../core/services/appCategory.service';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { FileBoxComponent } from '../../../components/file-box/file-box.component';

@Component({
  selector: 'app-more-page-setting-edit',
  standalone: false,

  templateUrl: './more-page-setting-edit.component.html',
  styleUrl: './more-page-setting-edit.component.scss',
})
export class MorePageSettingEditComponent {
  constructor(
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<DialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _appCategoryService: AppCategoryService
  ) {
    this.AppCategoryRequest = new AppCategoryRequest(); // 确保 AppCategoryRequest 有一个默认实例
    this.AppCategoryList = {
      alias: {
        zh_tw: false,
        en: false,
        jp: false,
        kr: false,
      },
      name: {
        zh_twName: '',
        enName: '',
        jpName: '',
        krName: '',
      },
    }; // 初始化为适合 appCategoryList 结构的对象
    this.appCategoryLinks = []; // 初始化为空数组
  }

  //權限列表
  permissionOptions = [
    {
      name: '未登入',
      code: '0',
      isSelected: false,
    },
    {
      name: '選手',
      code: '1',
      isSelected: false,
    },
    {
      name: '隊職員及教練',
      code: '2',
      isSelected: false,
    },
    {
      name: '陪同者',
      code: '3',
      isSelected: false,
    },
    {
      name: '帕拉選手助理員',
      code: '3-1',
      isSelected: false,
    },
    {
      name: '12歲以下報名陪同者',
      code: '3-2',
      isSelected: false,
    },
    {
      name: '貴賓',
      code: '4',
      isSelected: false,
    },
    {
      name: '技術代表及技術官員',
      code: '5',
      isSelected: false,
    },
    {
      name: '工作人員',
      code: '6',
      isSelected: false,
    },
    {
      name: '志工',
      code: '7',
      isSelected: false,
    },
    {
      name: '媒體',
      code: '8',
      isSelected: false,
    },
    {
      name: '贊助商',
      code: '9',
      isSelected: false,
    },
  ];

  //分類標題列表
  category = [
    {
      alias: 'zh_tw',
      ischeck: false,
      name: '分類標題-中文:',
      lang: '請輸入中文標題',
      checkName: '中文',
      categoryName: '',
    },
    {
      alias: 'en',
      ischeck: false,
      name: '分類標題-英文:',
      lang: '請輸入英文標題',
      checkName: '英文',
      categoryName: '',
    },
    {
      alias: 'jp',
      ischeck: false,
      name: '分類標題-日文:',
      lang: '請輸入日文標題',
      checkName: '日文',
      categoryName: '',
    },
    {
      alias: 'kr',
      ischeck: false,
      name: '分類標題-韓文:',
      lang: '請輸入韓文標題',
      checkName: '韓文',
      categoryName: '',
    },
  ];

  // 獨立的連結名稱與網址資料結構
  categoryLinks = [
    {
      alias: 'zh_tw',
      linkName: '',
      linkUrl: '',
      ischeck: false,
      checkName: '中文',
      isPrivacyChecked: false,
      privacyDescription: '',
    },
    {
      alias: 'en',
      linkName: '',
      linkUrl: '',
      ischeck: false,
      checkName: '英文',
      isPrivacyChecked: false,
      privacyDescription: '',
    },
    {
      alias: 'jp',
      linkName: '',
      linkUrl: '',
      ischeck: false,
      checkName: '日文',
      isPrivacyChecked: false,
      privacyDescription: '',
    },
    {
      alias: 'kr',
      linkName: '',
      linkUrl: '',
      ischeck: false,
      checkName: '韓文',
      isPrivacyChecked: false,
      privacyDescription: '',
    },
  ];

  // 存放所有新增的連結設定
  linkSettings: any[] = [];

  imageFile: any;

  AppCategoryRequest!: AppCategoryRequest;
  AppCategoryList!: appCategoryList;
  appCategoryLinks!: AppCategoryLinkDto[];
  popupData: any;

  ngOnInit() {
    //console.log(this.data.data);
    this.popupData = this.data.data;
    if (this.popupData != null) {
      this.getCategoryData(this.popupData.categoryId);
    }
  }

  getCategoryData(categorId: string) {
    this._appCategoryService.getCategoryInfo(categorId).subscribe({
      next: (res) => {
        this.AppCategoryRequest = res;

        // 初始化語言
        this.category.forEach((item) => {
          item.ischeck =
            res.appCategoryList.alias[
              item.alias as keyof typeof this.AppCategoryList.alias
            ] || false;
          item.categoryName =
            res.appCategoryList.name[
              `${item.alias}Name` as keyof typeof this.AppCategoryList.name
            ] || '';
        });

        // 初始化權限
        const selectedPermissions = res.permissions!.split(',');
        this.permissionOptions.forEach((option) => {
          option.isSelected = selectedPermissions.includes(option.code);
        });

        // 初始化連結
        // 初始化連結
        this.linkSettings = res.appCategoryLinks.map((link) => {
          const lang = this.categoryLinks.map((langItem) => {
            const newLangItem = {
              alias: langItem.alias,
              checkName: langItem.checkName,
              linkName: '',
              linkUrl: '',
              ischeck: false,
              isPrivacyChecked: false,
              privacyDescription: '',
            };

            // 檢查 link.linkInFo 和 link.alias 是否存在
            if (
              link.linkInFo &&
              link.linkInFo[
                langItem.alias as keyof typeof this.AppCategoryList.alias
              ]
            ) {
              const linkInfo =
                link.linkInFo[
                  langItem.alias as keyof typeof this.AppCategoryList.alias
                ];
              newLangItem.linkName = linkInfo.name || '';
              newLangItem.linkUrl = linkInfo.url || '';
              newLangItem.isPrivacyChecked = linkInfo.isPrivacyChecked || false;
              newLangItem.privacyDescription =
                linkInfo.privacyDescription || '';
            }

            if (
              link.alias &&
              link.alias[
                langItem.alias as keyof typeof this.AppCategoryList.alias
              ]
            ) {
              newLangItem.ischeck =
                link.alias[
                  langItem.alias as keyof typeof this.AppCategoryList.alias
                ];
            }

            return newLangItem;
          });

          // 對應每個 link 的 permissions 屬性
          const permissions: { [key: string]: boolean } = {};
          const linkPermissions = link.permissions.split(','); // 確保是以逗號分隔的字串
          this.permissionOptions.forEach((option) => {
            permissions[option.code as string] = linkPermissions.includes(
              option.code
            );
          });

          return {
            appCategoryLinkId: link.appCategoryLinkId || '',
            lang,
            permissions,
          };
        });

        //console.log(this.linkSettings);
      },
      error: (err: HttpErrorResponse) => {
        //console.log(err);
      },
    });
  }

  // 新增整個連結設定的功能
  addLink() {
    const newLink = this.categoryLinks.map((lang) => ({
      ...lang, // 複製每個語系的資料
      linkName: '', // 初始化連結名稱
      linkUrl: '', // 初始化連結網址
    }));

    // 將新的連結設定推入 linkSettings，並初始化權限
    const permissions = this.permissionOptions.reduce(
      (acc: { [key: string]: boolean }, option) => {
        acc[option.code] = false; // 初始化每個權限為 false
        return acc;
      },
      {}
    );

    this.linkSettings.push({ lang: newLink, permissions });

    //console.log(this.linkSettings);
  }

  // 切換選項的方法
  toggleCategory(alias: string) {
    if (alias === 'all') {
      // 檢查「全部」選項的當前狀態
      let isAllChecked = false;
      this.category.forEach((item) => {
        if (item.alias === 'all') {
          isAllChecked = item.ischeck;
        }
      });

      // 如果「全部」選項是被勾選的，則取消勾選所有選項
      if (isAllChecked) {
        this.category.forEach((item) => (item.ischeck = false));
      } else {
        // 如果「全部」選項未被勾選，則只選中「全部」，取消其他選項的選擇
        this.category.forEach((item) => (item.ischeck = item.alias === 'all'));
      }
    } else {
      // 如果選擇單一語系，取消「全部」的勾選，並切換該語系的勾選狀態
      this.category.forEach((item) => {
        if (item.alias === 'all') {
          item.ischeck = false;
        } else if (item.alias === alias) {
          item.ischeck = !item.ischeck; // 切換該語系的勾選狀態
        }
      });
    }
  }

  // 切換語系選擇的狀態
  toggleLanguage(link: any, alias: string) {
    const selectedLanguage: { alias: string; ischeck: boolean } | undefined =
      link.lang.find((lang: { alias: string }) => lang.alias === alias);

    if (selectedLanguage) {
      if (alias === 'all') {
        // 當「全部」被勾選時，取消其他語系的勾選
        const isChecked = selectedLanguage.ischeck; // 獲取「全部」的當前狀態並翻轉

        link.lang.forEach((lang: { alias: string; ischeck: boolean }) => {
          lang.ischeck = lang.alias === 'all' ? isChecked : false; // 僅勾選「全部」，取消其他
        });
      } else {
        // 當勾選其他語系時，切換該語系的勾選狀態
        selectedLanguage.ischeck = selectedLanguage.ischeck;

        // 如果該語系被勾選，則取消「全部」的勾選
        const allOption = link.lang.find(
          (lang: { alias: string; ischeck: boolean }) => lang.alias === 'all'
        );
        if (allOption) {
          allOption.ischeck = false; // 確保「全部」未勾選
        }

        // 檢查是否有其他語系被勾選
        const anyLanguageSelected = link.lang.some(
          (lang: { alias: string; ischeck: boolean }) =>
            lang.alias !== 'all' && lang.ischeck
        );
      }

      // 強制更新顯示
      link.lang = Object.assign([], link.lang);
    }
  }

  saveCategorySetting() {
    this.CategorySetting();
    if (this.AppCategoryRequest) {
      this.insertOrUpdateAppCategory(this.AppCategoryRequest);
    }
  }

  CategorySetting() {
    // 格式化 category 数据

    this.category.forEach((item) => {
      if (this.AppCategoryList && this.AppCategoryList.alias) {
        this.AppCategoryList.alias[
          item.alias as keyof typeof this.AppCategoryList.alias
        ] = item.ischeck;
      }
      this.AppCategoryList.name[
        `${item.alias}Name` as keyof typeof this.AppCategoryList.name
      ] = item.categoryName;
    });

    // 格式化 permissions 数据
    const permissions = this.permissionOptions
      .filter((option) => option.isSelected)
      .map((option) => option.code)
      .join(',');

    //console.log(permissions);

    this.appCategoryLinks = this.linkSettings.map((linkSetting) => {
      // 設置alias和linkInFo
      interface Alias {
        zh_tw: boolean;
        en: boolean;
        jp: boolean;
        kr: boolean;
        [key: string]: boolean; // Add index signature
      }

      const alias: Alias = {
        zh_tw: false,
        en: false,
        jp: false,
        kr: false,
      };

      interface LinkInfo {
        zh_tw: {
          name: string;
          url: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        };
        en: {
          name: string;
          url: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        };
        jp: {
          name: string;
          url: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        };
        kr: {
          name: string;
          url: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        };
      }

      interface LinkInfo {
        [key: string]: {
          name: string;
          url: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        };
      }

      const linkInFo: LinkInfo = {
        zh_tw: {
          name: '',
          url: '',
          isPrivacyChecked: false,
          privacyDescription: '',
        },
        en: {
          name: '',
          url: '',
          isPrivacyChecked: false,
          privacyDescription: '',
        },
        jp: {
          name: '',
          url: '',
          isPrivacyChecked: false,
          privacyDescription: '',
        },
        kr: {
          name: '',
          url: '',
          isPrivacyChecked: false,
          privacyDescription: '',
        },
      };

      // 構建語系的連結信息
      linkSetting.lang.forEach(
        (lang: {
          alias: string;
          ischeck: boolean;
          linkName: string;
          linkUrl: string;
          isPrivacyChecked: boolean;
          privacyDescription: string;
        }) => {
          alias[lang.alias] = lang.ischeck;
          linkInFo[lang.alias] = {
            name: lang.linkName || '',
            url: lang.linkUrl || '',
            isPrivacyChecked: lang.isPrivacyChecked || false,
            privacyDescription: lang.privacyDescription || '',
          };
        }
      );

      // 構建權限字串
      const permissions = Object.keys(linkSetting.permissions)
        .filter((code) => linkSetting.permissions[code])
        .join(',');

      // 返回構建好的AppCategoryLinkDto對象
      return {
        appCategoryLinkId: linkSetting.appCategoryLinkId || '',
        alias,
        linkInFo,
        permissions,
      } as AppCategoryLinkDto;
    });

    // 更新AppCategoryRequest
    this.AppCategoryRequest = {
      ...this.AppCategoryRequest,
      imageUrl: this.imageFile || this.AppCategoryRequest.imageUrl,
      appCategoryList: this.AppCategoryList,
      permissions,
      appCategoryLinks: this.appCategoryLinks,
    };

    //console.log('更新后的 AppCategoryRequest:', this.AppCategoryRequest);
  }

  insertOrUpdateAppCategory(appCategoryRequest: AppCategoryRequest) {
    this._appCategoryService.insertOrUpdate(this.AppCategoryRequest).subscribe({
      next: (res) => {
        if (res == true) {
          Swal.fire('成功儲存', '', 'success').then(() => {
            // 使用者按下確認後執行此段代碼
            //console.log(res);

            this.dialogRef.close(res);
          });
        } else {
          Swal.fire('儲存失敗', '', 'error');
        }
      },
    });
  }

  selectImage() {
    const selectImageDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '500px',
        contentTemplate: FileBoxComponent,
        type: 'Image',
        isMultiple: false,
      },
    });

    selectImageDialog.afterClosed().subscribe((res: any) => {
      if (res) {
        this.AppCategoryRequest.imageUrl = res.data.url;
      }
    });
  }
}

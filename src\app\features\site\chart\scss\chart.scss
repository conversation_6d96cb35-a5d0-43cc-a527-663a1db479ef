mat-grid-tile {
    border-radius: 5px;
}

.img {
    width: 40px;
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;

    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.header {
    padding: 10px 0 0 25px;
    display: flex;
    align-items: center;
    font-size: 1.5em;
}

.title {
    margin-left: -15px;
    text-align: left;
    line-height: 1.6;
    font-size: 1.1em;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

mat-icon {
    font-size: 2em;
    margin-right: 16px;
    cursor: pointer;
}

.menu-icon {
    font-size: 36px;
    height: 36px;
    width: 36px;
    margin-right: 5px;
    font-size: 36px;
    height: 36px;
    width: 36px;
    margin-right: 5px;
}

.normal-background-color {
    border: solid #bfbfbf;
    border-radius: 0.5em;
}

/* 網頁捲軸【寬度】 */
::-webkit-scrollbar {
    width: 20px;
    padding-right: 12px;
}

/* 網頁捲軸【背景】顏色 */
::-webkit-scrollbar-track {
    background: #c8c8c8;
}

/* 網頁捲軸【把手】顏色 */
::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: #4284f3;
}

/* 網頁捲軸【滑過時】把手的顏色 */
::-webkit-scrollbar-thumb:hover {
    background: #32b3e2;
}

.alert-column {
    display: flex;

    .alert-div {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 4px;
        max-height: 100%;
        margin: 10px;
    }

    .alert-title {
        font-weight: bold;
        margin: 0.5em;
        font-size: 24px;
        height: 40px;
    }

    .alert-content {
        display: flex;
        flex-direction: column;
        padding-right: 10px;
        overflow-y: auto;
    }

    .alert-text {
        margin-bottom: 0.5em;
        padding: 10px;
        font-size: 20px;
    }
}

@mixin theme(
    $grid-list-background,
    $grid-tile-board-color,
    $tile-box-shadow,
    $header-background,
    $span-color,
    $a-color,
    $mat-icon-color,
    $alert-color,
    $icon-color
) {
    mat-grid-list {
        background-color: $grid-list-background;
    }

    mat-grid-tile {
        border: 1px solid $grid-tile-board-color;

        @if $tile-box-shadow {
            box-shadow: $tile-box-shadow;
        }
    }

    .header {
        background-image: $header-background;
    }

    span {
        color: $span-color;
    }

    a {
        color: $a-color;
    }

    mat-icon {
        color: $mat-icon-color;
    }

    .alert-column {
        color: $alert-color;
    }

    .icon-color {
        filter: $icon-color;
    }
}

@mixin card-theme($board-color) {
    .card-main {
        border: solid 1px $board-color;
    }
}

.dark-theme {
    @include theme(
        "",
        #46fff2,
        null,
        linear-gradient(to top, #100c2a, #00554f),
        #ffff,
        #ffff,
        #46fff2,
        #ffff,
        invert(73%) sepia(90%) saturate(320%) hue-rotate(110deg) brightness(107%) contrast(104%)
    );
    @include card-theme(#46fff2);
}

.light-theme {
    @include theme(
        #ffff,
        #c8c8c8,
        2px 2px 15px 0px #c8c8c8,
        "",
        #100c2a,
        #100c2a,
        #00b0f0,
        #100c2a,
        invert(57%) sepia(77%) saturate(2660%) hue-rotate(159deg) brightness(95%) contrast(102%)
    );
    @include card-theme(#c8c8c8);
}

.center-theme {
    @include theme(
        #f5f5f5,
        #c8c8c8,
        2px 2px 15px 0px #c8c8c8,
        linear-gradient(to top, #edebf8, #edebf8),
        #703da9,
        #703da9,
        #703da9,
        #703da9,
        invert(20%) sepia(58%) saturate(3057%) hue-rotate(258deg) brightness(84%) contrast(100%)
    );
    @include card-theme(#703da9);
}

.error-msg {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bolder;
    height: 90%;
    font-size: 25px;
}

.card-main {
    border: 1px solid;
    display: flex;
    flex-direction: column;
    border-radius: 5px;
    align-items: center;
    justify-content: center;
    margin: 10px;
    // 使用共用樣式變數來避免重複
    & > .card-title,
    & > .card-value,
    & > .card-unit {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
    }
    .card-title {
        font-size: 1.25em;
    }

    .card-value {
        font-size: 1.5em;
    }

    .card-unit {
        font-size: 1.25em;
    }
}

.card-container {
    display: flex;
    flex-direction: row;
    min-height: 80%;
    flex-wrap: wrap;
    justify-content: start;
}

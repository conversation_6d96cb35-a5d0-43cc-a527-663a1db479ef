<div style="display: flex;justify-content: center;width: 100%;">
  <span class="white">
    <!-- <h1>內容編輯</h1> -->
    <div class="inner-contents">
      <div class="tab-group">
        @for(tab of postTypeTabs; track tab){
        @if(!isENewsletter || (isENewsletter && tab.type !== 'pdf' && tab.type !== 'html'&& tab.type !== 'address')){
        <div class="tab-ctn">
          <button mat-flat-button (click)="addEditorBlock(tab.type)">
            <mat-icon>{{ tab.iconName }}</mat-icon>
            {{ tab.tabName }}
          </button>
        </div>
        }
        }
      </div>
      <div class="editor-group">
        <form [formGroup]="form">
          @for (editorBlock of editorArr; let i = $index; track editorBlock;) {
          @switch (editorBlock.type) {
          @case ('title') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">小標題</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <input class="input" type="text" [formControlName]="editorBlock.id" />
          </span>
          }
          @case ('paragraphTitle') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">段落標題</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions;  context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <input class="input" type="text" [formControlName]="editorBlock.id" />
          </span>
          }
          @case ('html') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">HTML</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions;  context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <textarea [formControlName]="editorBlock.id">
                </textarea>
          </span>
          }
          @case ('photo') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">圖片區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>&nbsp;※&nbsp;只能上傳jpg、png檔案</p>
            <div>
              <button mat-flat-button class="btn" (click)="selectGalleryImage(editorBlock.id)">
                選擇圖片
              </button>
            </div>
            @if(editorBlock.data){
            <img [src]="getFileUrl(editorBlock)" alt="" width="100px">
            }
          </span>
          }
          @case ('photoList') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">相簿區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>&nbsp;※&nbsp;只能上傳jpg、png檔案</p>
            <div>
              <button mat-flat-button class="btn" (click)="selectMultipleGalleryImage(editorBlock.id)">
                選擇圖片
              </button>
            </div>
            @if(editorBlock.data){
            <div style="display: flex;flex-wrap: wrap;">
              @for (item of getFileUrlList(editorBlock);let i = $index ;track $index) {
              <img [src]="item.fileUrl" alt="" width="100px" (click)="removeImageData(editorBlock.id, i)">&nbsp;&nbsp;
              }
            </div>
            }

          </span>
          }
          @case ('fileData') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">檔案區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>
              &nbsp;※&nbsp;只能上傳word﹑excel﹑odp﹑odt﹑ods或pdf檔案，檔案大小不超過30MB選擇檔案
            </p>
            <div style="display: flex;flex-wrap: wrap;margin-bottom: 10px;">
              <div>
                <button mat-flat-button class="btn" (click)="selectGalleryFile(editorBlock.id,'file')">
                  選擇檔案
                </button>&nbsp;&nbsp;&nbsp;
              </div>
              <div>
                <button mat-flat-button class="btn" (click)="inputLink(editorBlock.id)">
                  新增連結
                </button>&nbsp;&nbsp;&nbsp;
              </div>
            </div>
            @if(editorBlock.data){
            <div style="display: flex;flex-wrap: wrap;flex-direction:column;">
              @for (item of getFileDataList(editorBlock); let i = $index; track $index) {
              <div class="file">
                <span>{{item.fileName}}</span>
                <span><mat-icon (click)="removeFileData(editorBlock.id, i)">close</mat-icon></span>
              </div>&nbsp;&nbsp;
              }
            </div>
            }

          </span>
          }
          @case ('table') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">表格區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <div>
              <button mat-flat-button class="btn" (click)="selectGalleryFile(editorBlock.id,'table')">
                選擇檔案
              </button>&nbsp;&nbsp;&nbsp;
            </div>
            @if(editorBlock.data){
            <div style="display: flex;flex-wrap: wrap;">
              @for (item of getTableDataList(editorBlock); track $index) {
              <span class="file">{{item.fileName}}</span>&nbsp;&nbsp;
              }
            </div>
            }
          </span>
          }
          @case ('button') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">按鈕區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>標題</p>
            <input class="input" type="text" [formControlName]="editorBlock.id + '_title'" />
            <p>連結網址</p>
            <input class="input" type="text" [formControlName]="editorBlock.id + '_url'" />
          </span>
          }
          @case ('content') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">內文區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions;  context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <div id="mytextarea" [froalaEditor]="froalaOptions" placeholder="請輸入內容..."
              [formControlName]="editorBlock.id"></div>
          </span>
          }
          @case ('video') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">影片區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <div>
              <button mat-flat-button class="btn" (click)="createVideo(editorBlock.id)">
                選擇影片
              </button>
            </div>
            @if(editorBlock.data){
            @if(getFileType(editorBlock)==='Youtube'){
            <iframe width="560" height="315" [src]="sanitizeUrl(editorBlock)" title="YouTube video player"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
            }@else{
            <video [src]="getFileUrl(editorBlock)" controls width="560"></video>
            }
            }
          </span>
          }
          @case ('address') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">地圖區</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>地址</p>
            <input class="input" type="text" [formControlName]="editorBlock.id" />
          </span>
          }
          @case ('card') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">入口卡</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>卡片標題</p>
            <input class="input" type="text" [formControlName]="editorBlock.id+ '_cardName'" />
            <p>連結名稱</p>
            <input class="input" type="text" [formControlName]="editorBlock.id+ '_cardUrlName'" />
            <p>連結</p>
            <input class="input" type="text" [formControlName]="editorBlock.id+ '_cardUrl'" />
          </span>
          }
          @case ('pdf') {
          <span class="block">
            <div class="title-ctn">
              <span class="title">PDF預覽</span>
              <span>
                <ng-container *ngTemplateOutlet="blockActions; context: { index: i ,blockId:editorBlock.id}">
                </ng-container>
              </span>
            </div>
            <p>
              &nbsp;※&nbsp;只能上傳pdf檔案，檔案大小不超過30MB選擇檔案
            </p>
            <div>
              <button mat-flat-button class="btn" (click)="selectPdfFile(editorBlock.id)">
                選擇檔案
              </button>&nbsp;&nbsp;&nbsp;
            </div>
            @if(editorBlock.data){
            <div style="display: flex;flex-wrap: wrap;flex-direction:column;">
              <span class="file">
                {{getPdfFileName(editorBlock)}}
              </span>
            </div>
            }
          </span>
          }
          @default {
          }
          }
          }
        </form>
      </div>
    </div>
  </span>
  <!-- <app-loading [loading]="loading"></app-loading> -->
</div>
<ng-template #blockActions let-i="index" let-blockId="blockId">
  <mat-icon (click)="moveUp(i)">arrow_upward</mat-icon>
  <mat-icon (click)="moveDown(i)">arrow_downward_alt</mat-icon>
  <mat-icon (click)="deleteBlock(i,blockId)">close</mat-icon>
</ng-template>
// Scss Document
.promote-organization{
	&-layout{
		max-width: 1200px;
		width: 100%;
		display: grid;
		grid-template-columns: repeat(4, minmax(0, 1fr));
		gap: 1.5rem;
	}
	&-item{
		padding: 10px;
		a{
			display: flex;
			overflow: hidden;
			text-decoration: none;
			flex-direction: column;
			justify-content: flex-start;
			align-items: center;
		}
		&-img{
			max-width: 250px;
			width: 100%;
			overflow: hidden;
			aspect-ratio: 4 / 3;
			border-radius: 10px;
			img{
				object-fit: cover;
				aspect-ratio: 4 / 3;
				width: 100%;
			}
		}
		&-title{
			margin: 0;
			padding: 10px 0;
			font-size: 1.25em;
			font-weight: bold;
			color: #000;
		}
	}
}

@media (max-width: 690px) {
	.promote-organization{
	&-layout{
		grid-template-columns: repeat(2, minmax(0, 1fr));
		}
	}
}
@media (max-width: 430px) {
	.promote-organization{
	&-layout{
		grid-template-columns: repeat(1, minmax(0, 1fr));
		}
	}
}
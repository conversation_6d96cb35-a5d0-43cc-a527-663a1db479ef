import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-forget-pw',
  standalone: false,

  templateUrl: './forget-pw.component.html',
  styleUrl: './forget-pw.component.scss',
})
export class ForgetPwComponent implements OnInit {
  form: FormGroup;
  token: string = '';
  constructor(
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParam) => {
      this.token = queryParam.get('t') as string;
      this.checkToken();
    });
    this.form = this.formBuilder.group({
      hamaP: ['', Validators.required],
      checkP: ['', Validators.required],
    });
  }

  ngOnInit(): void {}

  checkToken() {
    this.httpClient
      .post(
        'api/Manage/UserData/CheckEmail',
        {},
        {
          headers: {
            Authorization: this.token,
          },
        }
      )
      .subscribe({
        next: () => {},
        error: () => {
          Swal.fire('', '該驗證信已失效', 'error').then(() => {
            this.router.navigate(['manage/home']);
          });
        },
      });
  }

  submit() {
    if (!this.form.valid) {
      Swal.fire('', '未填寫完整', 'error');
      return;
    }
    if (this.form.value.hamaP !== this.form.value.checkP) {
      Swal.fire('', '密碼兩個對不上!!!', 'error');
      return;
    } else {
      this.httpClient
        .post(
          'api/Manage/UserData/UpdateMima',
          {
            hamaP: this.form.value.hamaP,
          },
          {
            headers: {
              Authorization: this.token,
            },
          }
        )
        .subscribe({
          next: () => {
            Swal.fire('', '變更成功!', 'success').then(() => {
              this.router.navigate(['manage/home']);
            });
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('', err.error.message, 'error');
          },
        });
    }
  }
}

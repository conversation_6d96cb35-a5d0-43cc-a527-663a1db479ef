@switch (surveyItem.fieldType) {
@case (FieldType.Input) {
<input type="text">
}
@case (FieldType.Select) {
<mat-select disabled="true" class="select">
    @for ( item of fieldMeta; track item) {
    <mat-option [value]="item.value">{{ item.value }}</mat-option>
    }
</mat-select>
}
@case (FieldType.TextArea) {
<textarea disabled="true"></textarea>
}
@case (FieldType.Date) {
<input type="date" disabled="true" [value]="today()">
}
@case (FieldType.Radio) {
@for (item of fieldMeta;let index=$index ;track item) {
<mat-radio-button disabled="true" [value]="item.value">
    {{ item.value }}</mat-radio-button>
}
}
@case (FieldType.CheckBox) {
@for ( item of fieldMeta; track item) {
<mat-checkbox disabled="true" [value]="item.value">
    {{ item.value }}
</mat-checkbox>
}
}
@case (FieldType.Address) {
<input type="text">
}
@case (FieldType.Mail) {
<input type="text">
}
@case (FieldType.Phone) {
<input type="text">
}
@case (FieldType.IDnumber) {
<input type="text">
}
@case (FieldType.ImageCheckBox) {
<div class="image-option-block-group">
    @for ( item of fieldMeta; track item) {
    <div class="image-option-block">
        <img [src]="getFileUrl(item)" alt="">
        <span> {{ item.value }}</span>
        <mat-checkbox disabled="true" [value]="item.value">
        </mat-checkbox>
    </div>
    }
</div>
}
@case (FieldType.File) {
<button button mat-flat-button color="primary" (click)="$event.stopPropagation()">上傳檔案</button>
@if(surveyItem.fileSize){
<span>檔案大小限制: {{surveyItem.fileSize}}MB</span>
}
}
}
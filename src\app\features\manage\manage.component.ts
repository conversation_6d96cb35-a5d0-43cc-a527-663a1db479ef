import { Component, ElementRef, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { ManageService } from '../../core/services/manage.service';
import { UserDataService } from '../../core/services/userData.service';
import { MenuItemService } from '../../core/services/menuItem.service';
import { WebSiteService } from '../../core/services/website.service';
import { MenuItem } from '../../shared/models/menuItem.model';
import { Navigation } from '../../shared/models/navigation.model';
import { UserData } from '../../shared/models/userData.model';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { AuthComponent } from '../components/dialogs/auth/auth.component';
import { DialogComponent } from '../../shared/components/dialog/dialog.component';
import { MenuSettingComponent } from '../components/menu-setting/menu-setting.component';
import { NavigationEnd, Router } from '@angular/router';
import { RoleSettingComponent } from '../components/role-setting/role-setting.component';
import { UserSettingComponent } from '../components/user-setting/user-setting.component';
import { FileBoxComponent } from '../components/file-box/file-box.component';
import { CreateUserComponent } from '../components/create-user/create-user.component';
import { GroupSettingComponent } from '../components/group-setting/group-setting.component';
import { ShareService } from '../../core/services/share.service';
import { AchievementsSettingComponent } from '../components/achievements-setting/achievements-setting.component';
import { SmtpSettingComponent } from '../components/smtp-setting/smtp-setting.component';

export enum Lang {
  zh = 'zh',
  en = 'en',
}

@Component({
  selector: 'app-manage',
  standalone: false,

  templateUrl: './manage.component.html',
  styleUrl: './manage.component.scss',
})
export class ManageComponent {
  contentLoading = false;
  isLogin = false;
  isManager = false;
  menuItem: MenuItem[] = [];
  navigation: Navigation[] = [];
  style: any;
  siteMap = [];
  userData: UserData | null = null;
  loading = false;
  $sub!: Subscription;
  $event!: Subscription;

  /** 是否為系統管理員 */
  isAdmin: boolean = false;
  /** 前台網址 */
  forestagePageUrl = sessionStorage.getItem('forestagePageUrl');
  /** 主控網址 */
  websitename = sessionStorage.getItem('webSiteName')!;
  isBanner = true;

  @ViewChild('top') top!: ElementRef;

  lang: string = sessionStorage.getItem('lang')
    ? (sessionStorage.getItem('lang') as string)
    : Lang.zh;

  LangText: { [key: string]: string } = {
    [Lang.zh]: '中文',
    [Lang.en]: '英文',
  };

  constructor(
    private _manageService: ManageService,
    private _userDataService: UserDataService,
    private _menuItemService: MenuItemService,
    private _webSiteService: WebSiteService,
    public dialog: MatDialog,
    private _router: Router,
    private shareService: ShareService
  ) {}

  ngOnInit() {
    if (sessionStorage.getItem('token')) {
      this.hasBanner();
      this.getMenuItem();
      this.getUser();
      this.checkManager();
    }

    this.$sub = this._manageService.homePageChange.subscribe((res) => {
      if (res) {
        this.getMenuItem();
      }
    });
  }

  hasBanner() {
    this.$event = this._router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const url = `/${location.pathname.split('/')[1]}/admin/manage/home`;
        this.isBanner =
          location.pathname === url || location.pathname === '/manage/home';
      }
    });
  }

  login() {
    // 登入
    const dialogRef = this.dialog.open(DialogComponent, {
      data: {
        width: '400px',
        title: '登入',
        showHeader: true,
        contentTemplate: AuthComponent,
      },
    });

    dialogRef.afterClosed().subscribe((res) => {
      this.isLogin = true;
      this.getUser();
      this.checkManager();
      this.getFunctionPolicy();
      this.getMenuPolicy();
    });
    (document.querySelector('app-home') as any).inert = false;
  }

  logout() {
    sessionStorage.removeItem('token');
    window.location.href = "manage/home"; // 直接重新載入
  }

  checkManager() {
    this.contentLoading = true;
    this._userDataService
      .isManager(sessionStorage.getItem('webSiteId')!)
      .subscribe((manager) => {
        this.isManager = manager;
        this.contentLoading = false;

        if (manager) {
          sessionStorage.setItem('isAdmin', 'true');
        } else {
          sessionStorage.setItem('isAdmin', 'false');
        }
      });
  }

  getMenuItem() {
    this.loading = true;
    this._menuItemService
      .getMenu(sessionStorage.getItem('webSiteId')!)
      .subscribe((tree: MenuItem[]) => {
        this.menuItem = tree;

        this._webSiteService
          .getWebSiteStyle(sessionStorage.getItem('webSiteId')!)
          .subscribe((style: string) => {
            this.style = JSON.parse(style);
            this.loading = false;

            this._webSiteService
              .listNavigation(sessionStorage.getItem('webSiteId')!)
              .subscribe((res: Navigation[]) => {
                this.navigation = res;
              });
          });
      });
  }

  /** 取得使用者 */
  getUser() {
    this._userDataService.get2().subscribe((x: any) => {
      this.userData = x;
      sessionStorage.setItem('manageId', x.id);
      sessionStorage.setItem('manageName', x.name);
      if (x.webSiteId != null) {
        if (
          x.birthday + 90 * 86400 <= new Date().getTime() / 1000 &&
          x.birthday != 0
        ) {
          Swal.fire(
            '密碼過期',
            '密碼已經超過三個月未更換，請更換密碼',
            'error'
          );
        }
      }
    });
  }

  /** 打開選單管理 */
  openMenuSettingPopup() {
    const dialogRef = this.dialog.open(DialogComponent, {
      data: {
        width: '80%',
        height: '500px',
        showHeader: false,
        contentTemplate: MenuSettingComponent,
      },
    });

    dialogRef.afterClosed().subscribe((res) => {
      this._manageService.homePageChange.next(res);
      this._manageService.contentChange.next(res);
      this.getMenuItem();
    });
  }

  goTop() {
    this.top.nativeElement.scrollIntoView({
      block: 'center',
      behavior: 'smooth',
    });
  }

  /** 取得目前的角色功能權限列表 */
  getFunctionPolicy() {
    this._userDataService.getUserFunctionPolicy().subscribe((res: any) => {
      sessionStorage.setItem('functionPolicy', res);
    });
  }

  /** 取得目前的角色選單權限列表，如為null則表示為系統管理員 */
  getMenuPolicy() {
    this._userDataService.getUserMenuPolicy().subscribe((res: any) => {
      sessionStorage.setItem('menuPolicy', res);
    });
  }

  getBg() {
    if (this.style) {
      return this.style.coverUrl
        ? {
            'background-color': this.style.backColor,
            'background-image': `url(${this.style.coverUrl})`,
            'background-size': '100vw 100vh',
            'background-position': 'top',
            'background-repeat': 'no-repeat',
            'background-attachment': 'fixed',
          }
        : {
            'background-color': this.style.backColor,
            'background-size': '100vw 100vh',
            'background-position': 'top',
            'background-repeat': 'no-repeat',
            'background-attachment': 'fixed',
          };
    } else {
      return;
    }
  }

  /** 打開角色權限設置 */
  openRole() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '500px',
        height: '700px',
        title: '角色權限設置',
        showHeader: true,
        contentTemplate: RoleSettingComponent,
      },
    });
  }

  /** 打開使用者設置 */
  openUser() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '500px',
        height: '700px',
        contentTemplate: UserSettingComponent,
        showHeader: true,
        title: '使用者設置',
      },
    });
  }

  openGroup() {
    // this.dialog.open(DialogComponent, {
    //   data: {
    //     width: '500px',
    //     height: '700px',
    //     contentTemplate: GroupSettingComponent,
    //     showHeader: true,
    //     title: '使用者群組設置',
    //   },
    // });
    this.dialog.open(GroupSettingComponent, {
      data: {
        width: '500px',
        height: '700px',
        showHeader: true,
        title: '使用者群組設置',
      },
    });
  }

  editUserData(data: any) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '800px',
        showHeader: true,
        title: '編輯資料',
        contentTemplate: CreateUserComponent,
        data: data,
        personalEditor: true,
      },
    });
  }

  /** 判斷目前登入使用者是否為系統管理員 */
  getIsAdmin() {
    this._userDataService
      .isAdministratorOrSeniorManager()
      .subscribe((res: boolean) => {
        this.isAdmin = res;
        if (res) {
          sessionStorage.setItem('isAdmin', 'true');
        }
      });
  }

  /** 跳轉頁面 */
  goUrl(url: string) {
    location.href = url;
  }

  /** 跳轉頁面 */
  goForestUrl() {
    location.href = this.forestagePageUrl!;
  }

  /** 檔案庫 */
  openFileBox() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '500px',
        contentTemplate: FileBoxComponent,
        data: {
          isOnlyView: true,
        },
      },
    });
  }

  getPolicy(policy: string) {
    return this.shareService.getPolicy(policy);
  }

  ngOnDestroy() {
    this.$sub.unsubscribe();
    this.$event.unsubscribe();
  }

  changeLanguage() {
    Swal.fire({
      title: `請問確定要變更為${
        this.LangText[this.lang === Lang.zh ? Lang.en : Lang.zh]
      }?`,
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.lang = this.lang === Lang.zh ? Lang.en : Lang.zh;
        sessionStorage.setItem('lang', this.lang);
        location.reload();
      }
    });
  }

  review() {
    this._router.navigate(['/manage/reviewList']);
  }

  textCloudSetting() {
    this._router.navigate(['/manage/textCloud']);
  }

  achievementsSetting() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        title: '推動成果設定',
        contentTemplate: AchievementsSettingComponent,
      },
    });
  }

  smtpSetting() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        title: 'SMTP設置',
        contentTemplate: SmtpSettingComponent,
      },
    });
  }
}

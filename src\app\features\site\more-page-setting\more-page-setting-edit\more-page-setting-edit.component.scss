.popup-container {
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
  }
  
  .section {
    margin-bottom: 20px;
    width: 100%;
    background-color: #cccccc33;
    padding: 10px;
    
    .privacy{
      display: flex;
      align-items: flex-start;
      flex-direction: column;
  
      div{
        margin-top: 10px;
      }
  
    }
  }
  

  .section div{
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
  }



  .section h3 {
    margin-top: 0;
  }
  
  label {
    display: block;
    font-weight: bold;
  }

  .block{
    display: flex;
    flex-direction: column;
  }

  .title{
    display: block;
    font-weight: bold;
  }
  
  input[type="text"] {
    width: 98%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom:10px ;
  }
  
  input[type="checkbox"] {
    margin-right: 5px;
  }
  
  .button-container {
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .cancel-btn, .save-btn,.create-category-btn , .image-btn{
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .cancel-btn {
    float: left;
    color: #fff;
    background-color: #2eaddb;
  }
  
  .save-btn {
    float: right;
    color: #fff;
    background-color: #2eaddb;
  }

  .select-image {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .preview {
        width: 200px;
        height: 100px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin: 10px 0;
        border: 4px solid #ccc;
    }
  }
  .create-category-btn {
    float: right;
    color: #fff;
    background-color: #2eaddb;
  }

  .image-btn {
    color: #fff;
    background-color: #2eaddb;
  }
  
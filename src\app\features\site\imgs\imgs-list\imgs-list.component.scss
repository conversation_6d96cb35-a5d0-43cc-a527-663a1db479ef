.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;

    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;

        h1 {
            color: #2eaddb;
        }

        .title-description {
            max-width: 50%;
        }

        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;

            .block {
                min-height: 150px;
                display: flex;
                border-bottom: 1px solid #ccc;
                cursor: pointer;
                transition: 0.3s ease-in-out;
                padding: 1em 0;

                &:hover {
                    box-shadow: 0px 0px 9px 0px #ccc;
                }

                &.add {
                    justify-content: center;
                    align-items: center;
                    background-color: #b8d3f3;
                }

                .titles {
                    min-width: 250px;
                    display: flex;
                    flex-direction: column;
                    padding: 10px;

                    img {
                        width: 250px;
                    }

                    h4 {
                        line-height: 1.5;
                    }
                }

                .cont {
                    line-height: 2;
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .title {
                        font-size: 25px;
                        font-weight: bold;
                    }

                    .time {
                        color: #a57868;
                        font-style: italic;
                    }
                }
            }
        }

        .add-layout {
            width: 90%;
            max-width: 1024px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
    }
}

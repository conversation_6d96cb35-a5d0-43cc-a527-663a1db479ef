<div class="popup-layout">
    <div class="popup-block" [ngStyle]="{ width: data.width, height: data.height }">
        <span class="popup-close">
            <label style="color: rgb(0, 0, 0)">群組設置</label>
            <span class="close-btn">
                <mat-icon aria-hidden="false" aria-label="close icon" fontIcon="close" mat-dialog-close
                    (click)="close()"></mat-icon>
            </span>
        </span>
        <div class="popup-content">
            <div class="Setting-layout">
                <div class="new-btns">
                    <span class="btns">
                        <button mat-flat-button (click)="createGroup()">
                            <i class="material-icons">add</i>建立
                        </button>
                    </span>
                </div>
                <span class="role-list">
                    <span class="role-block" *ngFor="let item of groupList">
                        <span class="role-name" (click)="getMember(item)">
                            {{ item.name }}
                        </span>
                        <span class="group-user" *ngIf="item.userShow">
                            <div class="group-btn">
                                <button mat-flat-button (click)="selectUser(item)">使用者設置</button>
                                <button mat-flat-button class="danger" (click)="deleteGroup(item)">刪除群組</button>
                            </div>
                            <ng-container *ngIf="userList.length; else noUser">
                                <span class="text-name" *ngFor="let user of userList">
                                    {{ user.name }}
                                </span>
                            </ng-container>
                            <ng-template #noUser>
                                <span class="text_c">暫無使用者</span>
                            </ng-template>
                            <span class="zoomout">
                                <i class="material-icons" (click)="closeUser(item)">expand_less</i>
                            </span>
                            <app-loading [loading]="userLoading"></app-loading>
                        </span>
                    </span>
                </span>
                <app-loading [loading]="loading"></app-loading>
            </div>


        </div>
    </div>
</div>
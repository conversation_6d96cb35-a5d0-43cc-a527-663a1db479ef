import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import {
  getFaqResp,
  getFaqListReq,
  getFaqListResp,
  createUpdateFaqReq,
} from '../../interface/faq.interface';
import { createUpdateResp, defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class FaqService {
  constructor(private httpClient: HttpClient) {}

  getFaq(id: string): Observable<getFaqResp> {
    return this.httpClient.get<getFaqResp>('api/Manage/Faq/GetFAQData', {
      params: {
        typeGroupId: id,
      },
    });
  }

  getFaqList(req: getFaqListReq): Observable<getFaqListResp> {
    return this.httpClient.get<getFaqListResp>('api/Manage/Faq/GetFAQList', {
      params: {
        ...req,
        lang: sessionStorage.getItem('lang') || 'zh',
      },
    });
  }

  deleteFaq(id: string) {
    return this.httpClient.delete('api/Manage/Faq/DeleteFAQ', {
      params: {
        typeGroupId: id,
      },
    });
  }

  createUpdateFaq(req: createUpdateFaqReq): Observable<createUpdateResp> {
    return this.httpClient.post<createUpdateResp>(
      'api/Manage/Faq/AddOrUpdateNew_FaqData',
      req
    );
  }
}

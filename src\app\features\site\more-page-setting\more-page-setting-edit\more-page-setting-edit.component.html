<div class="popup-container">
    <!-- 分類模組設定 -->
    <section class="section">
      <h3>分類標題設定</h3>
      <label>語系選擇：</label>
      <div>
        <div *ngFor="let item of category">
          <input
            type="checkbox"
            [id]="item.alias"
            [checked]="item.ischeck"
            (change)="toggleCategory(item.alias)"
          />
          <label [for]="item.alias">{{ item.checkName }}</label>
        </div>
      </div>
  
      <!-- 動態顯示分類標題 -->
      <div *ngFor="let item of category">
        <div *ngIf="item.ischeck">
          <label [for]="item.alias">{{ item.name }}</label>
          <input
            type="text"
            [id]="item.alias"
            [(ngModel)]="item.categoryName"
            [placeholder]="item.lang"
          />
        </div>
      </div>
  
      <span class="block">
        <span class="title">分類標題圖示</span>
        <span class="select-image">
          <button class="image-btn" (click)="selectImage()">請選擇圖片</button>
          <span
            *ngIf="AppCategoryRequest?.imageUrl"
            class="preview"
            [ngStyle]="{
              'background-image': 'url(' + AppCategoryRequest.imageUrl + ')'
            }"
          ></span>
        </span>
      </span>
  
      <label>權限設定：</label>
      <div>
        <div *ngFor="let option of permissionOptions">
          <input
            type="checkbox"
            [id]="option.code"
            [(ngModel)]="option.isSelected"
          />
          <label [for]="option.code">{{ option.name }}</label>
        </div>
      </div>
    </section>
  
    <div class="button-container">
      <button class="create-category-btn" (click)="addLink()">新增連結</button>
    </div>
  
    <!-- 動態顯示每個新增的連結設定 -->
    <section class="section" *ngFor="let link of linkSettings; let i = index">
      <h4>連結設定 {{ i + 1 }}</h4>
  
      <!-- 語系選擇 -->
      <div>
        <label>語系選擇：</label>
        <div *ngFor="let lang of link.lang; let j = index">
          <input
            type="checkbox"
            [id]="lang.alias + '-link-' + j"
            [(ngModel)]="lang.ischeck"
          />
          <label [for]="lang.alias + '-link-' + j">{{
            lang.checkName | uppercase
          }}</label>
        </div>
      </div>
  
      <!-- 連結名稱和網址 -->
      <div *ngFor="let lang of link.lang">
        <div *ngIf="lang.ischeck">
          <label [for]="lang.alias + '-name-' + i"
            >{{ lang.checkName }} 連結名稱：</label
          >
          <input
            type="text"
            [id]="lang.alias + '-name-' + i"
            [(ngModel)]="lang.linkName"
            placeholder="請輸入連結名稱"
          />
  
          <label [for]="lang.alias + '-url-' + i"
            >{{ lang.checkName }} 連結路徑：</label
          >
          <input
            type="text"
            [id]="lang.alias + '-url-' + i"
            [(ngModel)]="lang.linkUrl"
            placeholder="https://example.com"
          />
  
          <div class="privacy"> 
            <!-- 是否需要個資宣告的勾選框 -->
            <label [for]="lang.alias + '-privacy-checkbox-' + i">
              <input
                type="checkbox"
                [id]="lang.alias + '-privacy-checkbox-' + i"
                [(ngModel)]="lang.isPrivacyChecked"
              />
              是否需要個資宣告
            </label>
  
            <!-- 個資說明輸入欄位 -->
            <div *ngIf="lang.isPrivacyChecked">
              <label [for]="lang.alias + '-privacy-description-' + i"
                >{{ lang.checkName }} 個資說明：</label
              >
              <textarea
                [id]="lang.alias + '-privacy-description-' + i"
                [(ngModel)]="lang.privacyDescription"
                placeholder="請輸入個資說明"
              ></textarea>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 權限設定 -->
      <label>權限設定：</label>
      <div>
        <div *ngFor="let option of permissionOptions">
          <input
            type="checkbox"
            [id]="option.code + '-' + i"
            [(ngModel)]="link.permissions[option.code]"
          />
          <label [for]="option.code + '-' + i">{{ option.name }}</label>
        </div>
      </div>
    </section>
  
    <!-- 按鈕區域 -->
    <div class="button-container">
      <button class="save-btn" (click)="saveCategorySetting()">保存</button>
    </div>
  </div>
  
import { EChartsOption } from 'echarts';
import { new_NewsTagDataItem } from './news.interface';

export interface defaultItem {
  code: number;
  message: string;
}

export interface getTextCloudResp extends defaultItem {
  data: {
    data: any[];
  };
}

export interface getTypeListResp extends defaultItem {
  data: {
    typeValue: string;
    typeName: string;
  }[];
}

export interface createUpdateContentReq {
  menuitemId: string;
  titleName: string;
  levelDecision: number;
  lang: string;
  keyword: string;
  new_NewsTagDatas: {
    tagName: string;
    dataString: string | null;
  }[];
}

export interface createUpdateResp extends defaultItem {
  data: string;
}

export interface getContentResp extends defaultItem {
  data: {
    menuitemId: string;
    new_NewsId: string;
    typeGroupId: string;
    titleName: string;
    keyword: string;
    creater: string;
    editor: string;
    isPendingApproval: boolean;
    lang: string;
    levelDecision: number;
    new_NewsTagDatas: new_NewsTagDataItem[];
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface setSortReq {
  typeGroupId: string
  menuitemId: string;
  sort: number
}

export interface changeSortReq {
  id: string;
  down: boolean;
  type: string;
  hasParent?: boolean;
}

export interface getAchievementsResp extends defaultItem {
  data: {
    dictionaryBrowseCount: number;
    forumEventCount: number;
    klokahReachCount: number;
    instructionalKit: number;
  };
}

export interface getClickLogListReq {
  webSiteId: string;
  currentPage: number;
  pageSize: number;
}

export interface getClickLogListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: clickLogItem[];
  };
}

export interface getClickLogChartResp extends defaultItem {
  data: string;
}

export interface clickLogItem {
  url: string;
  name: string;
  count: number;
}

export interface getGustViewLogListReq {
  webSiteId: string;
  dateStart: string;
  dateEnd: string;
  currentPage: number;
  pageSize: number;
}

export interface getGustViewLogListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    data: guestViewLogItem[];
  };
}

export interface getGustViewLogChartReq {
  startTime: string;
  endTime: string;
}

export interface getGustViewLogChartResp extends defaultItem {
  data: string;
}

export interface guestViewLogItem {
  operatorIP: string;
  date: string;
  devicePlatform: string;
}

export interface getSearchKeywordLogListReq {
  webSiteId: string;
  column: string;
  isAsc: boolean;
  lang: string;
  currentPage: number;
  pageSize: number;
}

export interface getSearchKeywordLogListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    data: searchKeywordLogItem[];
  };
}

export interface getSearchKeywordLogChartResp extends defaultItem {
  data: string;
}

export interface searchKeywordLogItem {
  keyWord: string;
  userCount: number;
  usageCount: number;
  lang: string;
}

export interface getAuditLogListReq {
  startTime: string;
  endTime: string;
  operatorAccount?: string;
  operatorIp?: string;
  description?: string;
  type?: string;
  currentPage: number;
  pageSize: number;
}

export interface getAuditLogListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    data: auditLogItem[];
  };
}

export interface getAuditLogChartReq {
  startTime: string;
  endTime: string;
}

export interface getAuditLogChartResp extends defaultItem {
  data: string;
}

export interface auditLogItem {
  id: string;
  operatorAccount: string;
  operatorName: string;
  operatorIp: string;
  devicePlatform: string;
  type: string;
  path: string;
  description: string;
  creationTime: string;
}

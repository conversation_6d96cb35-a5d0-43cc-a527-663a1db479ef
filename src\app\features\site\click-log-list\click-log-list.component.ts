import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  clickLogItem,
  getClickLogChartResp,
  getClickLogListReq,
  getClickLogListResp,
} from '../../../interface/share.interface';
import { EChartsOption } from 'echarts';

@Component({
  selector: 'app-click-log-list',
  standalone: false,

  templateUrl: './click-log-list.component.html',
  styleUrl: './click-log-list.component.scss',
})
export class ClickLogListComponent {
  loading: boolean = false;
  menuItemId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  clickLogList: clickLogItem[] = [];
  chartOption: EChartsOption = {};

  isTable: boolean = true;

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) { }

  ngOnInit(): void {
    this.getClickLogList();
    this.getClickLogChart();
  }

  searchlist() {
    this.nowPage = 1;
    this.getClickLogList();
    this.getClickLogChart();
  }

  getClickLogList() {
    let req: getClickLogListReq = {
      webSiteId: sessionStorage.getItem('webSiteId')!,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareService.getClickLogList(req).subscribe({
      next: (resp: getClickLogListResp) => {
        this.loading = false;
        this.clickLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }


  getClickLogChart() {
    this.loading = true;
    this.shareService.getClickLogChart().subscribe({
      next: (resp: getClickLogChartResp) => {
        this.loading = false;
        this.chartOption = this.getOptions(resp.data);
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  getOptions(options: string): EChartsOption {
    return options ? (JSON.parse(options) as EChartsOption) : {};
  }


  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getClickLogList();
  }
}

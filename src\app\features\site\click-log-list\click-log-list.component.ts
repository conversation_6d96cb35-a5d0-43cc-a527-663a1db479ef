import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  clickLogItem,
  getClickLogListReq,
  getClickLogListResp,
} from '../../../interface/share.interface';

@Component({
  selector: 'app-click-log-list',
  standalone: false,

  templateUrl: './click-log-list.component.html',
  styleUrl: './click-log-list.component.scss',
})
export class ClickLogListComponent {
  loading: boolean = false;
  menuItemId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  clickLogList: clickLogItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) {}

  ngOnInit(): void {
    this.getClickLogList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getClickLogList();
  }

  getClickLogList() {
    let req: getClickLogListReq = {
      webSiteId: sessionStorage.getItem('webSiteId')!,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareService.getClickLogList(req).subscribe({
      next: (resp: getClickLogListResp) => {
        this.loading = false;
        this.clickLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getClickLogList();
  }
}

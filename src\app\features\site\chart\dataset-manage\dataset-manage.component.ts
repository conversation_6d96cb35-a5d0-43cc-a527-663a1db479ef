import { Component } from '@angular/core';
import Swal from 'sweetalert2';
import { ShareService } from '../../../../core/services/share.service';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ChartService } from '../../../../core/services/chart.service';
import {
  datasetItem,
  getDatasetListResp,
} from '../../../../interface/chart.interface';
import { HttpErrorResponse } from '@angular/common/http';
import { defaultItem } from '../../../../interface/share.interface';

@Component({
  selector: 'app-dataset-manage',
  standalone: false,

  templateUrl: './dataset-manage.component.html',
  styleUrl: './dataset-manage.component.scss',
})
export class DatasetManageComponent {
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  datasetList: datasetItem[] = [];
  keyword: string = '';
  file: File | null = null;
  fileName: string = '';

  constructor(private chartService: ChartService) {}

  ngOnInit(): void {
    this.getDatasetList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getDatasetList();
  }

  getDatasetList() {
    let req = {
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.chartService.getDatasetList(req).subscribe({
      next: (resp: getDatasetListResp) => {
        this.loading = false;
        this.datasetList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.nowPage = 1;
    this.getDatasetList();
  }

  uploadFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    const maxFileSize = 30 * 1024 * 1024;
    if (file.size > maxFileSize) {
      Swal.fire('檔案大小超過 30MB，請重新選擇', '', 'warning');
      this.loading = false;
      return;
    }
    if (
      file.type !==
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
      file.type !== 'text/csv'
    ) {
      Swal.fire('請選擇xlsx或csv', '', 'warning');
      this.loading = false;
      return;
    }
    this.file = file;
    this.fileName = file.name;
    this.chartService.uploadDataset(this.file).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        resp.code === 200
          ? Swal.fire('成功', '上傳成功', 'success').then(() => {
              this.getDatasetList();
            })
          : Swal.fire('失敗', resp.message, 'error');
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.chartService.deleteDataset(id).subscribe({
          next: (resp: defaultItem) => {
            this.loading = false;
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                  this.getDatasetList();
                })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getDatasetList();
  }

  donloadTemplate() {
    window.open('assets/files/圖表上傳範例.xlsx', '_blank');
  }

  back() {
    window.history.back();
  }
}

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Role } from '../../shared/models/role.model';
import { HttpClient } from '@angular/common/http';
import { EnumTypeInfo } from '../../shared/models/enumTypeInfo.model';

@Injectable({
  providedIn: 'root',
})
export class RoleService {
  private mngRoleApi: string = '/api/Manage/Role';

  constructor(private http: HttpClient) {}

  getRoleList(): Observable<Role[]> {
    return this.http.get<Role[]>(`api/Manage/Role/ddl`, {
      params: {
        webSiteId: sessionStorage.getItem('webSiteId') as string,
      },
    });
  }

  /**
   * 取得指定子網站的權限列表
   *
   * @param webSiteId 子網站唯一識別號
   * @param enable 啟用停用過濾
   */
  list(
    webSiteId?: string,

    enable?: boolean
  ): Observable<Role[]> {
    let url = '/api/Manage/Role';
    const queryList = [];

    if (webSiteId !== null && webSiteId !== undefined) {
      queryList.push('webSiteId=' + encodeURIComponent(webSiteId.toString()));
    }

    if (enable !== null && enable !== undefined) {
      queryList.push('enable=' + encodeURIComponent(enable.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<Role[]>(url);
  }

  /**
   * 取得指定角色功能權限列表
   *
   * @param id 角色唯一識別號
   */
  getRoleFunctionPolicyAll(id: string): Observable<string[]> {
    let url = '/api/Manage/Role/{id}/functionPolicy';

    return this.http.get<string[]>(`${this.mngRoleApi}/${id}/functionPolicy`);
  }

  /**
   * 取得可用的角色功能權限列表
   *
   */
  getRoleFunctionPolicy(): Observable<EnumTypeInfo[]> {
    return this.http.get<EnumTypeInfo[]>(`${this.mngRoleApi}/functionPolicy`);
  }

  /**
   * 更新實例
   *
   * @param instance 實例內容
   */
  update(instance: Role): Observable<Role> {
    return this.http.put<Role>(this.mngRoleApi, instance);
  }

  /**
   * 建立實例
   *
   * @param instance 實例內容
   */
  create(instance: Role): Observable<Role> {
    return this.http.post<Role>(this.mngRoleApi, instance);
  }

  /**
   * 加入指定角色功能權限
   *
   * @param id 角色唯一識別號
   * @param func 功能
   */
  addFunctionPolicy(id: string, func: string): Observable<string[]> {
    return this.http.post<string[]>(
      `${this.mngRoleApi}/${id}/functionPolicy/${func}`,
      {}
    );
  }

  /**
   * 刪除指定角色功能權限
   *
   * @param id 角色唯一識別號
   * @param func 功能
   */
  deleteFunctionPolicy(id: string, func: string): Observable<string[]> {
    return this.http.delete<string[]>(
      `${this.mngRoleApi}/${id}/functionPolicy/${func}`
    );
  }

  /**
   * 更新指定角色選單權限列表
   *
   * @param id 角色唯一識別號
   * @param menuItemIds 選單唯一識別號列表
   */
  updateRoleMenuPolicy(
    id: string,

    menuItemIds: string[]
  ): Observable<string[]> {
    let url = '/api/Manage/Role/{id}/menuPolicy';
    return this.http.put<string[]>(
      `${this.mngRoleApi}/${id}/menuPolicy`,
      menuItemIds
    );
  }

  /**
   * 更新指定角色功能權限列表
   *
   * @param id 角色唯一識別號
   * @param functions 功能列表
   */
  updateRoleFunctionPolicy(
    id: string,
    functions: string[]
  ): Observable<string[]> {
    return this.http.put<string[]>(
      `${this.mngRoleApi}/${id}/functionPolicy`,
      functions
    );
  }

  /**
   * 取得指定角色選單權限列表
   *
   * @param id 角色唯一識別號
   */
  getRoleMenuPolicy(id: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.mngRoleApi}/${id}/menuPolicy`);
  }

  /**
   * 刪除指定實例
   *
   * @param id 唯一識別號
   */
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${this.mngRoleApi}/${id}`);
  }

  /**
   * 加入指定角色選單權限
   *
   * @param id 角色唯一識別號
   * @param menuItemId 選單唯一識別號
   */
  addRoleMenuPolicy(
    id: string,

    menuItemId: string
  ): Observable<string[]> {
    let url = '/api/Manage/Role/{id}/menuPolicy/{menuItemId}';

    url = url.replace('{id}', id.toString());

    url = url.replace('{menuItemId}', menuItemId.toString());
    return this.http.post<string[]>(url, {});
  }

  /**
   * 刪除指定角色選單權限
   *
   * @param id 角色唯一識別號
   * @param menuItemId 選單唯一識別號
   */
  deleteRoleMenuPolicy(
    id: string,

    menuItemId: string
  ): Observable<string[]> {
    let url = '/api/Manage/Role/{id}/menuPolicy/{menuItemId}';

    url = url.replace('{id}', id.toString());

    url = url.replace('{menuItemId}', menuItemId.toString());
    return this.http.delete<string[]>(url);
  }
}

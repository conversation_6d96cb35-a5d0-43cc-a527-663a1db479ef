<div class="upload-box-layout">
    <div class="upload-box"
        (drop)="status===uploadStatus.MULTIPLE ? onFileMultipleDropped($event) : onFileDropped($event)"
        (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)">
        @if(fileList.length>0){
        <div class="file-list">
            @for (fileName of fileNameList; track fileName; let i = $index) {
            <div class="file-item">
                <span class="name" [title]="fileName">{{ fileName }}</span>
                <span class="file-remove" (click)="remove(i)">
                    <mat-icon class="dialog-close-btn">close</mat-icon>
                </span>
            </div>
            }
        </div>
        }
        <p>將檔案拖曳到這裡，或 <label for="fileUpload" class="upload-link">上傳檔案</label></p>
        @if(fileTypeList.length){
        <p>已支援檔案類型:
            @for (item of fileTypeList; track item;let last=$last) {
            {{item}}
            @if(!last){
            ,
            }
            }
        </p>
        }
        @if(status===uploadStatus.MULTIPLE){
        <input type="file" id="fileUpload" [accept]="fileAcceptList.join(', ')"
            (change)="onFileMultipleSelected($event)" hidden multiple />
        }@else{
        <input type="file" id="fileUpload" [accept]="fileAcceptList.join(', ')" (change)="onFileSelected($event)"
            hidden />
        }
    </div>
</div>
<div class="close-btn-layout">
    <button mat-stroked-button (click)="close()">取消</button>
    <button mat-flat-button color="primary" (click)="submit()">確認</button>
</div>
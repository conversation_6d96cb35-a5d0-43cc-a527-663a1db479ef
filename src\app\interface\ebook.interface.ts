import { defaultItem } from './share.interface';

export interface getEbookListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getEbookListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    data: ebookItem[];
  };
}

export interface ebookItem {
  typeGroupId: string;
  bookId: string;
  title: string;
  littleTile: string;
  littleTitleZh: string;
  period: string;
  coverUrl: string;
  status: string;
  sort: number;
  isTop: boolean;
}

export interface getEbookResp extends defaultItem {
  data: {
    id: string;
    menuItemId: string;
    title: string;
    author: string;
    introduction: string;
    outline: string;
    coverDataId: string;
    ebookDataId: string;
    sort: number;
    enable: true;
    onHomepage: false;
    defaultCoverDataId: string;
    startTime: string;
    endTime: string;
    littleTitle: string;
    littleTitleZh: string;
    period: string;
    isTop: boolean;
    lang: string;
    coverUrl: string;
    ebookUrl: string;
    previewUrl: string;
    creater: string;
    editor: string;
    isPendingApproval: boolean;
    levelDecision: number;
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface addEbookReq {
  menuItemId: string;
  title: string;
  author: string;
  introduction: string;
  outline: string;
  littleTitle: string;
  littleTitleZh: string;
  period: string;
  coverDataId: string;
  isTop: boolean;
  startTime: string;
  endTime: string;
  FromFile: File | null;
  levelDecision: number;
  lang: string;
}

export interface updateEbookReq extends addEbookReq {
  id: string;
  typeGroupId: string;
}

export interface createUpdateEbookResp extends defaultItem {
  data: {
    typeGroupId: string;
    bookId: string;
  };
}
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareService } from '../../../../core/services/share.service';
import { EbookService } from '../../../../core/services/ebook.service';
import {
  ebookItem,
  getEbookListReq,
  getEbookListResp,
} from '../../../../interface/ebook.interface';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../interface/share.interface';

@Component({
  selector: 'app-ebook-list',
  standalone: false,

  templateUrl: './ebook-list.component.html',
  styleUrl: './ebook-list.component.scss',
})
export class EbookListComponent implements OnInit {
  lang: string = '中文';
  loading: boolean = false;
  menuItemId: string = '';
  keyword: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  ebookList: ebookItem[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareService: ShareService,
    private ebookService: EbookService
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.activatedRoute.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId') as string;
      this.getEbookList();
    });
  }

  getEbookList() {
    let req: getEbookListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.ebookService.getEbookList(req).subscribe({
      next: (resp: getEbookListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.ebookList = resp.data.data;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  changePage($event: any) {
    this.nowPage = $event.pageIndex + 1;
    this.getEbookList();
  }

  search() {}
  
  addEbook() {
    this.router.navigate(['/manage/', this.menuItemId, 'ebook', 'edit']);
  }

  editEbook(id: string, typeGroupId: string) {
    console.log(id, typeGroupId);
    this.router.navigate(['/manage/', this.menuItemId, 'ebook', 'edit'], {
      queryParams: { id: id, typeGroupId: typeGroupId },
    });
  }

  deleteEbook(id: string, typeGroupId: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.ebookService.deleteEbook(id, typeGroupId).subscribe({
          next: (res: defaultItem) => {
            if (res.code === 200) {
              Swal.fire('成功', '刪除成功', 'success').then(() => {
                this.getEbookList();
              });
            } else {
              Swal.fire('失敗', '刪除失敗', 'error');
            }
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', '刪除失敗', 'error');
          },
        });
      }
    });
  }
}

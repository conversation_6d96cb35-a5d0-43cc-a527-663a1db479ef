import { Component, Inject, OnInit } from '@angular/core';
import { ENewsletterService } from '../../../core/services/e-newsletter.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  getViewENewsLetterColumnReq,
  getViewENewsLetterColumnResp,
  groupReportItem,
  viewENewsLetterColumnItem,
} from '../../../interface/eNewsletter.interface';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-view-enews-letter-column-dialog',
  standalone: false,

  templateUrl: './view-enews-letter-column-dialog.component.html',
  styleUrl: './view-enews-letter-column-dialog.component.scss',
})
export class ViewENewsLetterColumnDialogComponent implements OnInit {
  loading: boolean = false;
  typeGroupId: string = '';
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  eNewsLetterColumn: viewENewsLetterColumnItem[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: {
        typeGroupId: string;
      };
    },
    private dialogRef: MatDialogRef<ViewENewsLetterColumnDialogComponent>,
    private eNewsLetterService: ENewsletterService
  ) {}

  ngOnInit(): void {
    this.typeGroupId = this.data.data.typeGroupId;
    this.getGroupReportNewsList();
  }

  getGroupReportNewsList() {
    this.loading = true;
    let req: getViewENewsLetterColumnReq = {
      typeGroupId: this.typeGroupId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };

    this.eNewsLetterService.getViewENewsLetterColumn(req).subscribe({
      next: (resp: getViewENewsLetterColumnResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.eNewsLetterColumn = resp.data.data;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getGroupReportNewsList();
  }

  cancel() {
    this.dialogRef.close();
  }

  send() {
    this.dialogRef.close();
  }
}

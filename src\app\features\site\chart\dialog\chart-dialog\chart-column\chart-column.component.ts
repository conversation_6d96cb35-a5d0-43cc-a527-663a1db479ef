import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatRadioChange } from '@angular/material/radio';
import { MatSelectChange } from '@angular/material/select';
import { ChartService } from '../../../../../../core/services/chart.service';
import {
  blockItem,
  blockListItem,
  datasetItem,
  datasetYItem,
  getBlockResp,
  getDatasetListResp,
  getDatasetXListResp,
  getDatasetYListResp,
} from '../../../../../../interface/chart.interface';
import { HttpErrorResponse } from '@angular/common/http';

export interface chartTypeOptionInterface {
  value: string;
  viewValue: string;
}

export interface customerChartInterface {
  datasetId: string;
  xAxis: string;
  yAxis: string[];
  chartType: string;
}

@Component({
  selector: 'app-chart-column',
  standalone: false,

  templateUrl: './chart-column.component.html',
  styleUrl: './chart-column.component.scss',
})
export class ChartColumnComponent implements OnInit {
  customerChartForm: FormGroup;
  @Input() blockListItem!: blockListItem | null;
  @Input() menuitemId!: string;
  @Output() loadingStatus = new EventEmitter<boolean>();
  @Output() customerChartStatus = new EventEmitter<boolean>();
  @Output() customerChart = new EventEmitter<customerChartInterface>();
  @Output() formStatusChange = new EventEmitter<boolean>();

  xAxis: string = '';

  datasetList: datasetItem[] = [];
  datasetYItemList: datasetYItem[] = [];

  chartTypeOptionList: chartTypeOptionInterface[] = [
    { value: 'bar', viewValue: '長條圖' },
    { value: 'line', viewValue: '折線圖' },
    { value: 'pie', viewValue: '圓餅圖' },
  ];

  calculateColumnList: {
    columnId?: string;
    columnTitle: string;
  }[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private chartService: ChartService
  ) {
    this.customerChartForm = this.formBuilder.group({
      datasetId: [null, Validators.required],
      chartType: [null, Validators.required],
      xAxis: [null, Validators.required],
      yAxis: [null, Validators.required],
    });
  }

  ngOnInit(): void {
    this.getDatasetList();

    // 監聽表單的 statusChanges
    this.customerChartForm.statusChanges.subscribe((status) => {
      // 當表單狀態改變時，發送當前的 valid 狀態給父元件
      this.formStatusChange.emit(this.customerChartForm.valid);
    });
  }

  getDatasetList() {
    let req = {
      currentPage: 1,
      pageSize: -1,
    };
    this.chartService.getDatasetList(req).subscribe({
      next: (resp: getDatasetListResp) => {
        this.datasetList = resp.data.data;
        this.settingBlock();
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
        this.loadingStatus.emit(false);
      },
    });
  }

  settingBlock() {
    if (this.blockListItem) {
      this.chartService.getBlock(this.blockListItem.blockId).subscribe({
        next: (resp: getBlockResp) => {
          this.customerChartForm.patchValue({
            datasetId: resp.data.datasetId,
            chartType: resp.data.options,
            xAxis: resp.data.xAxis,
            yAxis: resp.data.datasetYId,
          });
          this.getDatasetYList(resp.data.datasetId);
          this.checkCustomerChart();
        },
        error: (err: HttpErrorResponse) => {
          console.error(err);
        },
      });
    }
  }

  selectChartType(event: MatSelectChange) {
    const selectValue = event.value;
    this.customerChartForm.get('yAxis')?.patchValue(null);
    this.calculateColumnList = [];
    this.checkCustomerChart();
  }

  selectDataset(event: MatSelectChange) {
    this.customerChartStatus.emit(false);
    this.loadingStatus.emit(false);
    this.customerChartForm.get('datasetId')?.patchValue(event.value);
    this.getDatasetXList(this.customerChartForm.value.datasetId);
    this.getDatasetYList(this.customerChartForm.value.datasetId);
    this.customerChartForm.get('xAxis')?.patchValue(null);
    this.customerChartForm.get('yAxis')?.patchValue(null);
    this.calculateColumnList = [];
  }

  getDatasetXList(datasetId: string) {
    this.chartService.getDatasetXList(datasetId).subscribe({
      next: (resp: getDatasetXListResp) => {
        this.customerChartForm.get('xAxis')?.patchValue(resp.data);
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
        this.loadingStatus.emit(false);
      },
    });
  }

  getDatasetYList(datasetId: string) {
    this.chartService.getDatasetYList(datasetId).subscribe({
      next: (resp: getDatasetYListResp) => {
        this.datasetYItemList = resp.data;
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
        this.loadingStatus.emit(false);
      },
    });
  }

  /**
   * 選擇API計算列
   * @param event
   */
  selectChartYAxis(event: MatSelectChange, status: string) {
    this.checkCustomerChart();
  }

  checkCustomerChart() {
    console.log(this.customerChartForm.value.datasetId);
    console.log(this.customerChartForm);
    if (
      this.customerChartForm.value.datasetId &&
      this.customerChartForm.value.xAxis &&
      this.customerChartForm.value.yAxis &&
      this.customerChartForm.value.chartType
    ) {
      this.customerChart.emit({
        datasetId: this.customerChartForm.value.datasetId,
        xAxis: this.customerChartForm.value.xAxis,
        yAxis: this.customerChartForm.value.yAxis,
        chartType: this.customerChartForm.value.chartType,
      });
    }
  }
}

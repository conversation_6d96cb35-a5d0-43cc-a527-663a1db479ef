import { Component, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { surveyItem } from '../../../../interface/survey.interface';
import { FieldType } from '../../../../enum/survey.enum';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../../../components/file-box/file-box.component';

@Component({
  selector: 'app-edit-question',
  standalone: false,

  templateUrl: './edit-question.component.html',
  styleUrl: './edit-question.component.scss',
})
export class EditQuestionComponent {
  form: FormGroup;
  type: string = '';
  FieldType = FieldType;
  optionList: { value: string; src?: string }[] = [];
  fieldTypeText: { [key: string]: string } = {
    [FieldType.Input]: '文字輸入框',
    [FieldType.Select]: '下拉選單',
    [FieldType.TextArea]: '文字區域',
    [FieldType.Date]: '日期選擇框',
    [FieldType.Radio]: '單選選項',
    [FieldType.CheckBox]: '多選選項',
    [FieldType.ImageCheckBox]: '圖片多選選項',
    [FieldType.Address]: '地址',
    [FieldType.Mail]: '信箱',
    [FieldType.Phone]: '手機號碼',
    [FieldType.IDnumber]: '身分證',
    [FieldType.File]: '檔案',
  };
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: surveyItem,
    private dialogRef: MatDialogRef<EditQuestionComponent>,
    private fb: FormBuilder,
    private matDialog: MatDialog
  ) {
    this.form = this.fb.group({
      name: [this.data.fieldName, Validators.required],
      required: [this.data.required, Validators.required],
    });

    this.type = this.data.fieldType;
    console.log(this.data.fieldMeta);
    if (
      this.data.fieldMeta &&
      (this.type === FieldType.Select ||
        this.type === FieldType.Radio ||
        this.type === FieldType.CheckBox ||
        this.type === FieldType.ImageCheckBox)
    ) {
      this.optionList = JSON.parse(this.data.fieldMeta as string);
    }

    if (this.type === FieldType.File) {
      const fileControl = this.fb.control(this.data.fileSize, Validators.required);
      this.form.addControl('fileSize', fileControl);
    }
  }

  get isMulit() {
    if (this.data) {
      const type = this.data.fieldType;
      return (
        type === FieldType.Select ||
        type === FieldType.Radio ||
        type === FieldType.CheckBox
      );
    } else {
      return false;
    }
  }

  get isImageCheckBox() {
    if (this.data) {
      const type = this.data.fieldType;
      return type === FieldType.ImageCheckBox;
    } else {
      return false;
    }
  }
  addOption() {
    this.optionList.push({
      value: '',
    });
  }

  selectGalleryImage(item: { value: string; src?: string }) {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          item.src = resp.data.url;
          // this.editorArr.find((item) => {
          //   return item.id === id;
          // })?.data;
          // const item = this.editorArr.find((item) => item.id === id);
          // if (item) {
          //   (item.data as photoItem) = { fileUrl: resp.data.previewImageUrl };
          // }
          // this.outputContentData();
        }
      });
  }

  input($event: Event, item: { value: string }) {
    let value = ($event.target as HTMLInputElement).value;
    item.value = value;
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.optionList, event.previousIndex, event.currentIndex);
  }

  deleteField($event: Event, index: number) {
    $event.stopPropagation();
    this.optionList.splice(index, 1);
  }

  submit() {
    let surveyItem: surveyItem = {
      fieldName: this.form.value.name,
      fieldType: this.type,
      required: this.form.value.required,
    };
    if (this.form.get('fileSize')) {
      surveyItem.fileSize = this.form.value.fileSize;
    }
    if (this.optionList && this.optionList.length > 0) {
      surveyItem.fieldMeta = JSON.stringify(this.optionList);
    }

    this.dialogRef.close(surveyItem);
  }

  close() {
    this.dialogRef.close();
  }
}

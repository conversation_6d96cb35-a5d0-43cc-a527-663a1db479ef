import { Component } from '@angular/core';
import Swal from 'sweetalert2';
import { ApprovalStatus, SAVESTATUS } from '../../../../enum/share.enum';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareService } from '../../../../core/services/share.service';
import { VideoService } from '../../../../core/services/video.service';
import {
  createUpdateVideoReq,
  getVideoResp,
} from '../../../../interface/video.interface';
import {
  createUpdateResp,
  defaultItem,
} from '../../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';
import { format } from 'date-fns';

@Component({
  selector: 'app-video-edit',
  standalone: false,

  templateUrl: './video-edit.component.html',
  styleUrl: './video-edit.component.scss',
})
export class VideoEditComponent {
  menuItemId: string = '';
  typeGroupId: string = '';
  loading = false;
  title: string = '';
  isTop: boolean = false;
  isShowTime: boolean = false;
  youtubeUrl: string = '';
  needType: boolean = true;
  showTime: string = '';
  needIsTop: boolean = true;
  typeList: {
    typeValue: string;
    typeName: string;
  }[] = [];

  createUser: string = '';
  editUser: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  cover: string = '';
  coverId: string = '';
  levelDecision: number = 2;
  isPendingApproval: boolean = false;
  saveStatus = SAVESTATUS;

  constructor(
    private videoService: VideoService,
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id')) {
        this.typeGroupId = queryParams.get('id')!;
        this.getVideo();
      }
    });
  }

  ngAfterViewInit(): void { }

  getVideo() {
    this.videoService.getVideo(this.typeGroupId).subscribe({
      next: (resp: getVideoResp) => {
        this.title = resp.data.titleName;
        this.youtubeUrl = resp.data.youtubeUrl;
        this.isTop = resp.data.isTop;
        this.isShowTime = resp.data.startTime ? true : false;
        this.showTime = resp.data.startTime;
        this.createUser = resp.data.createUser;
        this.editUser = resp.data.editUser;
        this.levelDecision = resp.data.levelDecision;
        this.isPendingApproval = resp.data.isPendingApproval;
        this.reason = resp.data.reason;
        this.reviewFileName = resp.data.reviewFileName;
        this.reviewFileUrl = resp.data.reviewFileUrl;
      },
      error: (err: HttpErrorResponse) => { },
    });
  }

  cancel() {
    history.back();
  }
  save(status: SAVESTATUS) {
    let req: createUpdateVideoReq = {
      menuItemId: this.menuItemId,
      typeGroupId: this.typeGroupId,
      title: this.title,
      youtubeUrl: this.youtubeUrl,
      isTop: this.isTop,
      startTime: this.isShowTime ? this.showTime ? format(new Date(this.showTime), 'yyyy-MM-dd') : '' : '',
      levelDecision: this.levelDecision,
      lang: this.shareService.getLang(),
    };

    this.videoService.createUpdateVideo(req).subscribe({
      next: (resp: createUpdateResp) => {
        if (resp.code === 200) {
          Swal.fire('成功', `建立成功`, 'success').then(() => {
            if (status === SAVESTATUS.SAVELEAVE) {
              this.router.navigate([`/manage/${this.menuItemId}/video/list`]);
            } else {
              this.typeGroupId = resp.data;
              this.router.navigate([], {
                relativeTo: this.activatedRoute,
                queryParams: {
                  id: this.typeGroupId,
                  menuItemId: this.menuItemId,
                },
                replaceUrl: true
              });
            }
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/viewVideo'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'Video',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    let req: createUpdateVideoReq = {
      menuItemId: this.menuItemId,
      typeGroupId: this.typeGroupId,
      title: this.title,
      youtubeUrl: this.youtubeUrl,
      isTop: this.isTop,
      startTime: this.isShowTime ? this.showTime ? format(new Date(this.showTime), 'yyyy-MM-dd') : '' : '',
      levelDecision: this.levelDecision,
      lang: this.shareService.getLang(),
    };
    this.videoService.createUpdateVideo(req).subscribe({
      next: (resp: createUpdateResp) => {
        if (resp.code === 200) {
          this.router.navigate(['manage/viewVideo'], {
            queryParams: {
              menuItemId: this.menuItemId,
              typeGroupId: resp.data,
              type: 'Video',
              status: ApprovalStatus.BeforeApproval,
            },
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }
}

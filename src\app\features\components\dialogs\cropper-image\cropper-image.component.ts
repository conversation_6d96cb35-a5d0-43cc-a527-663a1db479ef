import { AfterViewInit, Component, Inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { BannerService } from '../../../../core/services/banner.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-cropper-image',
  standalone: false,
  templateUrl: './cropper-image.component.html',
  styleUrl: './cropper-image.component.scss',
})
export class CropperImageComponent implements OnInit {
  loading = false;
  file?: File;
  url: string = '';
  imageFile?: File; // 用於裁剪器的 Blob 資料
  croppedImage?: string | null = '';
  croppedFile?: Blob;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      fileId: string;
    },
    private dialogRef: MatDialogRef<DialogComponent>,
    public dialog: MatDialog,
    private bannerService: BannerService
  ) {
    if (this.data) {
      this.getBanner();
    }
  }

  ngOnInit(): void {}

  getBanner() {
    this.bannerService.getFile(this.data.fileId).subscribe({
      next: (resp: string) => {
        this.url = resp;
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  close() {
    this.dialogRef.close(false);
  }

  imageCropped(event: any): void {
    this.croppedImage = event.objectUrl; // 取得裁切後的圖片數據
    this.croppedFile = event.blob;
  }
  // 手動觸發裁剪
  cut() {
    this.dialogRef.close({
      file: this.croppedFile,
      image: this.croppedImage,
    });
  }
}

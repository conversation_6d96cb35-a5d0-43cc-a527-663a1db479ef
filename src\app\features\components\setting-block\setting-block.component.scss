.out-block {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  &.move {
    cursor: move;
  }

  .btns {
    top: 10px;
    transition: 0.3s ease-in-out;
    opacity: 0;
    display: initial;

    button {
      margin: 5px 0;
    }

    &.isRight {
      opacity: 0;
    }
  }

  &:hover {
    background-color: #cccccc1a;

    .btns {
      left: calc(100vw - 90px);
      opacity: 1;

      &.isRight {
        left: calc(100vw - 90px);
        opacity: 1;
      }
    }
  }
}

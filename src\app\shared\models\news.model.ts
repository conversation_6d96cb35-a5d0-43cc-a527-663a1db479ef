import { MenuItem } from "./menuItem.model";
import { WebSite } from "./website.model";

export interface News {
  id: string;
  title: string;
  startTime: number;
  endTime: number;
  enable: boolean;
  isTop: boolean;
  coverDataId: string;
  authorName: string;
  menuItemId: string;
  description: string;
  meta: string;
  combineNews: CombineNews[];
  newsContent: NewsContent[];
  newsDocument: NewsDocument[];
  coverUrl: string;
  time: number;
  top1: boolean;
  viewCount: number;
  NewsRType: NewsRType[];
  timeShow: boolean;
}

export class CombineNews {
  id?: string;
  menuItemId?: string;
  newsId?: string;
  menuItem?: MenuItem;
  webSite?: WebSite;
}


export class NewsContent {
  id?: string;
  newsId?: string;
  title?: string;
  content?: string;
  sort?: number;
}

export class NewsDocument {
  id?: string;
  newsId?: string;
  fileName?: string;
  sort?: number;
  size?: number;
  documentDataId?: string;
  documentUrl?: string;
}

export class NewsRType {
  id?: string;
  newsId?: string;
  NType?: string;
}

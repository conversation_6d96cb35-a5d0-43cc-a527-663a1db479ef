import { defaultItem } from './share.interface';

export interface surveyItem {
  fieldId?: string;
  fieldName: string;
  fieldType: string;
  fieldMeta?: string;
  required: boolean;
}

export interface getSurveyResp extends defaultItem {
  data: {
    menuitemId: string;
    surveyId: string;
    startTime: string;
    endTime: string;
    typeGroupId: string;
    levelDecision: number;
    currentLevel: number;
    surveyField: surveyItem[];
    creater: string;
    editor: string;
    isPendingApproval: boolean; //審核中
    publishDateTime: string; //是否有發布版及當前發布版發布時間null為無
    lang: string;
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface saveSurveyReq {
  menuitemId: string;
  startTime: string;
  endTime: string;
  levelDecision: number;
  surveyField: surveyItem[];
  lang: string;
}

export interface saveSurveyResp extends defaultItem {
  data: string; //TypeGroupId
}

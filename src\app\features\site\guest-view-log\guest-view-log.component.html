<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>訪客人次統計</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>日期 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startDate" (dateChange)="endDate = ''" />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endDate" [min]="startDate" />
                    <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                    <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
                &nbsp;
                <button mat-flat-button (click)="isTable = !isTable">{{isTable?'列表':'圖表'}}</button>
            </div>
        </div>
        <div class="contents">
            @if(isTable){
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">IP</th>
                            <th width="70px">日期</th>
                            <th width="70px">瀏覽器</th>
                    </thead>
                    <tbody>
                        @for (item of guestViewLogList; track item) {
                        <tr>
                            <td data-label="IP">{{item.operatorIP}}</td>
                            <td data-label="日期">{{item.date|date:'yyyy/MM/dd'}}</td>
                            <td data-label="瀏覽器">{{item.devicePlatform}}</td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="3" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize"
                    [hidePageSize]="true" (page)="changePage($event)">
                </mat-paginator>
            </div>
            }@else {
            <div echarts [options]="chartOption"></div>
            }
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
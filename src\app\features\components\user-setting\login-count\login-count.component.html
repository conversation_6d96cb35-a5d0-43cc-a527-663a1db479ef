<div class="setting-layout">
  <div class="contents">
    <div>
      <div class="title">總登入次數</div>
      <h1 class="count" [countUp]="sub" [options]="ConutUpoptions">0</h1>
    </div>
    <div>
      <form #registrationForm="ngForm" appIdentityRevealed>
        <div class="title">查詢區間</div>
        <mat-form-field appearance="outline">
          <input matInput [matDatepicker]="pickerStart" placeholder="Choose a date" name="startTime"
            [(ngModel)]="form.startTime" (dateChange)="form.endTime=''" required />
          <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
          <mat-datepicker #pickerStart></mat-datepicker>
        </mat-form-field>
        ~
        <mat-form-field appearance="outline">
          <input matInput [matDatepicker]="pickerEnd" placeholder="Choose a date" name="endTime"
            [(ngModel)]="form.endTime" [min]="form.startTime" required />
          <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
          <mat-datepicker #pickerEnd></mat-datepicker>
        </mat-form-field>
        <button mat-flat-button color="primary" (click)="query()" class="btn" [disabled]="registrationForm.invalid">
          查詢
        </button>
      </form>
    </div>
    <div echarts [options]="options" class="chart"></div>
  </div>
  <div class="btns">
    <button mat-flat-button mat-dialog-close>確定</button>
  </div>
</div>
.Setting-layout {
    position: relative;
    .contents {
        margin: 1em 0;
        display: flex;
        flex-direction: column;
        margin: 0 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #949494;

        .block {
            display: flex;
            flex-direction: column;
            padding: 5px 0;

            .title {
                font-size: 1.125em;
                font-weight: bold;
                color: #232127;
                line-height: 1.8;
            }

            input {
                font-size: 1.125em;
                line-height: 1.8;
                outline: 0;
                background-color: #fff;
                border: 1px solid #949494;
                padding: 8px 10px;
                border-radius: 5px;

                &:focus {
                    border: 3px solid #00aeef;
                }
            }
        }
    }

    .group-list {
        height: 100%;
        overflow-y: auto;

        .group-block {
            display: flex;
            flex-direction: column;
            margin: 15px 20px;
            transition: 0.3s ease-in-out;

            &:hover {
                // box-shadow: 0 0 10px #ccc;
                background-color: #c2efff;
            }

            .group-name {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 1.125em;
                line-height: 1.8;
                outline: 0;
                background-color: #fff;
                border: 1px solid #949494;
                padding: 8px 10px;
                border-radius: 5px;
                cursor: pointer;

                &:hover {
                    // box-shadow: 0 0 10px #ccc;
                    background-color: #c2efff;
                    border: 3px solid #00aeef;
                }
                &.selected {
                    background-color: #c2efff;
                    border: 1px solid #00aeef;
                }
            }

            .group-user {
                border-bottom: 1px solid #ccc;
                position: relative;
                min-height: 100px;
                display: flex;
                flex-direction: column;
                padding: 0 1em;
                line-height: 2;

                .add-btn {
                    text-align: end;
                }

                .zoomout {
                    text-align: center;

                    .material-icons {
                        cursor: pointer;
                    }
                }
            }
        }
    }

    .lazyload {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

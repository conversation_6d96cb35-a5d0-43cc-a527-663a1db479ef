import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { MatDialogRef } from '@angular/material/dialog';

const regExp = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z])(?=.*\W).{12,32}$/;
@Component({
  selector: 'app-change-password',
  standalone: false,

  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss'
})
export class ChangePasswordComponent {
  form!: FormGroup;

  constructor(
    private _fb: FormBuilder,
    public dialogRef: MatDialogRef<DialogComponent>,
  ) {

  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.form = this._fb.group({
      // password: [null, Validators.pattern('^(?=.*\d)(?=.*[a-zA-Z])(?=.*\W){12,20}$')],
      // check: [null, Validators.pattern('^(?=.*\d)(?=.*[a-zA-Z])(?=.*\W){12,20}$')]
      password: [null, Validators.pattern(regExp)],
      check: [null, Validators.pattern(regExp)]
    });
  }

  submit(formVal: any) {
    if (formVal.password == null || formVal.check == null) {
      Swal.fire('請輸入密碼', '', 'warning');
    }
    if (formVal.password.length < 12 || formVal.check.length < 12) {
      Swal.fire('密碼長度應十二個字元以上，包含英文大小寫、數字及特殊字元', '', 'warning');
      return;
    }
    if (formVal.password !== formVal.check) {
      Swal.fire('請確認密碼正確', '', 'warning');
      return;
    }
    this.dialogRef.close(formVal);
  }
}

import { Injectable } from '@angular/core';
import { MenuitemNewsTypeListRes } from '../../shared/models/menuitemnewstype.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MenuItemNewsTypeService {
  private baseUrl = `api/Manage/MenuitemNewsType`;
  constructor(
    private http: HttpClient
  ) { }

  // 取得選單消息類型列表
  getMenuitemNewsTypeList(
    menuitemId: string
  ): Observable<MenuitemNewsTypeListRes> {
    return this.http
      .get<MenuitemNewsTypeListRes>(
        `${this.baseUrl}/MenuitemNewsTypeList?menuitemid=${menuitemId}`
      )
  }
}

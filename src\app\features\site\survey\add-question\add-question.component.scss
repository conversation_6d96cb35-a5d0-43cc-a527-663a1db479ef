.contents {
    margin: 1em 0;
    display: flex;
    flex-direction: column;
    margin: 0 20px;

    .block {
        display: flex;
        flex-direction: column;
        padding: 15px 0;
        border-bottom: 1px solid #949494;
        &.row {
            display: flex;
            flex-direction: row;
            align-items: center;

            .column {
                padding-right: 20px;
            }

            .mat-slide-toggle {
                margin-left: 0.5em;
            }
        }

        .title {
            font-size: 1.125em;
            font-weight: bold;
            color: #232127;
            line-height: 1.8;
        }

        mat-select {
            font-size: 1.125em;
            line-height: 1.8;
            outline: 0;
            background-color: #fff;
            border: 1px solid #949494;
            padding: 8px 0;
            border-radius: 5px;
        }

        input {
            font-size: 1.125em;
            line-height: 1.8;
            outline: 0;
            background-color: #fff;
            border: 1px solid #949494;
            padding: 8px 10px;
            border-radius: 5px;

            &:focus {
                border: 3px solid #00aeef;
            }
        }
    }
}

.btns {
    display: flex;
    justify-content: center;
    margin: 15px 0;
}

.title-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialog-close-btn {
        display: flex;
    }
}

.close-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px;
    button{
        margin: 0 10px;
    }
}

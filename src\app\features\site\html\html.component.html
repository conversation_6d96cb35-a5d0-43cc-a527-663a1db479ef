<div>
    <div class="news-layout">
        <span class="white">
            <h1>{{title}}-(HTML編輯)</h1>
        </span>
    </div>
    <div class="news-layout">
        <div class="contents">
            <span class="block">
                <div class="title-ctn">
                    <span class="title">HTML</span>
                </div>
                <textarea [(ngModel)]="content">
                </textarea>
            </span>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">關鍵字</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>關鍵字</mat-label>
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{creater}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editor}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="save()">存檔</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
    </div>
</div>
<app-loading [loading]="loading"></app-loading>
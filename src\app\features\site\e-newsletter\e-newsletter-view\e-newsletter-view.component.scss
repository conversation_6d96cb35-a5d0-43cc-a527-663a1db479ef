/* ---- 以下是您需要的固定審核區塊樣式 ---- */

.review-panel {
    position: fixed;
    bottom: 109px;
    left: 34%;
    width: 600px;
    background-color: #f8f9fa;
    padding: 15px 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 5; /* 設定高的 z-index 來確保在最上層 */
    box-sizing: border-box; /* 確保 padding 不會影響總寬度 */
    display: flex;
    flex-direction: column;
    height: auto; /* 高度自動 */
    transition: all 0.3s ease; /* 動畫效果 */
}

.review-actions {
    display: flex;
    gap: 15px; /* 按鈕之間的間距 */
}

input {
    font-size: 25px;
    outline: 0;
    background-color: #ffffff;
    border: 1px solid #868585;
}

h2 {
    text-align: center;
}

.btn-group {
    width: 100%;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    button {
        margin: 0 10px;
    }
}

.review-switch-btn {
    position: fixed;
    top: 250px;
    right: 30px;
    width: 50px;
    height: 50px;

    // background-color: red;
    border: 0;
    background-color: #177691;
    // border-color: #177691;
    border-radius: 40px;
    z-index: 5; /* 設定高的 z-index 來確保在最上層 */
    color: #fff;
    // padding: 15px 20px;
    // box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    // box-sizing: border-box; /* 確保 padding 不會影響總寬度 */
    // display: flex;
    // flex-direction: column;
    // height: auto; /* 高度自動 */
    // transition: all 0.3s ease; /* 動畫效果 */
}

.iframe-container {
    position: relative; /* 確保遮罩是相對於這個容器定位 */
    width: 100%; /* 依據你的需求設定寬高 */
    height: 100vh;
}

.iframe-container iframe {
    width: 100%;
    height: 100%;
}

.iframe-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3; /* 確保遮罩層級高於 iframe */
    cursor: not-allowed; /* 顯示禁止符號，增加使用者體驗 */
}

.file-layout {
    display: flex;
    flex-direction: column;
    input[type="file"] {
        display: none;
    }
    button {
        width: 130px;
        margin: 10px 0;
    }
    .file-name {
        margin-left: 10px;
    }
}

.files-layout {
    display: flex;
    align-items: center;

    .file-block {
        border: 1px solid #ccc;
        background-color: #ffffff;
        margin: 5px;
        padding: 5px 1em;
        cursor: pointer;
        transition: 0.3s ease-in-out;

        &:hover {
            border: 1px solid #3f51b5;
        }
    }
}

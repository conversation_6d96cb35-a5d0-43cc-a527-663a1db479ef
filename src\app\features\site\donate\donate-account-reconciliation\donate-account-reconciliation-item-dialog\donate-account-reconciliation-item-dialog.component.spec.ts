import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DonateAccountReconciliationItemDialogComponent } from './donate-account-reconciliation-item-dialog.component';

describe('DonateAccountReconciliationItemDialogComponent', () => {
  let component: DonateAccountReconciliationItemDialogComponent;
  let fixture: ComponentFixture<DonateAccountReconciliationItemDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DonateAccountReconciliationItemDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DonateAccountReconciliationItemDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

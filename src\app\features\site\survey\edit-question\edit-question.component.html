<h3 mat-dialog-title class="title-group">
    <span>編輯問題</span>
    <span style="margin-left: auto; cursor: pointer;" (click)="close()">
        <mat-icon class="dialog-close-btn">close</mat-icon></span>
</h3>

<form class="Setting-layout" [formGroup]="form">
    <div class="contents">
        <span class="block">
            <span class="title">問題類型</span>
            {{fieldTypeText[type]}}
        </span>
        <span class="block">
            <span class="title">標題</span>
            <input class="input" type="text" formControlName="name">
        </span>
        @if(type===FieldType.File){
        <span class="block">
            <span class="title">檔案大小限制(MB)</span>
            <input class="input" type="number" formControlName="fileSize">
        </span>
        }
        @if(isMulit){
        <div cdkDropList (cdkDropListDropped)="drop($event)">
            @for (item of optionList;let index=$index ;track item) {
            <section class="option-block" cdkDrag>
                <ng-template cdkDragPreview>
                    <div class="drag-preview">
                        {{ item.value }}
                    </div>
                </ng-template>
                <input type="text" [value]="item.value" (keyup)="input($event, item)">
                <button mat-icon-button (click)="deleteField($event, index)">
                    <mat-icon>clear</mat-icon>
                </button>
            </section>
            }
        </div>
        <button style="margin-top: 10px;" mat-flat-button (click)="addOption()">新增選項</button>
        }
        @if(isImageCheckBox){
        <div cdkDropList (cdkDropListDropped)="drop($event)">
            @for (item of optionList;let index=$index ;track item) {
            <section class="image-option-block" cdkDrag>
                <ng-template cdkDragPreview>
                    <div class="drag-preview">
                        {{ item.value }}
                    </div>
                </ng-template>
                <button mat-flat-button class="btn" (click)="selectGalleryImage(item)">
                    選擇圖片
                </button>
                <div class="image-option-group">
                    <img [src]="item.src" alt="" srcset="">
                    <input type="text" [value]="item.value" (keyup)="input($event, item)">
                </div>
                <button mat-icon-button (click)="deleteField($event, index)">
                    <mat-icon>clear</mat-icon>
                </button>
            </section>
            }
        </div>
        <button style="margin-top: 10px;" mat-flat-button (click)="addOption()">新增選項</button>
        }
        <span class="block row">
            <span class="column">
                <span class="title">必填</span>
                <mat-slide-toggle color="primary" formControlName="required"></mat-slide-toggle>
            </span>
        </span>
    </div>
    <div class="close-btn">
        <button mat-stroked-button mat-dialog-close type="button">取消</button>
        <button mat-flat-button color="primary" type="submit" [disabled]="!form.valid" (click)="submit()">
            確定
        </button>
    </div>
</form>
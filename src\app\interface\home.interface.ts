import { defaultItem } from './share.interface';

export interface getNewsListWithHomeResp extends defaultItem {
  data: {
    currentPage: number;
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    data: newsItem[];
  };
}

export interface newsItem {
  content: string;
  coverDescription: string;
  coverUrl: string;
  lang: string;
  new_NewsId: string;
  startTime: string;
  title: string;
  type: string;
  typeGroupId: string;
  description: string;
}

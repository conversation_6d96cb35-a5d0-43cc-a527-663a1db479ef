import { Component } from '@angular/core';
import { WebSiteService } from '../../../../core/services/website.service';
import { UserDataService } from '../../../../core/services/userData.service';
import { MatDialog } from '@angular/material/dialog';
import { UserData } from '../../../../shared/models/userData.model';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { AuthComponent } from '../../../components/dialogs/auth/auth.component';

@Component({
  selector: 'app-create-web-list',
  standalone: false,
  
  templateUrl: './create-web-list.component.html',
  styleUrl: './create-web-list.component.scss'
})
export class CreateWebListComponent {
  constructor(
      private _webSiteService: WebSiteService,
      private _userDataService: UserDataService,
      public dialog: MatDialog,
    ) {}
  
    userData!: UserData;
    webList = [
      {
        id: 'c52cd7e3-2efb-478e-beab-cbf98db8309d',
        name: '世壯運_中文版',
      },
      {
        id: '7fb84b88-98e0-48bc-87bf-f268958be10f',
        name: '世壯運_日文版',
      },
      {
        id: 'a969186b-f038-470a-9c36-cf11a0aef95c',
        name: '世壯運_英文版',
      },
      {
        id: 'ce4c410b-6079-4a2c-abf9-b86ad1f74419',
        name: '世壯運_韓文版',
      },
    ];
  
    ngOnInit() {
      this.getUser();
    }
  
    getUser() {
      this._userDataService.get2().subscribe((x) => {
        this.userData = x;
      });
    }
  
    /** 進入後台 */
    goBackstage(data: any) {
      this._webSiteService.getWebSiteBackstageUrl(data.id).subscribe((url) => {
        window.open(url + 'manage/home');
      });
    }
  
    login() {
      // 登入
      const dialogRef = this.dialog.open(DialogComponent, {
        data: {
          width: '400px',
          title: '登入',
          showHeader: true,
          contentTemplate: AuthComponent,
        },
      });
  
      dialogRef.afterClosed().subscribe((res) => {
        this.getUser();
      });
    }
}

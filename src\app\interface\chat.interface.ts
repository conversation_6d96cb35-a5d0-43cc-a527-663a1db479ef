import { defaultItem } from './share.interface';

export interface getCustomerServiceListReq {
  keyword: string;
}

export interface getCustomerServiceListResp extends defaultItem {
  data: customerServiceItem[];
}

export interface customerServiceItem {
  id: string;
  question: string;
  answer: string;
  sort: number;
  enable: boolean;
  isDelete: boolean;
  createTime: string;
}

export interface addCustomerServiceReq {
  aiQuestionId?: string;
  aiQuestion: string;
  aiAnswer: string;
  enable: boolean;
}

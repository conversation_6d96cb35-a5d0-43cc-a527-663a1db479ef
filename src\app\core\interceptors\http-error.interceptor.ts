import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpInterceptorFn,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import Swal from 'sweetalert2';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error instanceof HttpErrorResponse) {
          switch (error.status) {
            case 400:
              console.error('Bad Request:', error.error);
              break;
            case 401:
              console.error('Unauthorized:', error.error);
              Swal.fire('錯誤', '登入超時，請重新登入', 'error').then(() => {
                sessionStorage.removeItem('token');
                window.location.href = 'manage/home'; // 直接重新載入
              });
              // You might want to redirect to the login page here.
              break;
            case 404:
              console.error('Not Found:', error.error);
              break;
            case 500:
              console.error('Internal Server Error:', error.error);
              break;
            default:
              console.error(`Unhandled Error (${error.status}):`, error.error);
          }
          if (error.status !== 401) {
            Swal.fire('錯誤', error.error.message, 'error');
          }
        }
        // Return a user-friendly error message or rethrow the error.
        return throwError(() => error);
      })
    );
  }
}

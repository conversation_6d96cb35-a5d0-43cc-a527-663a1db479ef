import { Component, Inject, Input } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { AnalysisService } from '../../../../../core/services/analysis.service';
import { UserDataService } from '../../../../../core/services/userData.service';
import { DialogComponent } from '../../../../../shared/components/dialog/dialog.component';
import { AuthComponent } from '../auth.component';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { Config } from '../../../../../core/services/config';

@Component({
  selector: 'app-check-code',
  standalone: false,

  templateUrl: './check-code.component.html',
  styleUrl: './check-code.component.scss',
})
export class CheckCodeComponent {
  form: FormGroup;
  loading = false;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      id: string;
    },
    private _fb: FormBuilder,
    private _analysis: AnalysisService,
    private _userDataService: UserDataService,
    private dialogRef: MatDialogRef<DialogComponent>,
    private matDialog: MatDialog
  ) {
    this.form = this._fb.group({
      code: [null, Validators.required],
    });
  }

  submit() {
    this.loading = true;
    this._userDataService
      .confirm2FALogin(this.form.value.code, this.data.id)
      .subscribe({
        next: (res: string) => {
          sessionStorage.setItem('token', res);
          Config.defaultOptions = {
            headers: null,
          };
          Swal.fire('登入成功', '', 'success').then(() => {
            location.reload();
          });
          this.getMenuPolicy();
          this.getFunctionPolicy();
          this.dialogRef.close();
          this.loading = false;
        },
        error: (error: HttpErrorResponse) => {
          // 失敗的處理
          Swal.fire(error.error, '', 'error');
          this.loading = false;
        },
      });
  }

  /** 取得目前的角色功能權限列表 */
  getFunctionPolicy() {
    this._userDataService.getUserFunctionPolicy().subscribe((res: any) => {
      sessionStorage.setItem('functionPolicy', res);
    });
  }

  /** 取得目前的角色選單權限列表，如為null則表示為系統管理員 */
  getMenuPolicy() {
    this._userDataService.getUserMenuPolicy().subscribe((res: any) => {
      sessionStorage.setItem('menuPolicy', res);
    });
  }

  back() {
    this.dialogRef.close();
    this.matDialog.open(DialogComponent, {
      data: {
        width: '400px',
        title: '登入',
        showHeader: true,
        contentTemplate: AuthComponent,
      },
    });
  }
}

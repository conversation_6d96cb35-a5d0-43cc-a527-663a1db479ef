import { Component, Input } from '@angular/core';
import { MenuItem } from '../../../shared/models/menuItem.model';

@Component({
  selector: 'app-menu-item',
  standalone: false,

  templateUrl: './menu-item.component.html',
  styleUrl: './menu-item.component.scss'
})
export class MenuItemComponent {
  @Input() menu: MenuItem[] = [];
  @Input() style: any;

  constructor() {

  }

  ngOnInit(): void {

  }
}

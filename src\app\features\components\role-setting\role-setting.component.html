<div class="main-layout">
  <!-- <div class="header">
    <h2>
      角色權限設置
      <i class="material-icons" *ngIf="false" [appTip]="'角色權限設置'">help_outline</i>
    </h2>
    <i class="material-icons close" (click)="close()">clear</i>
  </div> -->
  <div class="role-layout">
    <span
      class="role-block"
      *ngFor="let item of roleList"
      (contextmenu)="onContextMenu($event, item)"
    >
      <span class="role-name">
        {{ item["name"] }}
        <mat-slide-toggle
          color="primary"
          (change)="updateEnable($event, item)"
          [checked]="item['enable']"
        >
        </mat-slide-toggle>
      </span>
      <span class="role-menu" *ngIf="item['menuShow']">
        <!-- <button mat-flat-button (click)="selectAllMenu(item)">全選</button> -->
        <!-- <hm-tree-node [node]="treeNode" (receiveData)="receiveTreeData($event)" childrenProperty="inverseParent">
        </hm-tree-node> -->

        <mat-tree
          #tree
          [dataSource]="dataSource"
          [childrenAccessor]="childrenAccessor"
        >
          <!-- This is the tree node template for leaf nodes -->
          <mat-tree-node
            *matTreeNodeDef="let node"
            matTreeNodePadding
          >
            <!-- use a disabled button to provide padding for tree leaf -->
            <button mat-icon-button disabled></button>
            <mat-checkbox color="primary" [checked]="node['isCheck']" (change)="menuPolicyChecked($event, node, item)"></mat-checkbox>
            {{ node.name }}
          </mat-tree-node>
          <!-- This is the tree node template for expandable nodes -->
          <mat-tree-node
            *matTreeNodeDef="let node; when: hasChild"
            matTreeNodePadding
            matTreeNodeToggle
            [cdkTreeNodeTypeaheadLabel]="node.name"
          >
            <button
              mat-icon-button
              matTreeNodeToggle
              [attr.aria-label]="'Toggle ' + node.name"
            >
              <mat-icon class="mat-icon-rtl-mirror">
                {{ tree.isExpanded(node) ? "expand_more" : "chevron_right" }}
              </mat-icon>
            </button>
            <mat-checkbox color="primary" [checked]="node['isCheck']"  (change)="menuPolicyChecked($event, node, item)"></mat-checkbox>
            {{ node.name }}
          </mat-tree-node>
        </mat-tree>

        <span class="zoomout">
          <i class="material-icons" (click)="closeMenu(item)">expand_less</i>
        </span>
        <app-loading [loading]="menuLoading"></app-loading>
      </span>

      <span class="role-function" *ngIf="item['functionShow']">
        <button mat-flat-button (click)="selectAllFunc(item)">全選</button>
        <mat-checkbox
          color="primary"
          *ngFor="let func of functionList"
          [checked]="func['checked']"
          (change)="functionChecked($event, func, item)"
        >
          {{ func["name"] }}
        </mat-checkbox>
        <span class="zoomout">
          <i class="material-icons" (click)="closeFuction(item)">expand_less</i>
        </span>
        <app-loading [loading]="functionLoading"></app-loading>
      </span>
    </span>

    <span class="role-block" (click)="createRole()">
      <span class="role-name add">
        <i class="material-icons">add</i>
      </span>
    </span>
    <app-loading [loading]="loading"></app-loading>
  </div>
</div>

<!-- <app-role-contexxt-menu
  (closeContextMenu)="closeContextMenu($event)"
></app-role-contexxt-menu> -->
<!-- <mat-menu #contextMenu="matMenu">
  <button mat-menu-item>編輯</button>
  <button mat-menu-item>選單權限</button>
  <button mat-menu-item>功能權限</button>
  <button c mat-menu-item>刪除</button>
</mat-menu>
<div
  style="visibility: hidden; position: fixed"
  [style.left]="contextMenuPosition.x"
  [style.top]="contextMenuPosition.y"
  [matMenuTriggerFor]="contextMenu"
></div> -->
<app-role-context-menu
  (closeContextMenu)="closeContextMenu($event)"
></app-role-context-menu>

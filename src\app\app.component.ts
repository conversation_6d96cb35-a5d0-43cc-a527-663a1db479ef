import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { UserDataService } from './core/services/userData.service';
import { Config } from './core/services/config';

declare var getWebSiteHeaders: any;

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = '財團法人原住民族語言研究發展基金會';
  token: string = '';
  constructor(
    private _router: Router,
    private _userDataService: UserDataService,
    private cdr: ChangeDetectorRef
  ) {
    // Config.onError.subscribe(result => {
    //   if (result.error.error) {
    //     // 登入失敗
    //     if (result.error.error.code === 1) {
    //       Config.authError.next(result.error.error);
    //       return;
    //     }
    //   }
    //   if (location.pathname === '/manage/home') {
    //     return;
    //   }
    //   // this._router.navigate(['/manage']);
    //   window.scrollTo(0, 0);
    // });
    // if (location.pathname === '/admin' || location.pathname === '/admin/') {
    //   this._router.navigate(['/createWebs']);
    // }
    //this.getFunctionPolicy();
    //this.getMenuPolicy();
  }

  /** 取得目前的角色功能權限列表 */
  getFunctionPolicy() {
    this._userDataService.getUserFunctionPolicy().subscribe((res: any) => {
      sessionStorage.setItem('functionPolicy', res);
      this.cdr.detectChanges();
    });
  }

  /** 取得目前的角色選單權限列表，如為null則表示為系統管理員 */
  getMenuPolicy() {
    this._userDataService.getUserMenuPolicy().subscribe((res: any) => {
      sessionStorage.setItem('menuPolicy', res);
      this.cdr.detectChanges();
    });
  }
}

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import {
  getLineListReq,
  getLineListResp,
} from '../../interface/line.interface';

@Injectable({
  providedIn: 'root',
})
export class LineService {
  constructor(private httpClient: HttpClient) {}

  getLineList(req: getLineListReq): Observable<getLineListResp> {
    return this.httpClient.get<getLineListResp>(
      'api/Manage/New_News/GetNew_LineDataList',
      {
        params: {
          ...req,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }
}

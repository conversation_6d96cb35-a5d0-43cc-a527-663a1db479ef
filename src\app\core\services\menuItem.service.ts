import { Injectable } from '@angular/core';
import {
  createMenuItemReq,
  getHaveMenuResp,
  MenuItem,
} from '../../shared/models/menuItem.model';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { UserGroup } from '../../shared/models/userGroup.model';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class MenuItemService {
  lang: string = sessionStorage.getItem('lang')
    ? (sessionStorage.getItem('lang') as string)
    : 'zh';
  constructor(private http: HttpClient) { }

  getHaveMenu(): Observable<getHaveMenuResp> {
    return this.http.get<getHaveMenuResp>('api/Manage/MenuItem/ExistMenuitem',
      {
        params: {
          websiteId: sessionStorage.getItem('webSiteId')!,
        },
      }
    )
  }

  /**
   * 取得指定子網站中可見的選單
   *
   * @param webSiteId 網站唯一識別號
   */
  getMenu(webSiteId: string): Observable<MenuItem[]> {
    return this.http.get<MenuItem[]>(`/api/MenuItem/tree/${webSiteId}`, {
      params: {
        lang: this.lang,
      },
    });
  }

  /**
   * 取得指定子網站中可見的選單
   *
   * @param webSiteId 網站唯一識別號
   */
  getMenu2(webSiteId: string): Observable<MenuItem[]> {
    return this.http.get<MenuItem[]>(`/api/Manage/MenuItem/tree/${webSiteId}`);
  }

  /**
   * 更新排序
   *
   * @param ids 唯一識別號集合
   */
  updateSort(ids: string[]): Observable<any> {
    return this.http.put<any>(`/api/Manage/MenuItem/sort`, { ids: ids });
  }

  /**
   * 取得指定實例
   *
   * @param id 唯一識別號
   */
  get2(id: string): Observable<MenuItem> {
    return this.http.get<MenuItem>(`/api/Manage/MenuItem/${id}`);
  }

  /**
   * 檢驗指定MenuItem是否可設定首頁項目
   *
   * @param id
   */
  onHomepageable(id: string): Observable<boolean> {
    return this.http.get<boolean>(`/api/Manage/MenuItem/${id}/onHomepageable`);
  }

  /**
   * 檢驗指定MenuItem是否可設定導覽列項目
   *
   * @param id
   */
  onNavigationable(id: string): Observable<boolean> {
    return this.http.get<boolean>(
      `/api/Manage/MenuItem/${id}/onNavigationable`
    );
  }

  /**
   * 群組限制
   *
   * @param id 選單唯一識別號
   */
  listGroups(id: string): Observable<UserGroup[]> {
    return this.http.get<UserGroup[]>(`/api/Manage/MenuItem/${id}/groups`);
  }

  /**
   * 取得指定子網站已經使用的選單類型列表
   *
   * @param webSiteId 子網站唯一識別號
   */
  existsTypes(webSiteId: string): Observable<string[]> {
    return this.http.get<string[]>(
      `/api/Manage/MenuItem/usedTypes/${webSiteId}`
    );
  }

  /**
   * 取得指定子網站中可見的選單(僅有目錄)
   *
   * @param webSiteId 網站唯一識別號
   * @param type 要移動的類型
   */
  getMenuMoveable(
    webSiteId: string,

    type?: string
  ): Observable<MenuItem[]> {
    let httpParams = new HttpParams();
    if (type) {
      httpParams = httpParams.set('type', type);
    }
    return this.http.get<MenuItem[]>(
      `/api/Manage/MenuItem/tree-moveable/${webSiteId}`,
      { params: httpParams }
    );
  }

  /**
   *  變更父層選單
   * @param currentMenuitemId
   * @param parentMenuitemId
   * @returns
   */
  changeParentMenu(
    currentMenuitemId: string,
    parentMenuitemId: string | null
  ): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      `api/Manage/MenuItem/UpdateMenuitemParent`,
      {
        currentMenuitemId: currentMenuitemId,
        parentMenuitemId: parentMenuitemId,
      }
    );
  }

  /**
   * 建立實例
   *
   * @param instance 實例內容
   */
  create(instance: createMenuItemReq): Observable<MenuItem> {
    return this.http.post<MenuItem>(`/api/Manage/MenuItem`, instance);
  }

  /**
   * 更新實例
   *
   * @param instance 實例內容
   */
  update(instance: MenuItem): Observable<MenuItem> {
    return this.http.put<MenuItem>(`/api/Manage/MenuItem`, instance);
  }

  /**
   * 刪除指定實例
   *
   * @param id 唯一識別號
   */
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`/api/Manage/MenuItem/${id}`);
  }

  /**
   * 取得熱門跟運動種類Id
   *
   * @param webSiteId 網站ID
   * @param type 種類
   *
   */
  getMenuitemIdByType(webSiteId: string, type: string): Observable<string> {
    return this.http.get<string>(
      `/api/MenuItem/${webSiteId}/menuitemId/${type}`
    );
  }
}

.index-main-layout {
	margin: 0 0;
	padding: 0;
	width: 100%;
	background: transparent url(../../../../../assets/images/share/index-search-bg.svg) center top no-repeat;
	background-size: 70%;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #ebeadf;
}

/*標題*/
.index-title {
	margin: 0;
	padding: 40px 0 30px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	&-text {
		margin: 0;
		padding: 0;
		font-size: 2.5em;
		color: #214e57;
	}
	&-more {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		font-size: 1.5em;
		text-decoration: none;
		font-weight: bold;
		color: #214e57;
		span {
			font-size: 1.5em;
		}
	}
}

/*頁籤*/
.tab-layout {
	margin: 10px 0;
	display: flex;
	justify-content: center;
	.tab-btn {
		margin: 5px;
		padding: 10px 20px;
		color: #202d3f;
		background-color: #fff;
		border-radius: 60px;
		border: 0;
		font-size: 1.25em;
		font-weight: bold;
		&-on {
			color: #fff;
			background-color: #177691;
		}
	}
}

.index-search-layout,
.index-news-layout,
.index-drive-results-layout,
.index-theme-service-layout {
	padding: 0 20px 40px;
	max-width: 1600px;
	width: 100%;
	box-sizing: border-box;
}

/*關鍵字查詢&文字雲*/
.index-search-layout {
	margin: 0;
	padding: 20px;
	display: flex;
	justify-content: center;
	/*關鍵字查詢*/
	.index-search-item {
		margin: 0;
		padding: 20px;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		&-title {
			margin: 0;
			padding: 0;
			color: #214e57;
			font-size: 3em;
		}
		&-text {
			margin: 0;
			padding: 0;
			color: #728d8b;
			font-size: 1.25em;
			letter-spacing: 1.5px;
			font-weight: bold;
		}
		.index-search {
			margin: 30px 0 0;
			display: flex;
			flex-direction: row;
			align-items: stretch;
			justify-content: space-between;
			overflow: hidden;
			border-radius: 15px;
			border: 1px solid #214e57;
			background-color: #fff;
			&-input {
				padding: 23px;
				border: 0;
				box-sizing: border-box;
				font-size: 1.125em;
				font-weight: bold;
				color: rgba(0, 0, 0, 0.5);
				width: calc(100% - 68px);
			}
			&-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 68px;
				height: 68px;
				border-radius: 10px;
				background-color: #214e57;
				color: #fff;
				font-size: 1.25em;
				border: 0;
			}
		}
	}
	/*文字雲*/
	.index-word-cloud {
		margin: 0;
		padding: 20px;
		img {
			width: 100%;
		}
	}
}

/*首頁推動成果*/
.index-drive-results-layout {
	.index-drive-results {
		&-cont {
			padding: 20px;
			display: grid;
			grid-template-columns: repeat(4, minmax(0, 1fr));
			border-radius: 180px;
			background-color: #fff;
		}
		&-item {
			padding: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-right: 1px solid #202d3f;
			&:last-child {
				border: 0;
			}
		}
		&-value {
			font-size: 2.25em;
			font-weight: bold;
		}
		&-title {
			display: flex;
			flex-direction: row;
			align-items: center;
			font-size: 1.125em;
			font-weight: bold;
			&-img {
				display: flex;
				padding-right: 5px;
			}
		}
	}
}

/*主題服務*/
.index-theme-service-layout {
	padding-bottom: 60px;
	.index-theme-service {
		&-cont {
			display: grid;
			grid-template-columns: repeat(4, minmax(0, 1fr));
			gap: 1.5rem;
		}
		&-item {
			margin: 0;
			padding: 30px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: stretch;
			border-radius: 40px;
			background-color: #fff;
			box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1);
			&-title {
				margin: 0;
				padding: 0 0 80px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				font-size: 1.875em;
				color: #202d3f;
				&-text {
					margin: 0;
					padding: 0;
				}
			}
			&-cont {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				color: #202d3f;
				font-size: 1.125em;
				p {
					margin: 0;
					padding: 0 20px 0 0;
				}
			}
			&:nth-child(1) {
				grid-column: 1;
				grid-row: 1;
				background: #fff url(../../../../../assets/images/share/theme-service-book-bg.svg) right 70px top
					no-repeat;
			}
			&:nth-child(2) {
				grid-column: 2 / 4;
				grid-row: 1;
				background: #fff url(../../../../../assets/images/share/theme-service-today-bg.svg) right 120px top
					no-repeat;
				background-size: contain;
			}
			&:nth-child(3) {
				grid-column: 4 / 4;
				grid-row: 1 / 3;
				background: #fff url(../../../../../assets/images/share/theme-service-paid-bg.svg) right center
					no-repeat;
			}
			&:nth-child(4) {
				grid-column: 1 / 3;
				grid-row: 2;
				background: #fff url(../../../../../assets/images/share/theme-service-approval_delegation-bg.svg) right
					120px bottom no-repeat;
			}
			&:nth-child(5) {
				grid-column: 3 / 4;
				grid-row: 2;
				background: #fff url(../../../../../assets/images/share/theme-service-account-balance-bg.svg) right
					bottom no-repeat;
			}
		}
	}
}

/*推薦族語資源*/
.index-recommended-resources-layout {
	padding: 0 20px;
	width: 100%;
	// background-color: #e0d4c2;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
	.index-title {
		justify-content: center;
	}
	.index-recommended-resources {
		&-cont {
			margin: 0;
			padding: 30px 0;
			box-sizing: border-box;
			display: grid;
			grid-template-columns: 220px 1fr;
			max-width: 1600px;
			width: 100%;
			height: 100%;
			gap: 1.5rem;
		}
		&-menu {
			display: grid;
			gap: 1.5rem;
			&-btn {
				margin: 0;
				padding: 10px 20px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				overflow: hidden;
				border-radius: 10px;
				background-color: #fff;
				box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1);
				border: 0;
				cursor: pointer;
				img {
					width: 100%;
					height: auto;
				}
				&:hover {
					background-color: #177691;
					opacity: 0.5;
					img {
						filter: brightness(100);
					}
				}
				&.btn-on {
					background-color: #177691;
					img {
						filter: brightness(100);
					}
				}
			}
		}

		&-iframe {
			margin: 0;
			padding: 0;
			width: 100%;
			height: 100%;
			border-radius: 10px;
			overflow: hidden;
			display: grid;
			gap: 1.5rem;
			iframe {
				width: 100%;
				height: 100%;
				border: none;
				display: block;
			}
			img {
				width: 100%;
				height: auto;
			}
		}
		&-web {
			display: block;
		}
		&-phone {
			display: none;
		}
		&-infor {
			display: flex;
			padding: 40px;
			width: 100%;
			max-width: 1320px;
			border-radius: 10px;
			overflow: hidden;
			background-color: #fff;
			box-sizing: border-box;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 20px;
			&-title {
				text-align: center;
				img {
					max-width: 551px;
					height: auto;
				}
			}
			&-sub-title {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				max-width: 1000px;
				height: 50px;
				text-align: center;
				text-indent: -9999px;
				position: relative;
				background: url(../../../../../assets/images/share/sub-title.png) center center no-repeat;
				&:before {
					content: "";
					display: block;
					width: 50px;
					height: 50px;
					background: url(../../../../../assets/images/share/sub-title.png) left center no-repeat;
				}
				&:after {
					content: "";
					display: block;
					width: 50px;
					height: 50px;
					background: url(../../../../../assets/images/share/sub-title.png) right center no-repeat;
				}
				img {
					max-width: 1000px;
					width: 100%;
				}
			}
			&-cont {
				margin: 0;
				padding: 0;
				list-style: none;
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;
				justify-content: center;
				align-items: flex-start;
				gap: 1.5rem;
				max-width: 1000px;
				li {
					margin: 0;
					padding: 0;
				}
				a {
					display: inline-block;
				}
				img {
					max-width: 289.47px;
					width: 100%;
				}
			}
			.card-body {
				text-align: center;
				font-size: 1.5em;
				color: rgb(90, 73, 63);
				background-color: rgb(255, 242, 218);
				flex: 1 1 auto;
				padding: 1rem;
				border-radius: 10px;
			}
			&-item01 {
			}
			&-item02 {
				background: #fff url(../../../../../assets/images/share/minanam-bg.png) center 0 no-repeat;
				background-size: cover;
			}
			&-item03 {
				padding: 40px 0 0;
				.index-recommended-resources-infor-title {
					width: 100%;
				}
				.index-recommended-resources-infor-cont {
					padding: 40px 0;
					max-width: fit-content;
					background-color: #39523f;
				}
			}
			&-item04 {
				background: #fff url(../../../../../assets/images/share/e-dictionary-bg.png) center bottom no-repeat;
				.index-recommended-resources-infor-cont {
					padding: 40px 0;
					max-width: fit-content;
				}
			}
			&-item05 {
			}
		}
	}
}

@media (max-width: 1024px) {
	/*推薦族語資源*/
	.index-recommended-resources-layout {
		.index-recommended-resources {
			&-menu {
				display: flex;
				flex-direction: column;
			}
			&-web {
				display: none;
			}
			&-phone {
				display: block;
			}
		}
	}
}
@media (max-width: 768px) {
	.index-main-layout {
		margin: 70px 0 0;
	}
	/*關鍵字查詢&文字雲*/
	.index-search-layout {
		flex-direction: column;
	}
	/*首頁最新消息*/
	.index-news-layout {
		.index-news-cont {
			grid-template-columns: repeat(2, minmax(0, 1fr));
		}
	}
	/*首頁推動成果*/
	.index-drive-results-layout {
		.index-drive-results {
			&-cont {
				grid-template-columns: repeat(2, minmax(0, 1fr));
				border-radius: 60px;
			}
			&-item {
				&:nth-child(1) {
					margin-bottom: 20px;
				}
				&:nth-child(2) {
					margin-bottom: 20px;
					border-right: 0;
				}
			}
		}
	}
	/*主題服務*/
	.index-theme-service-layout {
		.index-theme-service {
			&-cont {
				grid-template-columns: repeat(2, minmax(0, 1fr));
			}
			&-item {
				&:nth-child(1),
				&:nth-child(2) {
					grid-column: auto;
					grid-row: 1;
					background-position: right 40px top;
				}
				&:nth-child(3) {
					grid-column: 1 / 3;
					grid-row: 2;
					background-size: 40%;
				}
				&:nth-child(4),
				&:nth-child(5) {
					grid-column: auto;
					grid-row: 3;
					background-position: right bottom;
				}
			}
		}
	}
	/*推薦族語資源*/
	.index-recommended-resources-layout {
		.index-recommended-resources {
			&-menu {
				display: grid;
			}
			&-cont {
				grid-template-columns: 1fr;
			}
			&-menu {
				grid-template-columns: repeat(4, minmax(0, 1fr));
			}
			&-iframe {
				min-height: 600px;
			}
		}
	}
}
@media (max-width: 410px) {
	/*首頁最新消息*/
	.index-news-layout {
		.index-news-cont {
			grid-template-columns: repeat(1, minmax(0, 1fr));
		}
	}
	/*首頁推動成果*/
	.index-drive-results-layout {
		.index-drive-results {
			&-cont {
				grid-template-columns: repeat(1, minmax(0, 1fr));
			}
			&-item {
				&:nth-child(1),
				&:nth-child(2),
				&:nth-child(3) {
					margin-bottom: 10px;
					border-right: 0;
					border-bottom: 1px solid #202d3f;
				}
			}
		}
	}
	/*主題服務*/
	.index-theme-service-layout {
		.index-theme-service {
			&-cont {
				grid-template-columns: repeat(1, minmax(0, 1fr));
			}
			&-item {
				&:nth-child(1),
				&:nth-child(2),
				&:nth-child(3),
				&:nth-child(4),
				&:nth-child(5) {
					grid-column: auto;
					grid-row: auto;
				}
				&:nth-child(3) {
					background-size: 70%;
				}
				&:nth-child(4) {
					background-size: 80%;
				}
			}
		}
	}
	/*推薦族語資源*/
	.index-recommended-resources-layout {
		.index-recommended-resources {
			&-menu {
				grid-template-columns: repeat(2, minmax(0, 1fr));
			}
			&-iframe {
				min-height: 600px;
			}
			&-infor {
				&-item03 {
					.index-recommended-resources-infor-cont {
						padding: 40px 20px;
					}
				}

				&-item01,
				&-item02,
				&-item03,
				&-item04,
				&-item06 {
					.index-recommended-resources-infor-cont {
						display: grid;
						grid-template-columns: repeat(2, minmax(0, 1fr));
					}
				}
			}
		}
	}
}

.banner-layout {
  position: relative;
  height: 30%;

  .transition {
    transition: all 0.4s ease-in-out;
  }

  .carousel {
    .content {
      display: flex;

      .item {
        width: 100%;
        display: block;

        .img {
          width: 100%;
          display: block;
          background-size: cover;
          background-position: center;
          height: 0;
          padding-bottom: 30%;
        }

        .video {
          width: 100%;
          height: 0;
          padding-bottom: 30%;

          video {
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }

    .item {
      width: 100%;
      display: block;

      .img {
        width: 100%;
        display: block;
        background-size: cover;
        background-position: center;
        height: 0;
        padding-bottom: 50%;
      }
    }

    .ball {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: black;
      border: 2px solid;
      opacity: 0.5;

      &.visible {
        opacity: 1;
      }
    }

    .progress {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 5px;
      background: #ff5252;
    }

    .click-area {
      width: 50px;
      text-align: center;

      i {
        font-size: 3em;
      }
    }
  }
}

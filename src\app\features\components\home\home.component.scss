@use "./scss/index-list-layout.scss";
@use "./scss/news-layout.scss";
@use "./scss/promote-organization-layout.scss";

.index-word-cloud {
    width: 25%;
    @media (max-width: 768px) {
        width: 90%;
    }
}

.more-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    .tab-btn {
        display: flex;
        align-items: center;
        margin: 5px;
        padding: 10px 20px;
        color: #fff;
        background-color: #177691;
        border-radius: 60px;
        border: 0;
        font-size: 1.25em;
        font-weight: bold;
    }
}

.index-theme-service-cont {
    .index-theme-service-item {
        border: solid 1px #789e74;
        &:nth-child(2) {
            border-color: #5a9289;
        }
        &:nth-child(3) {
            border-color: #bd957d;
        }
        &:nth-child(4) {
            border-color: #c87979;
        }
        &:nth-child(5) {
            border-color: #e29667;
        }
    }
}

.container-with-carousel {
    // 1. 外部容器必須設定相對定位，以便按鈕能以絕對定位相對於它
    position: relative;
    // 保持您的原有樣式
    width: 100%;
    min-height: 500px;
    z-index: 1;
    // 3. 樣式化您的按鈕
    button {
        // 設定絕對定位，讓按鈕脫離文檔流
        position: absolute;
        // 將按鈕放置在左上角 (您可以根據需要調整數值)
        top: 10px; // 距離頂部 10px
        left: 10px; // 距離左側 10px
        // 確保按鈕在輪播元件的上方
        z-index: 10;
        // (可選) 讓按鈕更醒目或透明化，以便不遮擋輪播內容
        opacity: 0.9;
        transition: opacity 0.2s ease-in-out; // 增加視覺效果
    }
}

// 輪播圖片和影片樣式
.slide-content {
    width: 100%;
    max-width: 1154px; // 最大寬度限制
    border-radius: 10px;
    overflow: visible; // 改為 visible，讓文字可以顯示在容器外
    display: flex;
    flex-direction: column; // 垂直排列
    align-items: center; // 置中對齊
    position: relative; // 相對定位，讓 span 的絕對定位相對於此容器
    img,
    video {
        flex: 0 0 auto; // 不自動伸縮，保持原始尺寸
        width: 100%; // 寬度填滿容器
        max-width: 1154px; // 最大寬度限制
        aspect-ratio: 16 / 9; // 保持 16:9 比例
        max-height: 649px; // 最大高度限制
        object-fit: cover; // 確保圖片填滿容器且不變形
        object-position: center; // 置中對齊
        border-radius: 10px;
    }

    span {
        position: absolute; // 絕對定位
        z-index: 10; // 確保文字在圖片/影片上方
        pointer-events: none; // 不阻擋點擊事件

        width: 100%;
        color: #fff;
        background: rgba(0, 0, 0, 0.82);
        padding: 10px;
        bottom: 10px;
        text-align: center;
        font-size: 1.25em;
        box-sizing: border-box;
    }
}

.left-carousel-style {
    display: flex;
    position: absolute;
    z-index: 5;
    width: calc(var(--carousel-mask-width, 500px) + 5px);
    height: 100%;
    backdrop-filter: blur(3px);
    background: linear-gradient(90deg, rgba(255, 255, 255, 1) 47%, rgba(255, 255, 255, 0) 100%);
}
.right-carousel-style {
    display: flex;
    position: absolute;
    right: 0;
    z-index: 5;
    width: var(--carousel-mask-width, 500px); // 使用 CSS 變數，預設值為 500px
    height: 100%;
    backdrop-filter: blur(3px);
    background: linear-gradient(270deg, rgba(255, 255, 255, 1) 47%, rgba(255, 255, 255, 0) 100%);
}


@media (max-width: 899px) {
    .left-carousel-style,
    .right-carousel-style {
        display: none;
    }
}

// 登入提示卡片樣式
.login-prompt-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 96vh;
    padding: 2rem;

}

.login-prompt-card {
    max-width: 450px;
    width: 100%;
    padding: 2.5rem;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    background: #ffffff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    }

    mat-card-content {
        padding: 0;
    }
}

.login-prompt-icon {
    margin-bottom: 1.5rem;

    .material-symbols-outlined {
        font-size: 64px;
        color: #177691;
        display: inline-block;
        animation: pulse 2s ease-in-out infinite;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.login-prompt-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin: 0 0 1rem 0;
    letter-spacing: 0.5px;
}

.login-prompt-message {
    font-size: 1rem;
    color: #666;
    margin: 0 0 2rem 0;
    line-height: 1.6;
}

.login-prompt-button {
    padding: 12px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(23, 118, 145, 0.3);

    .material-symbols-outlined {
        font-size: 20px;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(23, 118, 145, 0.4);
    }

    &:active {
        transform: translateY(0);
    }
}

// 響應式設計
@media (max-width: 768px) {
    .login-prompt-container {
        min-height: 50vh;
        padding: 1rem;
    }

    .login-prompt-card {
        padding: 2rem 1.5rem;
    }

    .login-prompt-icon .material-symbols-outlined {
        font-size: 48px;
    }

    .login-prompt-title {
        font-size: 1.5rem;
    }

    .login-prompt-message {
        font-size: 0.9rem;
    }

    .login-prompt-button {
        padding: 10px 24px;
        font-size: 1rem;
    }
}
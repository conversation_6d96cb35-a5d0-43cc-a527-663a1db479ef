@use "./scss/index-list-layout.scss";
@use "./scss/news-layout.scss";
@use "./scss/promote-organization-layout.scss";

.index-word-cloud {
    width: 25%;
    @media (max-width: 768px) {
        width: 90%;
    }
}

.more-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    .tab-btn {
        display: flex;
        align-items: center;
        margin: 5px;
        padding: 10px 20px;
        color: #fff;
        background-color: #177691;
        border-radius: 60px;
        border: 0;
        font-size: 1.25em;
        font-weight: bold;
    }
}

.index-theme-service-cont {
    .index-theme-service-item {
        border: solid 1px #789e74;
        &:nth-child(2) {
            border-color: #5a9289;
        }
        &:nth-child(3) {
            border-color: #bd957d;
        }
        &:nth-child(4) {
            border-color: #c87979;
        }
        &:nth-child(5) {
            border-color: #e29667;
        }
    }
}

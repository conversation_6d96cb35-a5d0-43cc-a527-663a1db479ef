.popup-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em 0;
    border-bottom: 1px solid #ccc;
  }

  .menuSet-layout {
    display: flex;
    height: calc(100% - 53px);
    align-items: flex-start;
    justify-content: center;

    .tree {
      min-width: 250px;
      border-right: 1px solid #ccc;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      height: 100%;

      span {
        padding: 1em;
        cursor: pointer;

        &:hover {
          font-weight: bold;
          text-decoration: underline;
        }

        &.selected {
          font-weight: bold;
        }
      }
    }

    .contents {
      width: 100%;
      padding: 0 1em;
      position: relative;
      overflow: hidden;
      height: 100%;

      .space-between {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .content-blocks {
          display: flex;
          flex-wrap: wrap;
          overflow-y: auto;

          .tags {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            overflow: hidden;

            button {
              border: 1px solid #ccc;
              background-color: #ffffff;
              border-radius: 15px;
              cursor: pointer;
              outline: 0;
              padding: 5px;
              margin: 5px;
              transition: 0.3s ease-in-out;

              &.selected {
                background-color: #ccc;
              }

              &:hover {
                border-color: #989898;
              }
            }
          }

          .block {
            width: 100px;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 5px;
            padding: 5px;
            box-shadow: 0 0 3px #ccc;
            font-size: 10px;
            border: 1px solid #ccc;
            position: relative;
            transition: 0.3s ease-in-out;
            cursor: pointer;

            .copy-btn {
              position: absolute;
              top: 0;
              right: 0;
            }

            &.selected {
              border: 1px solid #ff0000;
            }

            &.add {
              background-color: #b8d3f3;
            }

            &:hover {
              box-shadow: 0 0 3px #000000;
            }

            .cover {
              width: 100px;
              height: 100px;
              background-image: url("/assets/images/gallery-img.jpeg");
              background-repeat: no-repeat;
              background-position: center;
              background-size: cover;
              border: 1px solid #ccc;
            }

            .name {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 100%;
            }
          }
        }

        .close-btn {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 10px 0;

          button {
            margin: 0 10px;
          }
        }
      }
    }
  }
}

.icon-group {
  display: flex;
  flex-direction: row;
  padding: 5px;
  gap: 5px;
}

import { Component } from '@angular/core';
import { HtmlService } from '../../../core/services/html.service';
import { ActivatedRoute, Router } from '@angular/router';
import { getHtmlResp } from '../../../interface/html.interface';
import Swal from 'sweetalert2';
import {
  createUpdateResp,
  defaultItem,
} from '../../../interface/share.interface';
import DOMPurify from 'dompurify';
import { ApprovalStatus } from '../../../enum/share.enum';
import { HttpErrorResponse } from '@angular/common/http';
@Component({
  selector: 'app-html',
  standalone: false,

  templateUrl: './html.component.html',
  styleUrl: './html.component.scss',
})
export class HtmlComponent {
  loading: boolean = false;
  title: string = '';
  menuItemId: string = '';
  content: string = '';
  creater = '';
  editor: string = '';

  levelDecision: number = 2;
  isPendingApproval: boolean = false;
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  constructor(
    private htmlService: HtmlService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {
    this.activatedRoute.url.subscribe(() => {
      this.menuItemId = this.activatedRoute.snapshot.params['menuItemId'];
      this.get();
    });
  }

  get() {
    this.loading = true;
    this.htmlService.getHtml(this.menuItemId).subscribe({
      next: (resp: getHtmlResp) => {
        this.loading = false;
        if (resp.code === 200) {
          this.creater = resp.data.creater;
          this.editor = resp.data.editor;
          this.title = resp.data.title;
          this.creater = resp.data.creater;
          this.editor = resp.data.editor;
          this.content = resp.data.content;
          this.levelDecision = resp.data.levelDecision;
          this.isPendingApproval = resp.data.isPendingApproval;
          this.reason = resp.data.reason;
          this.reviewFileName = resp.data.reviewFileName;
          this.reviewFileUrl = resp.data.reviewFileUrl;
        } else {
          Swal.fire('失敗', `${resp.message}`, 'error');
        }
      },
      error: () => {
        this.loading = false;
        Swal.fire('失敗', '取得失敗', 'error');
      },
    });
  }

  save() {
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,

      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        this.loading = true;
        this.htmlService
          .createHtml(
            this.menuItemId,
            this.content,
            this.levelDecision
          )
          .subscribe({
            next: (resp: createUpdateResp) => {
              this.loading = false;
              resp.code === 200
                ? Swal.fire('成功', '儲存成功', 'success').then(() => {
                    this.get();
                  })
                : Swal.fire('失敗', resp.message, 'error');
            },
            error: (err: HttpErrorResponse) => {
              this.loading = false;
              Swal.fire('失敗', err.error.message, 'error');
            },
          });
      }
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          type: 'HTML',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        this.loading = true;
        this.htmlService
          .createHtml(
            this.menuItemId,
            this.content,
            this.levelDecision
          )
          .subscribe({
            next: (resp: createUpdateResp) => {
              this.loading = false;
              this.router.navigate(['manage/view'], {
                queryParams: {
                  menuItemId: this.menuItemId,
                  typeGroupId: resp.data,
                  type: 'HTML',
                  status: ApprovalStatus.BeforeApproval,
                },
              });
            },
            error: (err: HttpErrorResponse) => {
              this.loading = false;
              Swal.fire('失敗', err.error.message, 'error');
            },
          });
      }
    });
  }

  create() {
    this.loading = true;
  }
}

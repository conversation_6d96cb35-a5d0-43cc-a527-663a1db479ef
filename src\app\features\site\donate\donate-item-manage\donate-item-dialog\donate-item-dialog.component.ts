import { Component, Inject } from '@angular/core';
import { DonateService } from '../../../../../core/services/donate.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ShareService } from '../../../../../core/services/share.service';
import {
  addDonateItemReq,
  donateItem,
  updateDonateItemReq,
} from '../../../../../interface/donate.interface';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../../interface/share.interface';

@Component({
  selector: 'app-donate-item-dialog',
  standalone: false,

  templateUrl: './donate-item-dialog.component.html',
  styleUrl: './donate-item-dialog.component.scss',
})
export class DonateItemDialogComponent {
  donateItem: string = '';
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: donateItem;
    },
    private dialogRef: MatDialogRef<DonateItemDialogComponent>,
    private donateService: DonateService,
    private shareService: ShareService
  ) {
    if (this.data.data) {
      this.donateItem = this.data.data.donateItem;
    }
  }

  ngOnInit() {}

  submit() {
    if (this.donateItem) {
      if (this.data.data.id) {
        let status: string = '編輯';
        let req: updateDonateItemReq = {
          id: this.data.data.id,
          donateItem: this.donateItem,
          enable: this.data.data.enable,
        };

        this.donateService.updateDonateItem(req).subscribe({
          next: (resp: defaultItem) => {
            resp.code === 200
              ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
                  this.dialogRef.close();
                })
              : Swal.fire('錯誤', resp.message, 'error');
          },
        });
      } else {
        let status: string = '新增';
        let req: addDonateItemReq = {
          donateItem: this.donateItem,
        };

        this.donateService.addDonateItem(req).subscribe({
          next: (resp: defaultItem) => {
            resp.code === 200
              ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
                  this.dialogRef.close();
                })
              : Swal.fire('錯誤', resp.message, 'error');
          },
        });
      }
    } else {
      Swal.fire('警告', '請輸入捐款項目', 'warning');
    }
  }

  close() {
    this.dialogRef.close();
  }
}

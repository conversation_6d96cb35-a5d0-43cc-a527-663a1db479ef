import { Injectable } from '@angular/core';
import { News, NewsDocument } from '../../shared/models/news.model';
import { Observable } from 'rxjs';
import { PagingOfNews } from '../../shared/models/pagingOfNews.model';
import { HttpClient } from '@angular/common/http';
import { Gallery } from '../../shared/models/gallery.model';
import { Config } from './config';
import {
  createUpdateNewsReq,
  getNewsListReq,
  getNewsListResp,
  getNewsResp,
  getNewsUserGroupResp,
} from '../../interface/news.interface';
import { createUpdateResp, defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class NewsService {
  private mngNews: string = '/api/Manage/News';
  private mngGallery: string = '/api/Manage/Gallery';

  constructor(private httpClient: HttpClient) {}

  getType() {
    return this.httpClient.get('api/Manage/New_News/GetNew_NewsType');
  }
  getNewsUserGroup(): Observable<getNewsUserGroupResp> {
    return this.httpClient.get<getNewsUserGroupResp>(
      'api/Manage/New_News/GetNew_NewsUserGroup'
    );
  }

  getSchoolList(): Observable<string[]> {
    let websiteID = sessionStorage.getItem('webSiteId');
    return this.httpClient.get<string[]>(
      `api/Manage/UserData/current/${websiteID}/school`
    );
  }

  getNews(id: string): Observable<getNewsResp> {
    return this.httpClient.get<getNewsResp>(
      'api/Manage/New_News/GetNew_NewsData',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  getNewsList(req: getNewsListReq): Observable<getNewsListResp> {
    return this.httpClient.get<getNewsListResp>(
      'api/Manage/New_News/GetNew_NewList',
      {
        params: {
          ...req,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }

  deleteNews(id: string) {
    return this.httpClient.delete('api/Manage/New_News/DeleteNew_newsData', {
      params: {
        typeGroupId: id,
      },
    });
  }

  createUpdateNews(req: createUpdateNewsReq): Observable<createUpdateResp> {
    return this.httpClient.post<createUpdateResp>(
      'api/Manage/New_News/UpdateOrInsertNew_News',
      req
    );
  }

  ////////////////////////////////////////////////////OLD//////////////////////////////////////////////////////////////////
  /**
   * 完整結構更新或建立最新消息
   *
   * @param instance 最新消息實例
   */
  fullUpdateOrCreate(instance: News): Observable<News> {
    return this.httpClient.post<News>(`${this.mngNews}/full`, instance);
  }

  /**
   * 取得指定選單項目中的News列表
   *
   * @param menuItemId 選單項目唯一識別號
   * @param keyword 關鍵字
   * @param enable 過濾啟用停用
   * @param skip 起始索引
   * @param take 取得筆數
   */
  list2(
    menuItemId?: string,

    keyword?: string,

    enable?: boolean | null,

    skip: number = 0,

    take: number = 10
  ): Observable<PagingOfNews> {
    //不使用httpParams，保留原url字串拼接方法，因搜尋結果會不一樣
    let url = '/api/Manage/News';
    const queryList = [];
    console.log('keyword', keyword);
    if (menuItemId !== null && menuItemId !== undefined) {
      queryList.push('menuItemId=' + encodeURIComponent(menuItemId.toString()));
    }

    if (keyword !== null && keyword !== undefined) {
      queryList.push('keyword=' + encodeURIComponent(keyword.toString()));
      console.log('keyword', encodeURIComponent(keyword!.toString()));
    }

    if (enable !== null && enable !== undefined) {
      queryList.push('enable=' + encodeURIComponent(enable.toString()));
    }

    if (skip !== null && skip !== undefined) {
      queryList.push('skip=' + encodeURIComponent(skip.toString()));
    }

    if (take !== null && take !== undefined) {
      queryList.push('take=' + encodeURIComponent(take.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.httpClient.get<PagingOfNews>(url);
  }

  /**
   * 匯入最新消息
   *
   * @param file 檔案
   * @param MenuItemId
   */
  import(MenuItemId: string, file?: File): Observable<string> {
    return this.httpClient.post<string>(
      `${this.mngNews}/import/new/${MenuItemId}`,
      file
    );
  }

  /**
   * 刪除指定實例
   *
   * @param id 唯一識別號
   */
  delete(id: string): Observable<any> {
    return this.httpClient.delete<any>(`${this.mngNews}/${id}`);
  }

  /**
   * 刪除指定最新消息檔案
   *
   * @param id 最新消息唯一識別號
   * @param documentId 文件唯一識別號
   */
  deleteFiles(
    id: string,

    documentId: string
  ): Observable<any> {
    return this.httpClient.delete<any>(
      `${this.mngNews}/${id}/document/${documentId}`
    );
  }

  /**
   * 刪除指定實例
   *
   * @param menuItemId
   * @param enable
   */
  deleteGallery(id: string): Observable<News> {
    return this.httpClient.delete<News>(`${this.mngGallery}/${id}`);
  }

  /**
   * 取得指定實例
   *
   * @param menuItemId
   * @param enable
   */
  getMenuItemGalleryList(
    menuItemId?: string,

    enable?: boolean
  ): Observable<Gallery[]> {
    let url = '/api/Manage/Gallery';
    const queryList = [];

    if (menuItemId !== null && menuItemId !== undefined) {
      queryList.push('menuItemId=' + encodeURIComponent(menuItemId.toString()));
    }

    if (enable !== null && enable !== undefined) {
      queryList.push('enable=' + encodeURIComponent(enable.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.httpClient.get<Gallery[]>(url);
  }

  /**
   * 取得指定最新消息檔案庫
   *
   * @param id 最新消息唯一識別號
   * @param skip 起始索引
   * @param take 取得筆數
   */
  getFiles(
    id: string,

    skip: number = 0,

    take: number = 10
  ): Observable<NewsDocument[]> {
    return this.httpClient.get<NewsDocument[]>(
      `${this.mngNews}/${id}/document?skip=${skip}&take=${take}`
    );
  }

  /**
   * 上傳指定最新消息檔案庫
   *
   * @param id 最新消息唯一識別號
   * @param files 上傳檔案
   */
  uploadFiles(
    id: string,

    files?: File[]
  ): Observable<NewsDocument[]> {
    const formData = new FormData();
    if (files && files.length) {
      for (var item of files) {
        formData.append('files', item);
      }
    }

    return this.httpClient.post<NewsDocument[]>(
      `${this.mngNews}/${id}/document`,
      formData
    );
  }

  /**
   * 更新實例
   *
   * @param instance 實例內容
   */
  UploadGalleryFiles(instance: Gallery): Observable<Gallery> {
    return this.httpClient.post<Gallery>(`${this.mngGallery}`, instance);
  }

  /**
   * 取得指定實例
   *
   * @param id 唯一識別號
   */
  get2(id: string): Observable<News> {
    return this.httpClient.get<News>(`${this.mngNews}/${id}`);
  }
}

{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"2025wmg-Backstage": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false, "module": "app.module.ts"}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/2025wmg-backstage", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss", "./node_modules/froala-editor/css/froala_editor.pkgd.min.css", "./node_modules/froala-editor/css/froala_style.min.css"], "scripts": ["./node_modules/froala-editor/js/froala_editor.pkgd.min.js"]}, "configurations": {"production": {"outputPath": "dist/lrdf_backstage/prod", "budgets": [{"type": "initial", "maximumWarning": "200MB", "maximumError": "200MB"}, {"type": "anyComponentStyle", "maximumWarning": "200MB", "maximumError": "200MB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}]}, "development": {"outputPath": "dist/lrdf_backstage/dev", "optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "src/proxy.conf.json"}, "configurations": {"production": {"buildTarget": "2025wmg-Backstage:build:production"}, "development": {"buildTarget": "2025wmg-Backstage:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss", "./node_modules/froala-editor/css/froala_editor.pkgd.min.css", "./node_modules/froala-editor/css/froala_style.min.css"], "scripts": ["./node_modules/froala-editor/js/froala_editor.pkgd.min.js"]}}}}}, "cli": {"analytics": "9e66e820-5cd8-411e-b596-4b77931dcf7c"}}
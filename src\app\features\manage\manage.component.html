<div class="manage-layout">
  <div class="top-block" #top></div>
  <div class="manage-header-layout">
    <img class="logo" src="assets/images/major_logo.png" alt="logo" [routerLink]="['/manage/home']" />
    <ul class="manage-menu">
      <li class="border-r user" *ngIf="userData" (click)="editUserData(userData)">
        <i class="material-icons">person</i>
        <span>{{ userData.name }}</span>
      </li>
      <li class="border-r" (click)="changeLanguage()">
        <i class="material-icons">language</i>
        <span>{{LangText[lang]}}</span>
      </li>
      <li class="border-r" *ngIf="getPolicy('MenuItemManage')" (click)="openMenuSettingPopup()">
        <i class="material-icons">web</i>
        <span>選單</span>
      </li>
      <li class="border-r" *ngIf="getPolicy('ApprovalItemListManage')||getPolicy('ApprovalLevel2DecisionManage')"
        (click)="review()">
        <span class="material-symbols-outlined">
          contract_edit
        </span>
        <span>審核</span>
      </li>
      <li class="border-r" *ngIf="getPolicy('Donate')" [matMenuTriggerFor]="donateSetting">
        <span class="material-symbols-outlined">
          credit_card_heart
        </span>
        <span>捐款</span>
      </li>
      <li class="border-r" *ngIf="userData" [matMenuTriggerFor]="setting">
        <i class="material-icons">build</i>
        <span>設定</span>
      </li>
      <li class="border-r" *ngIf="getPolicy('AIQuestion')" [routerLink]="'/manage/customerService'">
        <i class="material-icons">support_agent</i>
        <span>智能客服</span>
      </li>
      <li class="border-r" *ngIf="!userData" (click)="login()">
        <i class="material-icons">login</i>
        <span>登入</span>
      </li>
      <li class="border-r" *ngIf="userData" (click)="logout()">
        <i class="material-icons">logout</i>
        <span>登出</span>
      </li>
      <li [matMenuTriggerFor]="menu">
        <i class="material-icons">menu</i>
      </li>
    </ul>
  </div>
  <div class="manage-contents" [ngStyle]="getBg()">
    <!-- <app-menu-item [menu]="menuItem" [style]="style"></app-menu-item> -->
    <div class="manage-main">
      <router-outlet></router-outlet>
    </div>
  </div>
  <div class="goTop" (click)="goTop()">
    <i class="material-icons">keyboard_arrow_up</i>
  </div>
  <!-- <app-loading [loading]="loading"></app-loading> -->
</div>

<mat-menu #setting="matMenu">
  <button mat-menu-item (click)="openRole()" *ngIf="getPolicy('RoleManage')">
    角色權限設置
  </button>
  <button mat-menu-item (click)="openUser()" *ngIf="getPolicy('UserManage')">
    使用者設置
  </button>
  <button mat-menu-item (click)="openGroup()" *ngIf="getPolicy('UserGroupManage')">
    群組設置
  </button>
  <button mat-menu-item *ngIf="userData" [routerLink]="'/manage/auditLog'">後台操作紀錄</button>
  <button mat-menu-item *ngIf="userData" [routerLink]="'/manage/clickLogList'">頁面點擊統計</button>
  <button mat-menu-item *ngIf="userData" [routerLink]="'/manage/searchKeywordLog'">搜尋關鍵字統計</button>
  <button mat-menu-item *ngIf="userData" [routerLink]="'/manage/guestViewLog'">訪客人次統計</button>
</mat-menu>
<mat-menu #donateSetting="matMenu">
  <button mat-menu-item [routerLink]="'/manage/donateItemManage'">
    項目管理
  </button>
  <button mat-menu-item [routerLink]="'/manage/donateBankDetail'">
    銀行明細
  </button>
  <button mat-menu-item [routerLink]="'/manage/donateAccountReconciliation'">
    銷帳作業
  </button>
</mat-menu>

<mat-menu #menu="matMenu">
  <button mat-menu-item *ngIf="userData" (click)="openFileBox()">檔案庫</button>
  <button mat-menu-item *ngIf="getPolicy('WordCloud')" (click)="textCloudSetting()">文字雲設定</button>
  <button mat-menu-item *ngIf="getPolicy('Achievements')" (click)="achievementsSetting()">推動成果設定</button>
  <button mat-menu-item *ngIf="getPolicy('SMTP')" (click)="smtpSetting()">審核信件SMTP設置</button>
  <button mat-menu-item *ngIf="getPolicy('SMTP')" (click)="eNewsLetterSmtpSetting()">電子報SMTP設置</button>
  <button mat-menu-item (click)="goForestUrl()">前台</button>


</mat-menu>
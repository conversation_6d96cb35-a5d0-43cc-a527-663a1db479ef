<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>電子報-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
            </div>
            <div class="add-layout">
                <button mat-flat-button (click)="addENewsletter()">+ 新增</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">出刊日期</th>
                            <th width="">名稱</th>
                            <th width="230px">狀態</th>
                            <th width="70px">開信率</th>
                            <th width="70px">點擊率</th>
                            <th width="80px">發送佇列</th>
                            <th width="120px">功能</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (item of eNewsletterList; track item) {
                        <tr>
                            <td data-label="出刊日期">{{item.e_NewsletterPublishDateTime|date:'yyyy-MM-dd'}}</td>
                            <td data-label="名稱">
                                {{item.name}}
                            </td>
                            <td data-label="狀態">
                                <span [ngClass]="statusClassMap[item.statusNum]">{{item.status}}</span>
                            </td>
                            <td data-label="開信率" class="text-center">
                                {{item.openRate}}
                            </td>
                            <td data-label="點擊率" class="text-center">
                                {{item.clickRate}}
                            </td>
                            <td data-label="發送佇列" class="center-column">
                                <i class="material-icons" (click)="viewColumn(item.typeGroupId)">visibility</i>
                            </td>
                            <td data-label="功能">
                                <i class="material-icons" [ngClass]="{ 'disabled':item.statusNum===2 }"
                                    (click)="editENewsletter(item)">edit</i>
                                <i class="material-icons" (click)="delete(item.typeGroupId)">delete</i>
                                <i class="material-icons"
                                    [ngClass]="{ 'disabled':item.statusNum!==3&&item.statusNum!==5 }"
                                    (click)="sendManage(item)">mail</i>
                            </td>
                            <!-- //1未送審 2已送審  3審核通過未寄信 4審核未通過 5已寄信 -->
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="7" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
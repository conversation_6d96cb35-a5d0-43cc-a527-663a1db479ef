import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UserData } from '../../shared/models/userData.model';
import {
  bindingGoogleResp,
  googleLoginResp,
  LoginInfo,
  unBindingGoogleResp,
} from '../../shared/models/loginInfo.model';
import { PagingOfUserData } from '../../shared/models/pagingOfUserData.model';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  private userDataApi: string = '/api/UserData';
  private mngUserDataApi: string = '/api/Manage/UserData';

  constructor(private http: HttpClient) {}

  /**
   * 是否為指定網站的管理員
   *
   * @param webSiteId 指定網站的唯一識別號
   */
  isManager(webSiteId: string): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.userDataApi}/me/${webSiteId}/IsManager`
    );
  }

  /**
   * 取得目前使用者實例
   *
   */
  get2(): Observable<UserData> {
    return this.http.get<UserData>(`${this.mngUserDataApi}/me`);
  }

  /**
   * 判斷目前登入使用者是否為系統管理員或高級管理員
   *
   */
  isAdministratorOrSeniorManager(): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.mngUserDataApi}/current/isAdministratorOrSeniorManager`
    );
  }

  /**
   * 登入並取得存取權杖
   *
   * @param webSiteId 子網站
   * @param loginInfo 登入資訊
   */
  login4(loginInfo: LoginInfo, webSiteId?: string): Observable<string> {
    return this.http.post<string>(
      `${this.userDataApi}/login?webSiteId=${webSiteId}`,
      loginInfo
    );
  }

  /**
   * 取得目前的角色功能權限列表
   *
   */
  getUserFunctionPolicy(): Observable<string[]> {
    return this.http.get<string[]>(
      `${this.mngUserDataApi}/current/functionPolicy`
    );
  }

  /**
   * 取得目前的角色選單權限列表，如為null則表示為系統管理員
   *
   */
  getUserMenuPolicy(): Observable<string[]> {
    return this.http.get<string[]>(`${this.mngUserDataApi}/current/menuPolicy`);
  }

  /**
   * 取得網站帳號機關列表
   *
   */
  getUserSchool(webSiteId: string): Observable<string[]> {
    return this.http.get<string[]>(
      `${this.mngUserDataApi}/current/${webSiteId}/school`
    );
  }

  /**
   * 更新使用者
   *
   * @param instance 使用者資訊
   */
  updateUserPassword(instance: UserData): Observable<boolean> {
    return this.http.put<boolean>(`${this.mngUserDataApi}/password`, instance);
  }

  /**
   * 檢驗帳號是否已經存在
   *
   * @param webSiteId 子網站唯一識別號
   * @param account 使用者帳號
   */
  exists(webSiteId: string, account: string): Observable<boolean> {
    const headers = new HttpHeaders().set('X-User-Account', account);
    return this.http.get<boolean>(
      `${this.mngUserDataApi}/${webSiteId}/exists/${account}`,
      { headers }
    );
  }

  /**
   * 取得指定子網站使用者列表
   *
   * @param webSiteId 子網站唯一識別號，如為null則表示取得超級管理者列表
   * @param keyword 關鍵字
   * @param keyword_property 關鍵字搜尋屬性
   * @param match 匹配模式
   * @param order 排序
   * @param skip 起始索引
   * @param take 取得筆數
   */
  list(
    webSiteId?: string,

    keyword?: string,

    keyword_property?: string[],

    match?: string | null,

    order?: string[] | null,

    skip: number = 0,

    take: number = 10
  ): Observable<PagingOfUserData> {
    let url = '/api/Manage/UserData';
    const queryList = [];

    if (webSiteId !== null && webSiteId !== undefined) {
      queryList.push('webSiteId=' + encodeURIComponent(webSiteId.toString()));
    }

    if (keyword !== null && keyword !== undefined) {
      queryList.push('keyword=' + encodeURIComponent(keyword.toString()));
    }

    if (keyword_property !== null && keyword_property !== undefined) {
      for (const item of keyword_property) {
        if (item) {
          queryList.push(
            'keyword_property=' + encodeURIComponent(item.toString())
          );
        }
      }
    }

    if (match !== null && match !== undefined) {
      queryList.push('match=' + encodeURIComponent(match.toString()));
    }

    if (order !== null && order !== undefined) {
      for (const item of order) {
        if (item) {
          queryList.push('order=' + encodeURIComponent(item.toString()));
        }
      }
    }

    if (skip !== null && skip !== undefined) {
      queryList.push('skip=' + encodeURIComponent(skip.toString()));
    }

    if (take !== null && take !== undefined) {
      queryList.push('take=' + encodeURIComponent(take.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<PagingOfUserData>(url);
  }

  /**
   * 更新使用者
   *
   * @param instance 使用者資訊
   */
  update22(instance: UserData): Observable<UserData> {
    return this.http.put<UserData>(this.mngUserDataApi, instance);
  }

  /**
   * 匯入使用者
   *
   * @param file 檔案
   * @param webSiteId
   */
  import(webSiteId: string, file?: any): Observable<defaultItem> {
    const formData = new FormData();

    formData.append('file', file);

    return this.http.post<defaultItem>(
      `${this.mngUserDataApi}/import/userdata/${webSiteId}`,
      formData
    );
  }

  /**
   * 建立新使用者
   *
   * @param instance 使用者資訊
   */
  create(instance: UserData): Observable<UserData> {
    return this.http.post<UserData>(this.mngUserDataApi, instance);
  }

  /**
   * 刪除指定使用者
   *
   * @param id 使用者唯一識別號
   */
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${this.mngUserDataApi}/${id}`);
  }

  /**
   * 雙因子登入取得驗證碼
   * @param webSiteId
   * @param req
   * @returns
   */
  loginSendEmailCode(
    webSiteId: string,
    req: {
      account: string;
      reCAPTCHA: string;
      password: string;
    }
  ): Observable<{ code: number; message: string; data: string }> {
    return this.http.post<{ code: number; message: string; data: string }>(
      `api/UserData/LoginSendEmailCode?webSiteId=${webSiteId}`,
      req
    );
  }

  /**
   * 雙因子登入
   * @param code
   * @param id
   * @returns
   */
  confirm2FALogin(code: string, id: string): Observable<string> {
    return this.http.get<string>(
      `api/UserData/CheckLoginCode?code=${code}&userId=${id}`
    );
  }

  /**
   * google登入
   * @param accessToken
   * @returns
   */
  googleLogin(accessToken: string): Observable<googleLoginResp> {
    return this.http.get<googleLoginResp>(`api/UserData/LoginByGoogle`, {
      params: {
        accessToken: accessToken,
      },
    });
  }

  /**
   * google綁定
   * @param accessToken
   * @returns
   */
  bindingGoogle(accessToken: string): Observable<bindingGoogleResp> {
    return this.http.post<bindingGoogleResp>(`api/Manage/WebSite/bindGoogle`, {
      accessToken: accessToken,
    });
  }

  /**
   * google解除綁定
   * @param userId
   * @returns
   */
  unBindingGoogle(userId: string): Observable<unBindingGoogleResp> {
    return this.http.post<unBindingGoogleResp>(
      `api/Manage/WebSite/disConnectGoogle`,
      {
        userId: userId,
      }
    );
  }
}

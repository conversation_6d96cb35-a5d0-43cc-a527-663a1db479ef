import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  approvalReq,
  batchApprovalReq,
  getReviewListReq,
  getReviewListResp,
} from '../../interface/review.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ReviewService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得審核列表
   * @param req
   * @returns
   */
  getReviewList(req: getReviewListReq): Observable<getReviewListResp> {
    return this.httpClient.post<getReviewListResp>(
      'api/Manage/Approval/ApprovalItemList',
      req
    );
  }

  /**
   * 是否啟用
   * @param req
   * @returns
   */
  activeReview(req: { itemId: string; type: string }): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Approval/ApprovalItem',
      req
    );
  }

  /**
   * 送審
   * @param req
   * @returns
   */
  submitForReview(req: {
    typeGroupId: string;
    menuItemType: string;
  }): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Approval/SendApproval',
      req
    );
  }

  /**
   * 審核
   * @param req
   * @returns
   */
  approval(req: approvalReq) {
    let formData = new FormData();
    formData.append('typeGroupId', req.typeGroupId);
    formData.append('approval', req.approval.toString());
    formData.append('menuItemType', req.menuItemType);
    formData.append('reason', req.reason);
    if (req.file) {
      formData.append('file', req.file);
    }
    return this.httpClient.post<defaultItem>(
      'api/Manage/Approval/Approval',
      formData
    );
  }

  /**
   * 批次核准
   * @param req
   * @returns
   */
  batchApproval(req: batchApprovalReq) {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Approval/BatchApproval',
      req
    );
  }

  /**
   *  發布
   * @param req
   * @returns
   */
  publish(req: { typeGroupId: string; menuItemType: string }) {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Approval/Release',
      req
    );
  }
}

/* You can add global styles to this file, and also import other style files */
@import "./assets/css/accessibility.css";

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background: #ffffff;
  font-family: Microsoft JhengHei;
}

// hm-tree-node {
//   ul {
//     padding-left: 1em;
//   }
// }

ul {
  padding: 0;
  margin: 0;
}

li {
  list-style-type: none;
}

.text_c {
  text-align: center;
}

/* width */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.ngx-contextmenu .dropdown-menu {
  background-color: #ffffff;
  box-shadow: 0px 0px 10px #8a8a8a;
  padding: 0;
}

.ngx-contextmenu li {
  display: block;
  border-top: 1px solid #ccc;
}

.ngx-contextmenu li:first-child {
  border-top: none;
}

.ngx-contextmenu a {
  color: #000000;
  display: flex;
  align-items: center;
  padding: 0.5em 1em;
  text-decoration: none;
  transition: 0.3s ease-in-out;
}

.ngx-contextmenu a:hover {
  color: #3f51b5;
  background-color: #cccccc80;
}

.mat-tab-group {
  font-family: Microsoft JhengHei;
}

.mat-mdc-menu-panel {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 0 !important;
  border-top: 4px solid #2eaddb;
}

.homePageTitle {
  display: flex;
  align-items: center;

  img {
    width: 50px;
    margin: 0 10px;
  }
}

/* in-flight clone */
.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
  pointer-events: none;
}

/* high-performance display:none; helper */
.gu-hide {
  left: -9999px !important;
}

/* added to mirrorContainer (default = body) while dragging */
.gu-unselectable {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* added to the source element while its mirror is dragged */
.gu-transit {
  opacity: 0.2;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
  filter: alpha(opacity=20);
}

.mat-tree-node {
  cursor: pointer;
}

app-news-list {
  .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: transparent;
  }
}

import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import Swal from 'sweetalert2';
import { defaultItem } from '../../interface/share.interface';
import { Router } from '@angular/router';

@Component({
  selector: 'app-change-pw',
  standalone: false,

  templateUrl: './change-pw.component.html',
  styleUrl: './change-pw.component.scss',
})
export class ChangePwComponent {
  form: FormGroup;
  constructor(
    private formBuilder: FormBuilder,
    private httpClient: HttpClient,
    private router: Router
  ) {
    this.form = this.formBuilder.group({
      hamaA: ['', Validators.required],
      email: ['', Validators.required],
    });
  }
  submit() {
    if (!this.form.valid) {
      Swal.fire('', '未填寫完整', 'error');
    } else {
      this.httpClient
        .post<defaultItem>('api/Manage/UserData/SendForgetPassWordMail', {
          mail: this.form.value.email,
          websiteId: sessionStorage.getItem('webSiteId'),
          account: this.form.value.hamaA,
        })
        .subscribe({
          next: (resp: defaultItem) => {
            Swal.fire('', resp.message, 'success').then(() => {
              this.router.navigate(['manage/home']);
            });
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('', err.error.message, 'error');
          },
        });
    }
  }
}

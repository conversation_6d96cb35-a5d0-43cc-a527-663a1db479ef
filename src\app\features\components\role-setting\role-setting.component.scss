.main-layout {
  height: 100%;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      display: flex;
      align-items: center;
    }

    .close {
      font-size: 40px;
      color: #7f7f7f;
      cursor: pointer;

      &:hover {
        color: #000;
      }
    }
  }

  .role-layout {
    height: 100%;
    overflow-y: auto;
    position: relative;

    .role-block {
      display: flex;
      flex-direction: column;
      margin: 5px;
      transition: 0.3s ease-in-out;

      &:hover {
        box-shadow: 0 0 10px #ccc;
      }

      .role-name {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        border: 1px solid #ccc;
        cursor: pointer;

        &.add {
          justify-content: center;
          align-items: center;
          background-color: #b8d3f3;
        }
      }

      .role-menu,
      .role-function {
        border-bottom: 1px solid #ccc;
        position: relative;
        min-height: 100px;
        display: flex;
        flex-direction: column;

        .zoomout {
          text-align: center;

          .material-icons {
            cursor: pointer;
          }
        }
      }

      .role-function {
        padding: 0 1em;
        line-height: 2;
      }
    }
  }
}

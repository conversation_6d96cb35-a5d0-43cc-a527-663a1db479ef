import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PagingOfLoginHistoryItem } from '../../shared/models/pagingOfLoginHistoryItem.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { PerDayLoginCount } from '../../shared/models/perDayLoginCount.model';

@Injectable({
  providedIn: 'root'
})
export class AnalysisService {
  private analysisApi: string = '/api/Analysis';
  private mngAnalysisApi: string = '/api/Manage/Analysis';

  constructor(
    private http: HttpClient
  ) { }

  /**
     * 取得指定使用者失敗登入次數總數
     *
     * @param userId 使用者唯一識別號
     */
  getUserFailLoginCount(
    userId: string,
    webSiteId?: string
  ): Observable<number> {
    let httpParams = new HttpParams();

    if (webSiteId) {
      httpParams = httpParams.set('webSiteId', webSiteId);
    }

    return this.http.get<number>(`${this.analysisApi}/User/{userId}/totalLoginCount?userId=${userId}`, { params: httpParams });


  }

  /**
       * 取得指定使用者的登入歷史
       *
       * @param userId 使用者唯一識別號
       * @param skip 起始索引
       * @param take 取得筆數
       */
  getUserLastLoginHistory(
    userId: string,
    webSiteId?: string,
    skip: number = 0,

    take: number = 10
  ): Observable<PagingOfLoginHistoryItem> {
    let httpParams = new HttpParams();
    if (webSiteId) {
      httpParams = httpParams.set('webSiteId', webSiteId);
    }

    if (skip) {
      httpParams = httpParams.set('skip', skip);
    }

    if (take) {
      httpParams = httpParams.set('take', take);
    }
    return this.http.get<PagingOfLoginHistoryItem>(`${this.analysisApi}/User/{userId}/history?userId=${userId}`, { params: httpParams });
  }

  /**
     * 取得指定使用者登入次數總數
     *
     * @param userId 使用者唯一識別號
     */
  getUserLoginCount(
    userId: string
  ): Observable<number> {
    return this.http.get<number>(`${this.mngAnalysisApi}/User/${userId}/totalLoginCount`);
  }

  /**
     * 取得指定使用者在指定時間區間每天登入次數
     *
     * @param userId 使用者唯一識別號
     * @param startTime
     * @param endTime
     */
  getUserPerDayLoginCount(
    userId: string,

    startTime?: number,

    endTime?: number
  ): Observable<PerDayLoginCount[]> {
    let url = '/api/Manage/Analysis/User/{userId}/perDayLoginCount';

    url = url.replace('{userId}', (userId).toString());
    const queryList = [];

    if (startTime !== null && startTime !== undefined) {
      queryList.push('startTime=' + encodeURIComponent(startTime.toString()));
    }

    if (endTime !== null && endTime !== undefined) {
      queryList.push('endTime=' + encodeURIComponent(endTime.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<PerDayLoginCount[]>(url);
  }
}

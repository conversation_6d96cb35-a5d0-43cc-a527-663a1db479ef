import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { ShareService } from '../../../core/services/share.service';
import { setSortReq, defaultItem } from '../../../interface/share.interface';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { textCloudItem } from '../../../interface/textCloud.interface';
import { VideoService } from '../../../core/services/video.service';
import { EbookService } from '../../../core/services/ebook.service';

@Component({
  selector: 'app-set-sort-dialog',
  standalone: false,

  templateUrl: './set-sort-dialog.component.html',
  styleUrl: './set-sort-dialog.component.scss'
})
export class SetSortDialogComponent {
  loading: boolean = false;
  sort?: number;
  maxSort: number = 0;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      typeGroupId: string, menuitemId: string, sort: number, totalCount: number,
      type: string;
    },
    public dialogRef: MatDialogRef<DialogComponent>,
    private shareService: ShareService,
    private videoService: VideoService,
    private ebookService: EbookService,
  ) {
  }

  ngOnInit(): void {
    console.log(this.data);
    this.sort = this.data.sort;
    this.maxSort = this.data.totalCount;
  }


  submit() {
    switch (this.data.type) {
      case 'book':
        this.setBook();
        break;
      case 'video':
        this.setVideo();
        break;
      default:
        this.setList();
        break;
    }

  }

  setList() {
    let req: setSortReq = {
      typeGroupId: this.data.typeGroupId,
      menuitemId: this.data.menuitemId,
      sort: this.sort as number,
    };
    this.loading = true;
    this.shareService.setSort(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '排序更新成功', 'success').then(() => {
            this.dialogRef.close();
          });

        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', '連線伺服器失敗，請稍後再試', 'error');
      },
    });
  }

  setBook() {
    let req: setSortReq = {
      typeGroupId: this.data.typeGroupId,
      menuitemId: this.data.menuitemId,
      sort: this.sort as number,
    };
    this.loading = true;
    this.ebookService.setSort(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '排序更新成功', 'success').then(() => {
            this.dialogRef.close();
          });

        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', '連線伺服器失敗，請稍後再試', 'error');
      },
    });
  }

  setVideo() {
    let req: setSortReq = {
      typeGroupId: this.data.typeGroupId,
      menuitemId: this.data.menuitemId,
      sort: this.sort as number,
    };
    this.loading = true;
    this.videoService.setSort(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '排序更新成功', 'success').then(() => {
            this.dialogRef.close();
          });

        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', '連線伺服器失敗，請稍後再試', 'error');
      },
    });
  }
}

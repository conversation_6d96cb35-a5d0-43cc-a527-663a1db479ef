<!-- <context-menu>
  <ng-template contextMenuItem (execute)="edit($event.item)">
    編輯
  </ng-template>
  <ng-template contextMenuItem (execute)="menuRole($event.item)">
    選單權限
  </ng-template>
  <ng-template contextMenuItem (execute)="functionRole($event.item)">
    功能權限
  </ng-template>
  <ng-template contextMenuItem (execute)="delete($event.item)">
    刪除
  </ng-template>
</context-menu> -->
<mat-menu #contextMenu="matMenu">
  <button mat-menu-item (click)="edit()">編輯</button>
  <button mat-menu-item (click)="menuRole()">選單權限</button>
  <button mat-menu-item (click)="functionRole()">功能權限</button>
  <button c mat-menu-item (click)="delete()">刪除</button>
</mat-menu>
<div
  style="visibility: hidden; position: fixed"
  [style.left]="contextMenuPosition.x"
  [style.top]="contextMenuPosition.y"
  [matMenuTriggerFor]="contextMenu"
></div>

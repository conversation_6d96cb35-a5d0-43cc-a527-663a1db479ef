.user-layout {
  position: relative;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      display: flex;
      align-items: center;
    }

    .close {
      font-size: 40px;
      color: #7f7f7f;
      cursor: pointer;

      &:hover {
        color: #000;
      }
    }
  }

  .user-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;

    .search {
      display: flex;
      align-items: center;

      input {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        padding: 0 10px;
        width: 200px;

        &:focus {
          border: 1px solid #3f51b5;
        }
      }
    }

    .btns {
      button {
        margin: 0 5px;
      }
    }
  }

  .user-list {
    height: 100%;
    overflow-y: auto;

    .user-block {
      display: flex;
      flex-direction: column;
      margin: 5px;
      transition: 0.3s ease-in-out;

      &:hover {
        box-shadow: 0 0 10px #ccc;
      }

      .user-name {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        border: 1px solid #ccc;
        cursor: pointer;

        &.add {
          justify-content: center;
          align-items: center;
          background-color: #b8d3f3;
        }
      }

      .user-menu,
      .user-function {
        border-bottom: 1px solid #ccc;
        position: relative;
        min-height: 100px;
        display: flex;
        flex-direction: column;

        .zoomout {
          text-align: center;

          .material-icons {
            cursor: pointer;
          }
        }
      }

      .user-function {
        padding: 0 1em;
        line-height: 2;
      }
    }
  }

  .lazyload {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

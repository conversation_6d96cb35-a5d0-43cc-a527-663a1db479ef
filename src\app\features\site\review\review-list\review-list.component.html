<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>審核列表-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="review-status-group">
            <span class="review-status " [ngClass]="{ active: reviewStatus ==='' }"
                (click)="changeReviewStatus('')">全部</span>
            <span class="review-status" [ngClass]="{ active: reviewStatus === '1' }"
                (click)="changeReviewStatus('1')">審核中</span>
            <span class="review-status" [ngClass]="{ active: reviewStatus === '2' }"
                (click)="changeReviewStatus('2')">已通過</span>
            <span class="review-status" [ngClass]="{ active: reviewStatus === '3' }"
                (click)="changeReviewStatus('3')">已退回</span>
        </div>
        <div class="user-search">
            <span>發布區間 :&nbsp;</span>
            <mat-form-field appearance="outline">
                <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startDate" (dateChange)="endDate = null" />
                <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
            <mat-form-field appearance="outline">
                <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endDate" [min]="startDate || null" />
                <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
            <span>審核層級 :&nbsp;</span>
            <mat-form-field appearance="outline">
                <mat-select class="input" [(ngModel)]="levelDecision" placeholder="請選擇審核層級">
                    @for (item of levelDecisionList; track item) {
                    <mat-option [value]="item.typeValue">
                        {{ item.typeName }}
                    </mat-option>
                    }
                </mat-select>
            </mat-form-field>
            &nbsp;
            <span>是否啟用 :&nbsp;</span>
            <mat-form-field appearance="outline">
                <mat-select [(ngModel)]="isActive" placeholder="請選擇是否啟用">
                    <mat-option [value]="true">啟用</mat-option>
                    <mat-option [value]="false">停用</mat-option>
                </mat-select>
            </mat-form-field>
            <div>
                <span>類型 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="type" placeholder="請選擇類型">
                        @for ( item of typeList; track $index) {
                        <mat-option [value]="item.typeValue">{{item.typeName}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>&nbsp;
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keywordtitle">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
            <div style="display: flex;justify-content: end;">
                @if(reviewStatus === '1'){
                <button mat-flat-button (click)="batchApproval()">批次核准</button>
                }
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            @if(reviewStatus === '1'){
                            <th width="60px"><input type="checkbox" id="select-all" (change)="selectAll($event)"
                                    [checked]="selectAllStatus">全選</th>
                            }
                            <th width="100px">日期</th>
                            <th width="70px">類型</th>
                            <th width="">名稱</th>
                            <th width="70px">審核層級</th>
                            <th width="70px">審核狀態</th>
                            <th width="70px">是否啟用</th>
                            <th width="60px">審核</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (item of reviewList; track item) {
                        <tr>
                            @if(reviewStatus === '1'){
                            <td data-label="選取"><input type="checkbox" (change)="changeSelect($event,item)" [checked]="isSelected({typeGroupId: item.typeGroupId,
                                menuItemType: item.menuType })">
                            </td>
                            }
                            <td data-label="日期">{{item.date|date:'yyyy/MM/dd'}}</td>
                            <td data-label="類型">{{item.menuTypeText}}</td>
                            <td data-label="名稱">
                                <a href="" (click)="goPage($event,item)">{{item.name}}</a>
                            </td>
                            <td data-label="審核層級">{{item.levelDecision}}</td>
                            <td data-label="審核狀態">
                                <span [ngClass]="statusClassMap[item.status]">{{item.status}}</span>
                            </td>
                            <td data-label="是否啟用">
                                <mat-slide-toggle class="example-margin" [checked]="item.enable"
                                    (change)="activeChange(item)">
                                </mat-slide-toggle>
                            </td>
                            <td data-label="審核">
                                <span class="review-icon" [ngClass]="{ 'disabled': isDisabled(item) }"
                                    (click)="goReiewPage(item)">
                                </span>
                            </td>
                        </tr>
                        }@empty {
                        <tr>
                            @if(reviewStatus === '1'){
                            <td colspan="8" style="text-align: center;">查無資料</td>
                            }@else{
                            <td colspan="7" style="text-align: center;">查無資料</td>
                            }
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
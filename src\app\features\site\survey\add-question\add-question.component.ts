import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { FieldType } from '../../../../enum/survey.enum';

@Component({
  selector: 'app-add-question',
  standalone: false,

  templateUrl: './add-question.component.html',
  styleUrl: './add-question.component.scss',
})
export class AddQuestionComponent {
  form: FormGroup;
  FieldType = FieldType;
  constructor(
    private dialogRef: MatDialogRef<AddQuestionComponent>,
    private _fb: FormBuilder
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      type: ['Input', Validators.required],
      required: [true, Validators.required],
    });
  }

  ngOnInit() {}

  submit(formVal: any) {
    this.dialogRef.close({
      fieldName: formVal.name,
      fieldType: formVal.type,
      required: formVal.required,
    });
  }

  close() {
    this.dialogRef.close();
  }
}

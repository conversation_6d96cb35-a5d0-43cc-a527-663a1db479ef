import { FileService } from '../../../../core/services/file.service';
import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  Inject,
} from '@angular/core';
import { createFileTage } from '../../../../shared/models/dialog.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-tag-setting',
  standalone: false,

  templateUrl: './tag-setting.component.html',
  styleUrl: './tag-setting.component.scss',
})
export class TagSettingComponent {
  tag: string = '';
  tagList: any = [];

  aspectRatio: any = 1 / 1;
  resizeToWidth = 0;
  resizeToHeight = 0;
  isnotCropper = false;
  imageChangedEvent: any = '';
  croppedImage: any = '';

  constructor(
    private _fileService: FileService,
    public dialogRef: MatDialogRef<createFileTage>,
    @Inject(MAT_DIALOG_DATA) public data: createFileTage
  ) {}

  ngOnInit() {
    //this.imageChangedEvent = this.popupData.fileEvent;
    //console.log('popupData:', this.data);
    this._fileService
      .getTags(sessionStorage.getItem('webSiteId')!, this.data.selector)
      .subscribe((x) => {
        this.tagList = x;
      });
  }

  selectTag(tag: string) {
    this.tag = tag;
  }

  changeResize() {
    if (this.isnotCropper) {
      this.getFileDimensions(this.imageChangedEvent.target.files[0])
        .then((dimensions) => {
          this.resizeToWidth = dimensions.width;
          this.resizeToHeight = dimensions.height;
          this.aspectRatio = dimensions.width / dimensions.height;
        })
        .catch((error) => {
          console.error('Error:', error);
        });
    }
  }

  submit() {
    // const originFile = this.imageChangedEvent.target.files[0];
    // const name = this.convertExtension(originFile.name, '.webp');

    // let file = this.convertBase64ToFile(this.croppedImage, name);
    // this.popupOutput.next({
    //   isSubmit: true,
    //   tag: this.tag,
    //   // webp: file
    // });

    const response = {
      isSubmit: true, // 假設標示用戶是否提交了資料
      tag: this.tag, // 傳遞 tags 資料
    };
    this.dialogRef.close(response);
  }

  close() {
    this.imageChangedEvent = null;
    this.croppedImage = null;

    this.dialogRef.close();
  }

  // 20231228 image cropper start

  // imageCropped(event: ImageCroppedEvent) {
  //   this.croppedImage = event.base64;
  // }

  convertBase64ToFile(base64String: string, fileName: string): File {
    const binaryString = window.atob(base64String.split(',')[1]);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);

    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const blob = new Blob([bytes], { type: 'application/octet-stream' });

    const file = new File([blob], fileName, {
      type: 'application/octet-stream',
    });

    return file;
  }

  convertExtension(fileName: string, newExtension: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');

    if (lastDotIndex !== -1) {
      const baseName = fileName.substring(0, lastDotIndex);
      const newFileName = `${baseName}${newExtension}`;
      return newFileName;
    } else {
      return fileName;
    }
  }

  getFileDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e: Event) => {
        const image = new Image();

        image.onload = () => {
          const dimensions = {
            width: image.width,
            height: image.height,
          };

          resolve(dimensions);
        };

        image.onerror = (error) => {
          reject(error);
        };

        image.src = reader.result as string;
      };

      reader.readAsDataURL(file);
    });
  }
}

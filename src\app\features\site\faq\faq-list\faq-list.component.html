<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1>{{ title }}</h1>
        <!-- 搜索區域 -->
        <div class="search-section">
            <div class="search-container">
                <div class="search-title">
                    <i class="material-icons">search</i>
                    <span>搜索條件</span>
                </div>
                <div class="search-form">
                    <div class="search-field">
                        <mat-form-field appearance="outline" class="search-input">
                            <mat-label>關鍵字搜索</mat-label>
                            <input matInput type="text" [(ngModel)]="keyword" placeholder="請輸入問題關鍵字">
                            <mat-icon matSuffix>search</mat-icon>
                        </mat-form-field>
                    </div>
                    <div class="search-actions">
                        <button mat-raised-button color="primary" (click)="searchFaq()" class="search-btn">
                            <mat-icon>search</mat-icon>
                            搜索
                        </button>
                        <button mat-stroked-button (click)="resetSearch()" class="reset-btn">
                            <mat-icon>refresh</mat-icon>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作區域 -->
        <div class="action-section">
            <div class="action-info">
                <span class="total-count">共 {{ totalCount }} 筆資料</span>
            </div>
            <div class="action-buttons">
                <button mat-raised-button color="accent" (click)="addFaq()" class="add-btn">
                    <mat-icon>add</mat-icon>
                    新增 FAQ
                </button>
            </div>
        </div>
        <div class="contents">
            <section class="block" *ngFor="let item of faqList">
                <span class="cont">
                    <span class="title">
                        Q: {{ item.question }}
                    </span>
                    <span style="margin-left: auto">
                        <button mat-flat-button (click)="editFaq(item.typeGroupId)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="delete(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1 *ngIf="title">{{ title }}</h1>

        <div class="add-layout">
            <button class="add" mat-flat-button (click)="addKeyword()">
                <i class="material-icons">add</i>關鍵字管理
            </button> &nbsp;&nbsp;
            <button class="add" mat-flat-button (click)="addFaq()">
                <i class="material-icons">add</i>新增
            </button>
        </div>
        <div class="contents">
            <section class="block" *ngFor="let item of faqList">
                <span class="cont">
                    <span class="title">
                        Q: {{ item.question }}
                    </span>
                    <span style="margin-left: auto">
                        <button mat-flat-button (click)="editFaq(item.typeGroupId)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="delete(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
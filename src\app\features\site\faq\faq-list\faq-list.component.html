<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
    <span class="white">
        <h1>{{ title }}</h1>
        <div class="add-layout">
            <div class="block2">
                <div class="title-ctn">
                    <span class="title">關鍵字</span>
                </div>
                <div class="block-input">
                    <mat-form-field class="example-form-field" appearance="outline">
                        <mat-label>關鍵字</mat-label>
                        <input matInput type="text" [(ngModel)]="keyword">
                    </mat-form-field>
                    <button class="add" mat-flat-button (click)="addFaq()">
                        <i class="material-icons">add</i>送出
                    </button>
                </div>
            </div>

        </div>
        <div class="add-layout">
            <button class="add" mat-flat-button (click)="addFaq()">
                <i class="material-icons">add</i>新增
            </button>
        </div>
        <div class="contents">
            <section class="block" *ngFor="let item of faqList">
                <span class="cont">
                    <span class="title">
                        Q: {{ item.question }}
                    </span>
                    <span style="margin-left: auto">
                        <button mat-flat-button (click)="editFaq(item.typeGroupId)">
                            編輯</button>&nbsp;
                        <button mat-flat-button (click)="delete(item.typeGroupId)">刪除</button>&nbsp;
                    </span>
                </span>
            </section>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </span>
    <app-loading [loading]="loading"></app-loading>
</div>
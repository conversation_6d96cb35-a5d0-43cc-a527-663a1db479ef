import { Component, Inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';

import { shareResourceItem } from '../../../../../interface/shareResource.interface';
import { DialogComponent } from '../../../../../shared/components/dialog/dialog.component';
import { BannerService } from '../../../../../core/services/banner.service';
import { Banner, getBannerListResp } from '../../../../../shared/models/banner.model';
import { AddBannerDialogComponent } from '../add-banner-dialog/add-banner-dialog.component';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../../interface/share.interface';

@Component({
  selector: 'app-banner-dialog',
  standalone: false,

  templateUrl: './banner-dialog.component.html',
  styleUrl: './banner-dialog.component.scss',
})
export class BannerDialogComponent implements OnInit {
  loading = false;
  bannerList: Banner[] = [];
  bannerEnable: boolean = true;
  enable: boolean = true;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      status: string;
      title: number;
      menuItemId: string;
      item?: shareResourceItem;
    },
    private dialogRef: MatDialogRef<DialogComponent>,
    private bannerService: BannerService,
    public matDialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.getBannerList();
  }

  getBannerList() {
    this.bannerService
      .getBannerListBackstage()
      .subscribe({
        next: (resp: getBannerListResp) => {
          this.bannerEnable = resp.data.isBannerEnable;
          this.bannerList = resp.data.bannerList;
          console.log(this.bannerList);
        },
        error: () => { },
      });
  }

  addBanner() {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '900px',
          showHeader: true,
          title: '新增',
          status: '新增',
          contentTemplate: AddBannerDialogComponent,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getBannerList();
      });
  }
  edit(item: Banner) {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '900px',
          showHeader: true,
          title: '編輯',
          status: '編輯',
          item: item,
          contentTemplate: AddBannerDialogComponent,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getBannerList();
      });
  }
  delete(id: string) {
    this.bannerService.deleteBanner(id).subscribe({
      next: (resp: defaultItem) => {
        resp.code === 200
          ? Swal.fire('成功', '刪除成功', 'success').then(() => {
            this.getBannerList();
          })
          : Swal.fire('失敗', '刪除失敗', 'error');
      },
      error: () => {
        Swal.fire('失敗', '刪除失敗', 'error');
      },
    });
  }

  close() {
    this.dialogRef.close(false);
  }

  changeEnable() {
    this.bannerService.updateBannerEnable().subscribe({
      next: (resp: defaultItem) => {
        resp.code === 200
          ? Swal.fire('成功', '更新成功', 'success')
          : Swal.fire('失敗', '更新失敗', 'error');
      },
      error: () => {
        Swal.fire('失敗', '更新失敗', 'error');
      },
    });
  }
}

trigger:
  branches:
    include:
      - "*"
  tags:
    include:
      - "*"

variables:
  - name: NODE_VERSION
    value: "22.12.0"
  - name: ANGULAR_BUILD_ENVIRONMENT
    value: "dev,prod"

stages:
  - stage: StaticAnalysisStage
    jobs:
      - job: StaticAnalysisJob
        pool:
          name: "Ubuntu-Pool"
        steps:
          - script: |
              echo "Running static analysis..."
              echo "TODO: Add static analysis here"
            displayName: "Run Static Analysis"

  - stage: AngularBuildStage
    dependsOn: [StaticAnalysisStage]
    condition: and(succeeded('StaticAnalysisStage'), startsWith(variables['Build.SourceBranch'], 'refs/tags/'))
    displayName: "Angular Build Stage"
    jobs:
      - job: AngularBuildJob
        displayName: "Angular Build Job"
        pool:
          name: "Ubuntu-Pool"
        workspace:
          clean: all
        steps:
          - script: |
              set -e

              echo "Setting environment variables..."
              BUILD_SOURCEBRANCH="$(Build.SourceBranch)"
              BUILD_SOURCEVERSION="$(Build.SourceVersion)"
              if [[ "$BUILD_SOURCEBRANCH" == refs/tags/* ]]; then
                TAG_NAME="${BUILD_SOURCEBRANCH#refs/tags/}"
              else
                TAG_NAME="$BUILD_SOURCEVERSION"
              fi
              BUILD_DATETIME=$(date -u +%Y%m%d%H%M)
              VERSION_BUILDNUMBER="${TAG_NAME}_${BUILD_DATETIME}"
              ANGULAR_BUILD_ENVIRONMENT=$(ANGULAR_BUILD_ENVIRONMENT)
              SYSTEM_TEAMPROJECT=$(System.TeamProject)

              docker run --rm \
                -w /workspace \
                -v "$(System.DefaultWorkingDirectory):/workspace" \
                node:$(NODE_VERSION) \
                 bash -eo pipefail -c '
                  echo "📂 Preparing app directory..."
                  mkdir /app && cp -r /workspace/* /app/

                  echo "📦 Installing dependencies..."
                  cd /app
                  npm install

                  echo "🚀 Building project..."
                  npm run build:all

                  echo "📤 Copying dist back to /workspace..."
                  cp -r /app/dist /workspace/

                  echo "🔓 Fixing permissions..."
                  chmod -R 777 /workspace

                  echo "✅ Build completed."
                '

              echo "📂 Locating build output directory..."
              DIST_DIR=$(find ./dist -maxdepth 0 -type d | head -n 1)
              if [[ ! -d "$DIST_DIR" ]]; then
                echo "❌ Cannot find dist directory: $DIST_DIR"
                exit 1
              fi
              echo "✅ Found build output: $DIST_DIR"

              echo "📁 Preparing archive output directory..."
              ARCHIVE_DIR="$(System.DefaultWorkingDirectory)/archive"
              mkdir -p "$ARCHIVE_DIR"

              IFS="," read -ra ENVIRONMENTS <<< "$ANGULAR_BUILD_ENVIRONMENT"
              for ENV in "${ENVIRONMENTS[@]}"; do
                echo "🔍 Processing environment: $ENV"
                BROWSER_DIR="$DIST_DIR/lrdf_Backstage/$ENV/browser"
                ZIP_NAME="${SYSTEM_TEAMPROJECT}_${ENV}_${VERSION_BUILDNUMBER}.zip"
                VERSION_FILE="$DIST_DIR/version.txt"

                if [[ ! -d "$BROWSER_DIR" ]]; then
                  echo "❌ ERROR: $BROWSER_DIR not found"
                  exit 1
                fi

                echo "📝 Generating version.txt"
                echo "Version Info" > "$VERSION_FILE"
                echo "Build Time (UTC): $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> "$VERSION_FILE"
                echo "Commit: $BUILD_SOURCEVERSION" >> "$VERSION_FILE"
                echo "Environment: $ENV" >> "$VERSION_FILE"
                echo "Version: $VERSION_BUILDNUMBER" >> "$VERSION_FILE"

                echo "📦 Creating archive: $ZIP_NAME"
                pushd "$BROWSER_DIR" > /dev/null
                zip -r "$ARCHIVE_DIR/$ZIP_NAME" .
                [[ -f "$VERSION_FILE" ]] && zip -g "$ARCHIVE_DIR/$ZIP_NAME" "$VERSION_FILE"
                popd > /dev/null
              done

              echo "📂 Listing archive contents:"
              ls -lh "$ARCHIVE_DIR"

              echo "✅ Archive creation completed successfully!"

              echo "📦 Moving final artifacts to $(Build.ArtifactStagingDirectory)/artifacts"
              BUILD_OUTPUT_DIR="$(Build.ArtifactStagingDirectory)/artifacts"
              mkdir -p "$BUILD_OUTPUT_DIR/release"
              cp -r "$(System.DefaultWorkingDirectory)/archive/"* "$BUILD_OUTPUT_DIR/release/"

              echo "🧹 Cleaning up environment..."
              cd $(Build.SourcesDirectory)
              git reset --hard && git clean -fdx
            displayName: "🔨 Angular Build & Archive in Docker"
          - task: PublishBuildArtifacts@1
            displayName: "📤 Publish Build Artifacts"
            inputs:
              PathtoPublish: "$(Build.ArtifactStagingDirectory)/artifacts"
              ArtifactName: "artifacts"
              publishLocation: "Container"

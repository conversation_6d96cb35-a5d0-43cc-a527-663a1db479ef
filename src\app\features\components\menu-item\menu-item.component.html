<div
  class="menu-layout"
  [ngStyle]="{ 'background-color': style?.menuBackColor }"
>
  <span *ngFor="let item of menu">
    <span
      class="name"
      [ngStyle]="{ color: style?.menuItemColor }"
      *ngIf="item.type !== 'Folder' && item.type !== 'Tab'"
    >
      {{ item.name }}
    </span>
    <span class="name" *ngIf="item.type === 'Folder' || item.type === 'Tab'">
      <span
        [matMenuTriggerFor]="menu"
        [ngStyle]="{ color: style?.menuItemColor }"
        >{{ item.name }}</span
      >
      <mat-menu #menu="matMenu">
        <ng-template matMenuContent>
          <button mat-menu-item *ngFor="let child of item.inverseParent">
            {{ child.name }}
          </button>
        </ng-template>
      </mat-menu>
    </span>
  </span>
</div>

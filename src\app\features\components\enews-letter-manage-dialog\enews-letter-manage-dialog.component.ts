import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ENewsletterService } from '../../../core/services/e-newsletter.service';
import {
  getENewsLetterInfoResp,
  getGroupListReq,
  getGroupListResp,
  getUserListReq,
  getUserListResp,
  memberTypeItem,
  setENewsLetterInfoReq,
  subscriberItem,
} from '../../../interface/eNewsletter.interface';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../interface/share.interface';
import { format } from 'date-fns';

@Component({
  selector: 'app-enews-letter-manage-dialog',
  standalone: false,

  templateUrl: './enews-letter-manage-dialog.component.html',
  styleUrl: './enews-letter-manage-dialog.component.scss',
})
export class ENewsLetterManageDialogComponent {
  loading: boolean = false;
  typeGroupId: string = '';
  sendStatus: number = 1;

  //#region 發送設定
  name: string = '';
  today: Date = new Date();
  groupList: memberTypeItem[] = [];
  userList: subscriberItem[] = [];

  sendType: boolean = false;
  sendDateTime: string = '';
  selectedHour: number = 1;
  selectedMinute: number = 0;
  hourList: number[] = Array.from({ length: 24 }, (_, i) => i);
  minuteList: number[] = Array.from({ length: 60 }, (_, i) => i);
  //#endregion

  //#region 選擇人員
  sendingObjectList: string[] = ['個別', '群組'];
  sendingObject: string = '個別';
  keyword: string = '';
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  selectAllStatus: boolean = false;
  sendingObjectUserList: subscriberItem[] = [];
  sendingObjectGroupList: memberTypeItem[] = [];
  selectUserList: subscriberItem[] = [];
  selectGroupList: memberTypeItem[] = [];

  //#endregion

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: {
        typeGroupId: string;
      };
    },
    private dialogRef: MatDialogRef<ENewsLetterManageDialogComponent>,
    private eNewsLetterService: ENewsletterService
  ) { }

  ngOnInit(): void {
    this.typeGroupId = this.data.data.typeGroupId;
    this.getENewsLetterInfo();
  }

  getENewsLetterInfo() {
    this.loading = true;
    this.eNewsLetterService.getENewsLetterInfo(this.typeGroupId).subscribe({
      next: (resp: getENewsLetterInfoResp) => {
        this.loading = false;
        this.name = resp.data.name;
        this.userList = resp.data.subscriber;
        this.selectUserList = resp.data.subscriber;
        this.groupList = resp.data.memberType;
        this.selectGroupList = resp.data.memberType;
        this.sendDateTime = resp.data.sendDateTime;
        this.sendType = resp.data.sendDateTime ? true : false;
        if (this.sendDateTime) {
          this.selectedHour = new Date(this.sendDateTime).getHours();
          this.selectedMinute = new Date(this.sendDateTime).getMinutes();
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  choseUser() {
    this.sendStatus = 2;
    this.selectAllStatus = false;
    this.searchSendingList();
  }

  deleteUser(item: subscriberItem) {
    this.userList = this.userList.filter(
      (x) => x.subscriberId !== item.subscriberId
    );
  }

  deleteGroup(item: memberTypeItem) {
    this.groupList = this.groupList.filter(
      (x) => x.memberTypeId !== item.memberTypeId
    );
  }

  cancel() {
    this.dialogRef.close();
  }

  send() {
    if (this.userList.length < 1 && this.groupList.length < 1) {
      Swal.fire('警告', `尚未選擇發送對象`, 'warning');
      return;
    }

    if (
      this.sendType &&
      (!this.sendDateTime ||
        this.selectedHour === null ||
        this.selectedMinute === null)
    ) {
      Swal.fire('警告', `請完整選擇發送日期與時間`, 'warning');
      return;
    }

    let req: setENewsLetterInfoReq = {
      typeGroupId: this.typeGroupId,
      subscriber: this.userList,
      memberType: this.groupList,
      sendDateTime: this.sendDateTime ? format(new Date(this.sendDateTime), 'yyyy-MM-dd') : '',
      hour: this.selectedHour,
      minute: this.selectedMinute,
      isSendNow: !this.sendType,
    };
    this.loading = true;
    this.eNewsLetterService.setENewsLetterInfo(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '設定成功', 'success').then(() => {
            this.dialogRef.close(true);
          });
        } else {
          Swal.fire('失敗', `${resp.message}`, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', `${err.error.message}`, 'error');
      },
    });
  }

  //#region 選擇人員
  searchSendingList(type?: string) {
    if (type) {
      this.sendingObject = type;
    }
    this.nowPage = 1;
    if (this.sendingObject === '個別') {
      this.getUserList();
    } else {
      this.getGroupList();
    }
  }

  getGroupList() {
    let req: getGroupListReq = {
      memberTypeName: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.eNewsLetterService.getGroupList(req).subscribe({
      next: (resp: getGroupListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.sendingObjectGroupList = resp.data.memberType;
        this.selectAllStatus = this.sendingObjectGroupList.every((item) =>
          this.selectGroupList.some((selectListItem) => selectListItem.memberTypeId === item.memberTypeId)
        );
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  getUserList() {
    let req: getUserListReq = {
      email: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.eNewsLetterService.getUserList(req).subscribe({
      next: (resp: getUserListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.sendingObjectUserList = resp.data.subscriber;
        this.selectAllStatus = this.sendingObjectUserList.every((item) =>
          this.selectUserList.some((selectListItem) => selectListItem.subscriberId === item.subscriberId)
        );
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    if (this.sendingObject === '個別') {
      this.getUserList();
    } else {
      this.getGroupList();
    }
  }

  selectAll(event: Event) {
    let target = event.target as HTMLInputElement;
    const checked = target.checked;
    if (this.sendingObject === '個別') {
      if (checked) {
        this.selectAllStatus = true;
        // 確保只加入當前分頁的項目，且不重複
        this.sendingObjectUserList.forEach((item) => {
          // 檢查此項目是否已存在於 selectList 中
          const exists = this.selectUserList.some(
            (selectedItem) => selectedItem.subscriberId === item.subscriberId
          );

          if (!exists) {
            this.selectUserList.push(item);
          }
        });
      } else {
        this.selectAllStatus = false;
        const currentReviewGroupIds = this.sendingObjectUserList.map(
          (item) => item.subscriberId
        );
        this.selectUserList = this.selectUserList.filter(
          (item) => !currentReviewGroupIds.includes(item.subscriberId)
        );
      }
    } else {
      if (checked) {
        this.selectAllStatus = true;
        // 確保只加入當前分頁的項目，且不重複
        this.sendingObjectGroupList.forEach((item) => {
          // 檢查此項目是否已存在於 selectList 中
          const exists = this.selectGroupList.some(
            (selectedItem) => selectedItem.memberTypeId === item.memberTypeId
          );

          if (!exists) {
            this.selectGroupList.push(item);
          }
        });
      } else {
        this.selectAllStatus = false;
        const currentReviewGroupIds = this.sendingObjectGroupList.map(
          (item) => item.memberTypeId
        );
        this.selectGroupList = this.selectGroupList.filter(
          (item) => !currentReviewGroupIds.includes(item.memberTypeId)
        );
      }
    }
  }

  changeSelect(event: Event, item: subscriberItem | memberTypeItem) {
    this.selectAllStatus = false;
    let target = event.target as HTMLInputElement;
    const checked = target.checked;
    if (this.sendingObject === '個別') {
      if (checked) {
        this.selectUserList.push(item as subscriberItem);
      } else {
        this.selectUserList = this.selectUserList.filter(
          (selectListItem) =>
            selectListItem.subscriberId !==
            (item as subscriberItem).subscriberId
        );
      }
    } else {
      if (checked) {
        this.selectGroupList.push(item as memberTypeItem);
      } else {
        this.selectGroupList = this.selectGroupList.filter(
          (selectListItem) =>
            selectListItem.memberTypeId !==
            (item as memberTypeItem).memberTypeId
        );
      }
    }
  }

  isSelected(item: subscriberItem | memberTypeItem): boolean {
    return this.sendingObject === '個別'
      ? this.selectUserList.some(
        (x) => x.subscriberId === (item as subscriberItem).subscriberId
      )
      : this.selectGroupList.some(
        (x) => x.memberTypeId === (item as memberTypeItem).memberTypeId
      );
  }

  back() {
    this.selectAllStatus = false;
    this.sendingObject = '個別';
    this.sendStatus = 1;
  }

  add() {
    this.sendStatus = 1;
    this.selectAllStatus = false;
    this.sendingObject = '個別';
    this.userList = [];
    this.groupList = [];
    const userToAdd = this.selectUserList.filter(
      (userItem) =>
        !this.userList.some(
          (existingItem) => existingItem.subscriberId === userItem.subscriberId
        )
    );
    this.userList = [...this.userList, ...userToAdd];

    const groupToAdd = this.selectGroupList.filter(
      (groupItem) =>
        !this.groupList.some(
          (existingItem) => existingItem.memberTypeId === groupItem.memberTypeId
        )
    );
    this.groupList = [...this.groupList, ...groupToAdd];
    // this.selectGroupList = [];
    // this.selectUserList = [];
  }

  //#endregion
}

import {
  fileBoxDialog,
  fileBoxDigData,
} from './../../../shared/models/dialog.model';
import { Component, ElementRef, Inject, ViewChild } from '@angular/core';
import { FileService } from '../../../core/services/file.service';
import { fromEvent, Observable } from 'rxjs';
import {
  FileEntity,
  PagingOfFileEntity,
} from '../../../shared/models/pagingOfFileEntity.model';
import Swal from 'sweetalert2';
import { ClipboardService } from 'ngx-clipboard';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { TagSettingComponent } from './tag-setting/tag-setting.component';
import { FileDescriptionComponent } from './file-description/file-description.component';
import {
  ElUploadBoxComponent,
  UploadStatus,
} from './el-upload-box/el-upload-box.component';

export interface typeItem {
  name: string;
  type: string;
  selected: boolean;
}

@Component({
  selector: 'app-file-box',
  standalone: false,

  templateUrl: './file-box.component.html',
  styleUrl: './file-box.component.scss',
})
export class FileBoxComponent {
  loading: boolean = true;
  /** 多選or單選 */
  isMultiple = false;
  /** 是否只能觀看(不須選擇圖片or影片) */
  isOnlyView = false;
  keyword: string = '';
  typeList: typeItem[] = [
    {
      name: '圖片',
      type: 'Image',
      selected: false,
    },
    {
      name: '影片',
      type: 'Video',
      selected: false,
    },
    {
      name: '檔案',
      type: 'File',
      selected: false,
    },
    {
      name: 'PDF',
      type: 'Pdf',
      selected: false,
    },
  ];
  selector = 'Image' as 'Image' | 'Video' | 'File' | 'Pdf';
  tagList: { name: string; selected: boolean }[] = [];
  contentList: any = [];
  skip: number = 0;
  totalCount: number = 0;
  $scrollEvent!: Observable<any>;
  isLazyLoad: boolean = false;
  @ViewChild('content') content!: ElementRef;
  @ViewChild('lastBlock') lastBlock!: ElementRef;
  popupData!: fileBoxDigData;

  constructor(
    private _fileService: FileService,
    private _clipboardService: ClipboardService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: fileBoxDigData,
    public dialogRef: MatDialogRef<DialogComponent>,
    public dialog: MatDialog
  ) {
    this.isMultiple = data.isMultiple as boolean;
  }

  ngOnInit(): void {
    this.popupData = this.data;
    if (this.popupData) {
      // 類型(Image/Video)
      this.selector = this.popupData.type || ('Image' as any);
      if (this.popupData.type) {
        this.typeList = this.typeList.filter(
          (item) => item.type === this.popupData.type
        );
      }

      // 多選/單選
      this.isMultiple = this.popupData.isMultiple;

      // 是否可選擇
      this.isOnlyView = this.popupData.isOnlyView;
    }
    this.typeList[0].selected = true;

    this.getList();
    this.getTags();
  }

  ngAfterViewInit(): void {
    this.scrollLoading();
  }

  getList() {
    this.loading = true;
    this._fileService
      .getFiles(
        sessionStorage.getItem('webSiteId')!,
        this.tagList
          .filter((x) => {
            return x.selected;
          })
          .map((y) => y.name),
        this.selector,
        this.keyword
      )
      .subscribe((x: PagingOfFileEntity) => {
        this.contentList = x.result!;
        this.totalCount = x.totalCount!;
        this.skip = 0;
      });
    this.loading = false;
  }

  getTags() {
    this.loading = true;
    this._fileService
      .getTags(sessionStorage.getItem('webSiteId')!, this.selector)
      .subscribe((tags) => {
        this.tagList = tags.map((y) => {
          return {
            name: y,
            selected: false,
          };
        });
        this.loading = false;
      });
  }

  /** LazyLoad */
  scrollLoading() {
    this.$scrollEvent = fromEvent(this.content.nativeElement, 'scroll');
    this.$scrollEvent.subscribe((event) => {
      const viewTop = event.target.scrollTop;
      const viewBottom = viewTop + event.target.clientHeight;

      const eleTop = this.lastBlock.nativeElement.offsetTop;
      const eleBottom = eleTop + this.lastBlock.nativeElement.clientHeight;

      if (
        eleBottom <= viewBottom &&
        eleTop >= viewTop &&
        this.contentList.length < this.totalCount &&
        !this.isLazyLoad
      ) {
        this.isLazyLoad = true;
        this.skip = this.skip + 10;
        this.loading = true;
        this._fileService
          .getFiles(
            sessionStorage.getItem('webSiteId')!,
            this.tagList
              .filter((x) => {
                return x.selected;
              })
              .map((y) => y.name),
            this.selector,
            null,
            this.skip,
            10
          )
          .subscribe((res: PagingOfFileEntity) => {
            res.result!.map((x: FileEntity) => {
              this.contentList.push(x);
            });
            this.totalCount = res.totalCount!;
            this.loading = false;
            this.isLazyLoad = false;
          });
      }
    });
  }

  openUploadFile() {
    const uploadDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        showHeader: true,
        title: `上傳${
          this.selector === 'Image'
            ? '圖片'
            : this.selector === 'Video'
            ? '影片'
            : '檔案'
        }`,
        contentTemplate: ElUploadBoxComponent,
        data: {
          status: UploadStatus.MULTIPLE,
          fileTypeList:
            this.selector === 'Image'
              ? ['jpg', 'png']
              : this.selector === 'Video'
              ? ['mp4']
              : this.selector === 'Pdf'
              ? ['pdf']
              : [],
        },
      },
    });
    uploadDialog
      .afterClosed()
      .subscribe((fileResp: { fileList: File[]; fileNameList: string[] }) => {
        console.log(fileResp);
        if (fileResp) {
          this.dialog
            .open(DialogComponent, {
              data: {
                width: '30%',
                showHeader: true,
                title: '分類設置',
                contentTemplate: TagSettingComponent,
                selector: this.selector,
              },
            })
            .afterClosed()
            .subscribe((res) => {
              if (res.isSubmit) {
                if (fileResp.fileList.length) {
                  this.loading = true;
                  this._fileService
                    .uploadFiles(
                      sessionStorage.getItem('webSiteId')!,
                      res.tag,
                      this.selector,
                      fileResp.fileList
                    )
                    .subscribe({
                      next: (resp: any) => {
                        this.loading = false;
                        if (resp == null) {
                          Swal.fire('上傳失敗，請聯絡客服人員', '', 'error');
                          return;
                        }
                        if (resp.code !== 0) {
                          Swal.fire(resp.message, '', 'error');
                        } else {
                          this.getList();
                        }
                      },
                      error: () => {
                        this.loading = false;
                        Swal.fire('上傳失敗，請聯絡客服人員', '', 'error');
                      },
                    });
                }
              }
            });
        }
      });
  }

  /** 選取tag */
  selectTag(tag: { name: string; selected: boolean }) {
    this.loading = true;
    tag.selected = !tag.selected;
    this._fileService
      .getFiles(
        sessionStorage.getItem('webSiteId')!,
        this.tagList
          .filter((x) => {
            return x.selected;
          })
          .map((y) => y.name),
        this.selector
      )
      .subscribe((res: PagingOfFileEntity) => {
        this.contentList = res.result!;
        this.totalCount = res.totalCount!;
        this.skip = 0;
        this.loading = false;
      });
  }

  /** 選擇圖片/影片 */
  selectedBlock(item: any) {
    if (!this.isMultiple) {
      this.contentList.map((x: any) => {
        x.selected = false;
      });
      item.selected = !item.selected;
    } else {
      item.selected = !item.selected;
    }
  }

  /** 雙擊選擇 */
  dbClickBlock(item: FileEntity) {
    if (this.isOnlyView) {
      return;
    }
    this.dialogRef.close({
      data: item,
      type: this.selector,
    });
  }

  /** 複製檔案preview url */
  copy($event: any, item: any) {
    $event.preventDefault();
    $event.stopPropagation();
    const preViewUrl = `${item.url.split('download')[0]}`;
    this._clipboardService.copyFromContent(preViewUrl);
    this.snackBar.open('Copied!', '×', {
      duration: 2000,
    });
  }

  /** 左邊選單選取 */
  selectType(item: any) {
    this.typeList = this.typeList.map((x) => {
      x.selected = false;
      return x;
    });
    item.selected = true;
    this.selector = item.type;
    this.getList();
    this.getTags();
  }

  /** 確定 */
  submit() {
    const selected = this.contentList.filter((x: any) => {
      if (x.selected) {
        return x;
      }
    });

    if (this.selector === 'Image') {
      for (const item of selected) {
        if (
          !item.description ||
          item.description.trim() === '' ||
          !item.enDescription ||
          item.enDescription.trim() === ''
        ) {
          Swal.fire('請填寫檔案描述', '', 'warning');
          return;
        }
      }
    }

    if (!this.isOnlyView) {
      if (!selected.length) {
        Swal.fire(
          `請選擇一個${this.selector === 'Image' ? '圖片' : '影片'}`,
          '',
          'warning'
        );
        return;
      }
    }
    this.dialogRef.close({
      data: this.isMultiple ? selected : selected[0],
      type: this.selector,
    });
  }

  editFileDescription(id: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '30%',
          showHeader: true,
          title: '編輯檔案描述',
          contentTemplate: FileDescriptionComponent,
          data: id,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getList();
      });
  }

  deleteFile(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        if (this.popupData) {
          this._fileService.deleteFile(id).subscribe({
            next: () => {
              this.tagList = [];
              this.getTags();
              this.getList();
            },
          });
        } else {
          Swal.fire('錯誤', '請選擇影片', 'error');
        }
      }
    });
  }
}

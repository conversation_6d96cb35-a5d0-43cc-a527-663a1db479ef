<div class="popup-layout">
  <div class="header">
    <h2>選單</h2>
    <button mat-flat-button (click)="openTypePopup()" [disabled]="!hasFunctionPolicy('MenuItemManage')">
      <i class="material-icons">add</i>新增選單
    </button>
    <span class="close-btn">
      <mat-icon aria-hidden="false" aria-label="close icon" fontIcon="close" mat-dialog-close></mat-icon>
    </span>

    <!-- <i class="material-icons close" (click)="close(true)">clear</i> -->
  </div>
  <div class="menuSet-layout">
    <span class="tree">

      
      <mat-tree #tree [dataSource]="dataSource" [childrenAccessor]="childrenAccessor">
        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding (click)="nodeOnClick(node)">
          <button mat-icon-button disabled></button>
          {{ node.name }}/{{node.englishName}}/___主選單
          <mat-icon class=wifi *ngIf="isAdmin" (click)="changeSort(node,false)">arrow_upward</mat-icon>
          <mat-icon class=wifi *ngIf="isAdmin" (click)="changeSort(node,true)">arrow_downward</mat-icon>
        </mat-tree-node>
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding matTreeNodeToggle
          [cdkTreeNodeTypeaheadLabel]="node.name" (click)="nodeOnClick(node)">
          <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
            <mat-icon class="mat-icon-rtl-mirror">
              {{ tree.isExpanded(node) ? "expand_more" : "chevron_right" }}
            </mat-icon>
          </button>
          {{ node.name }}/{{node.englishName}}/___有第二層選單
          <mat-icon class=wifi *ngIf="isAdmin" (click)="changeSort(node,false)">arrow_upward</mat-icon>
          <mat-icon class=wifi *ngIf="isAdmin" (click)="changeSort(node,true)">arrow_downward</mat-icon>
        </mat-tree-node>
      </mat-tree>


      <app-loading [loading]="treeLoading"></app-loading>
    </span>
    <span class="contents" *ngIf="content">
      <span class="space-between">
        <span class="content-blocks">
          <h3>中文:{{ content.name }}</h3>
          <h3>英文:{{ content.englishName }}</h3>
          <span class="block">
            <span class="title">
              <i class="material-icons" [ngClass]="{ notEdit: !hasFunctionPolicy('MenuItemManage') }">not_interested</i>
              <label>選單顯示設定</label>
            </span>
            <mat-form-field appearance="outline">
              <mat-select [disabled]="!hasFunctionPolicy('MenuItemManage')" [(value)]="content.visibility"
                (selectionChange)="selectionChange()">
                <mat-option *ngFor="let visibility of menuItemVisibilityTypes" [value]="visibility.type">
                  {{ visibility.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </span>
          @if(content.type !== 'FastMenu' && content.type !== 'Donate' && content.type !== 'DonateFolder' &&
          content.type !== 'DonateList' && content.type !== 'Recruit' && !(content.type === 'Folder' && content.parentId
          == null)) {
          <span class="block">
            <span class="title">
              <label>父層選單設定</label>
            </span>
            <button mat-flat-button [matMenuTriggerFor]="animals">請選擇父層選單</button>
            <mat-menu #animals="matMenu">
              <ng-container *ngFor="let item of parentMenuList">
                <button mat-menu-item
                  *ngIf="item.type === 'Folder' && item.inverseParent && item.inverseParent.length > 0"
                  [matMenuTriggerFor]="childMenu" (menuOpened)="setCurrentParentItem(item)"> {{ item.name }}
                </button>
                <button mat-menu-item
                  *ngIf="!item.inverseParent || (item.inverseParent.length === 0) || item.type !== 'Folder'"
                  (click)="change(item)">
                  {{ item.name }}
                </button>
              </ng-container>

              <mat-menu #childMenu="matMenu">
                <button mat-menu-item (click)="change(currentParentItem)">設定 {{ currentParentItem.name }}</button>
                <mat-divider></mat-divider>
                @if( currentParentItem.inverseParent&&getUniqueChildren(currentParentItem.inverseParent ||
                []).length>1){
                <ng-container *ngFor="let childItem of getUniqueChildren(currentParentItem.inverseParent || [])">
                  @if (childItem.type === 'Folder'||childItem.type === 'Tab') {
                  <button mat-menu-item
                    *ngIf="childItem.type === 'Folder' && childItem.inverseParent && childItem.inverseParent.length > 0"
                    [matMenuTriggerFor]="nestedMenu" (menuOpened)="setNestedParentItem(childItem)">
                    {{ childItem.name }}
                  </button>
                  <button mat-menu-item
                    *ngIf="!childItem.inverseParent || (childItem.inverseParent.length === 0) || childItem.type !== 'Folder'"
                    (click)="change(childItem)">
                    {{ childItem.name }}
                  </button>
                  }
                </ng-container>
                }
              </mat-menu>

              <mat-menu #nestedMenu="matMenu">
                <button mat-menu-item (click)="change(nestedParentItem)">設定 {{nestedParentItem.name }}</button>
                @if( nestedParentItem.inverseParent&&getChildrenLang(nestedParentItem.inverseParent || [])>0){
                <mat-divider></mat-divider>
                <ng-container *ngFor="let nestedItem of getUniqueChildren(nestedParentItem.inverseParent || [])">
                  @if (nestedItem.type === 'Tab') {
                  <button mat-menu-item (click)="change(nestedItem)">
                    {{ nestedItem.name }}
                  </button>
                  }
                </ng-container>
                }
              </mat-menu>
            </mat-menu>
          </span>
          }


          <ng-container *ngIf="selectedType === 'OnlyMember'">
            <mat-divider *ngIf="false"></mat-divider>
          </ng-container>
        </span>
        <div class="close-btn">
          <span>
            <button mat-flat-button (click)="editContent()" *ngIf="
                content.type !== 'Folder' &&
                content.type !== 'FastMenu' &&
                content.type !== 'Tab' &&
                content.type !== 'MenuItemLink'&&
                content.type !== 'Recruit'&&
                content.type !== 'Donate'&&
                content.type !== 'DonateList'&&
                content.type !== 'DonateFolder'
              " [disabled]="!hasMenuPolicy(content.id) && !content.editable">
              <i class="material-icons">send</i>內容管理
            </button>
            <button mat-flat-button (click)="createChild()"
              *ngIf="content.type === 'FastMenu' ||content.type === 'Folder' || content.type === 'Tab'"
              [disabled]="!hasFunctionPolicy('MenuItemManage')">
              <i class="material-icons">add</i>新增子選單
            </button>
            <button mat-flat-button (click)="editMenuItem()" [disabled]="!hasFunctionPolicy('MenuItemManage')">
              <i class="material-icons">create</i>編輯選單
            </button>
            <button mat-flat-button (click)="deleteMenuItem()" [disabled]="!hasFunctionPolicy('MenuItemManage')">
              <i class="material-icons">delete_forever</i>刪除選單
            </button>
          </span>
        </div>
      </span>
      <app-loading [loading]="contentLoading"></app-loading>
    </span>
  </div>
</div>
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-create-group',
  standalone: false,
  templateUrl: './create-group.component.html',
  styleUrls: ['./create-group.component.scss'],
})
export class CreateGroupComponent implements OnInit {
  name: string = '';
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<CreateGroupComponent>
  ) {}

  ngOnInit() {}

  submit() {
    if (this.name) {
      this.dialogRef.close({ name: this.name });
    } else {
      Swal.fire('請輸入群組名稱', '', 'warning');
    }
  }

  close() {
    this.dialogRef.close();
  }
}

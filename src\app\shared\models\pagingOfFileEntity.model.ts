﻿export class PagingOfFileEntity {
  skip?: number;
  take?: number;
  totalCount?: number;
  currentPageIndex?: number;
  totalPageCount?: number;
  hasPreviousPage?: boolean;
  hasNextPage?: boolean;
  result?: FileEntity[];
}

export class FileEntity {
  id?: string;
  webSiteId?: string;
  name?: string;
  type?: string;
  dataId?: string;
  previewImageDataId?: string;
  size?: number;
  userId?: string;
  tag?: string;
  url?: string;
  previewImageUrl?: string;
}

import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareService } from '../../../../core/services/share.service';
import { ENewsletterService } from '../../../../core/services/e-newsletter.service';
import {
  eNewsletterItem,
  getENewsletterListReq,
  getENewsletterListResp,
} from '../../../../interface/eNewsletter.interface';
import { HttpErrorResponse } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { ViewENewsLetterColumnDialogComponent } from '../../../components/view-enews-letter-column-dialog/view-enews-letter-column-dialog.component';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../interface/share.interface';
import { ENewsLetterManageDialogComponent } from '../../../components/enews-letter-manage-dialog/enews-letter-manage-dialog.component';

@Component({
  selector: 'app-e-newsletter-list',
  standalone: false,

  templateUrl: './e-newsletter-list.component.html',
  styleUrl: './e-newsletter-list.component.scss',
})
export class ENewsletterListComponent implements OnInit {
  lang: string = '中文';
  loading: boolean = false;
  menuItemId: string = '';
  keyword: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  eNewsletterList: eNewsletterItem[] = [];

  statusClassMap: { [key: number]: string } = {
    1: 'status-pending',
    2: 'status-pending',
    3: 'status-pending',
    4: 'status-rejected',
    5: 'status-editing',
  }; //1未送審 2已送審  3審核通過未寄信 4審核未通過 5已寄信

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareService: ShareService,
    private eNewsletterService: ENewsletterService,
    private dialog: MatDialog
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.activatedRoute.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId') as string;
      this.searchlist();
    });
  }

  searchlist() {
    this.nowPage = 1;
    this.getENewsletterList();
  }

  addENewsletter() {
    this.router.navigate(['/manage/', this.menuItemId, 'eNewsletter', 'edit']);
  }

  editENewsletter(item: eNewsletterItem) {
    if (item.statusNum === 2) {
      return;
    }
    this.router.navigate(['/manage/', this.menuItemId, 'eNewsletter', 'edit'], {
      queryParams: { id: item.typeGroupId },
    });
  }

  viewColumn(id: string) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '1300px',
        height: '400px',
        contentTemplate: ViewENewsLetterColumnDialogComponent,
        showHeader: true,
        data: {
          typeGroupId: id,
        },
        title: '發送佇列',
      },
    });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.eNewsletterService.deleteENewsletter(id).subscribe({
          next: (res: defaultItem) => {
            this.loading = false;
            res.code === 200
              ? Swal.fire({
                  title: '刪除成功',
                  icon: 'success',
                  showCancelButton: false,
                  reverseButtons: true,
                }).then(() => {
                  this.getENewsletterList();
                })
              : Swal.fire({
                  title: '刪除失敗',
                  icon: 'error',
                  showCancelButton: false,
                  reverseButtons: true,
                });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire({
              title: '刪除失敗',
              text: err.error.message,
              icon: 'error',
              showCancelButton: false,
              reverseButtons: true,
            });
          },
        });
      }
    });
  }

  getENewsletterList() {
    let req: getENewsletterListReq = {
      menuitemId: this.menuItemId,
      name: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: sessionStorage.getItem('lang')
        ? (sessionStorage.getItem('lang') as string)
        : 'zh',
    };
    this.loading = true;
    this.eNewsletterService.getENewsletterList(req).subscribe({
      next: (resp: getENewsletterListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.eNewsletterList = resp.data.data;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  sendManage(item: eNewsletterItem) {
    //3審核通過未寄信 5已寄信 除外要return
    if (item.statusNum !== 3 && item.statusNum !== 5) {
      return;
    }
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '900px',
          height: '400px',
          contentTemplate: ENewsLetterManageDialogComponent,
          showHeader: true,
          data: {
            typeGroupId: item.typeGroupId,
          },
          title: '發送設定',
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getENewsletterList();
      });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getENewsletterList();
  }
}

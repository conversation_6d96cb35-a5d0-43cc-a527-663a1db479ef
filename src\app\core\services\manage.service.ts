import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ManageService {
  menuItemChange = new Subject<any>();
  homePageChange = new Subject<any>();
  contentChange = new Subject<any>();

  constructor() { }

  // getBg(menu) {
  //   const meta = menu.menuItem.meta as any;
  //   if (meta) {
  //     return meta.coverUrl ?
  //       {
  //         'background-color': meta.backColor,
  //         'background-image': `url(${meta.coverUrl})`,
  //         'background-size': 'cover',
  //         'background-position': 'center',
  //         'background-repeat': 'no-repeat'
  //       } : {
  //         'background-color': meta.backColor,
  //         'background-size': 'cover',
  //         'background-position': 'center',
  //         'background-repeat': 'no-repeat'
  //       };
  //   } else {
  //     return;
  //   }
  // }
}

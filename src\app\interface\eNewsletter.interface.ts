import { new_NewsTagDataItem } from './news.interface';
import { defaultItem } from './share.interface';

export interface getENewsletterListReq {
  menuitemId: string;
  name: string;
  publishDateStart?: string;
  publishDateEnd?: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getENewsletterListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    data: eNewsletterItem[];
  };
}

export interface eNewsletterItem {
  typeGroupId: string;
  e_NewsletterId: string;
  e_NewsletterPublishDateTime: string;
  name: string;
  status: string;
  statusNum: number;
  openRate: string;
  clickRate: string;
  isEditable: boolean;
  isScheduleEditable: boolean;
}

export interface getENewsletterResp extends defaultItem {
  data: {
    name: string;
    keyword: string;
    isPendingApproval: boolean; //審核中
    e_NewsletterPublishDateTime: string;
    emailTemplate: number;
    levelDecision: number;
    e_NewsletterTagDatas: new_NewsTagDataItem[];
    new_NewsDatas: {
      new_NewsId: string;
      title: string;
    }[];
    creater: string;
    editor: string;
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface getGroupReportNewsListReq {
  keyword: string;
  type: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getGroupReportRecruitListReq {
  keyword: string;
  type: string; // 1 是徵才公告 2 甄選結果
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getGroupReportListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    data: groupReportItem[];
  };
}

export interface groupReportItem {
  new_NewsId: string;
  title: string;
  publishDateTime: string;
}

export interface createUpdateENewsletterReq {
  menuitemId: string;
  typeGroupId: string;
  keyword: string;
  levelDecision: number;
  name: string;
  e_NewsletterPublishDateTime: string;
  emailTemplate: number;
  new_NewsIds: string[];
  e_NewsletterTagDatas: {
    tagName: string;
    dataString: string | null;
  }[];
  lang: string;
}

export interface getViewENewsLetterColumnReq {
  typeGroupId: string;
  currentPage: number;
  pageSize: number;
}

export interface getViewENewsLetterColumnResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    data: viewENewsLetterColumnItem[];
  };
}
export interface viewENewsLetterColumnItem {
  email: string;
  name: string;
  firstOpenDateTime: string;
  firstClickDateTime: string;
  actuallySendDateTime: string;
}



export interface getENewsletterForViewResp extends defaultItem {
  data: string | { title: string, content: string };
}

export interface getENewsLetterInfoResp extends defaultItem {
  data: {
    name: string;
    sendDateTime: string;
    subscriber: subscriberItem[];
    memberType: memberTypeItem[];
  };
}

export interface getUserListReq {
  email: string;
  currentPage: number;
  pageSize: number;
}

export interface getUserListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    subscriber: subscriberItem[];
  };
}

export interface subscriberItem {
  subscriberId: string;
  email: string;
}

export interface getGroupListReq {
  memberTypeName: string;
  currentPage: number;
  pageSize: number;
}

export interface getGroupListResp extends defaultItem {
  data: {
    totalCount: number;
    totalPage: number;
    memberType: memberTypeItem[];
  };
}

export interface memberTypeItem {
  memberTypeId: string;
  name: string;
}

export interface setENewsLetterInfoReq {
  typeGroupId: string;
  subscriber: subscriberItem[];
  memberType: memberTypeItem[];
  sendDateTime: string;
  isSendNow: boolean;
  hour: number;
  minute: number;
  isAll?: boolean;
}

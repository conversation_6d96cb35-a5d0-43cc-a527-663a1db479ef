.news-layout {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    align-items: center;
    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;
        h1 {
            color: #2eaddb;
        }
    }
}
.contents {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 80%;
}

.block-end {
    margin: 5px 0;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #cccccc33;
    width: 100%;

    .tag-layout {
        display: flex;
        flex-wrap: wrap;

        .tag-title {
            width: 150px;
            padding: 0 5px;
            margin: 10px 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 0 3px #ccc;
            background-color: #ffffff;
            border: 3px solid #ffffff;
            cursor: pointer;
            transition: 0.3s ease-in-out;

            &.add {
                width: auto;
                background-color: #b8d3f3;
                border: 3px solid #b8d3f3;
            }

            &.selected {
                border: 3px solid #3f51b5;
            }

            &:hover {
                box-shadow: 0px 0px 9px 0px #ccc;
            }

            input {
                width: 100%;
                font-size: 25px;
                outline: 0;
                background-color: #ffffff;
                border: 1px solid #868585;
            }
        }
    }

    &.row {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .column {
        display: flex;
        flex-direction: column;
    }

    .title {
        line-height: 2;
    }

    .input {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        padding: 0 10px;

        &:focus {
            border: 1px solid #3f51b5;
        }
    }

    textarea {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        resize: vertical;
        min-height: 280px;
        padding: 0 10px;

        &:focus {
            border: 1px solid #3f51b5;
        }
    }

    .mat-slide-toggle {
        margin: 0 1em;
    }

    .select-image {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .preview {
            width: 200px;
            height: 100px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin: 10px 0;
            border: 4px solid #ccc;
        }

        .files-layout {
            display: flex;
            align-items: center;

            .file-block {
                border: 1px solid #ccc;
                background-color: #ffffff;
                margin: 5px;
                padding: 5px 1em;
                cursor: pointer;
                transition: 0.3s ease-in-out;

                &:hover {
                    border: 1px solid #3f51b5;
                }
            }
        }
    }
    .combine {
        padding: 0;
        .combine-block {
            display: flex;
            align-items: center;
            .website {
                font-weight: bold;
            }
        }
    }
}

.btn-group {
    width: 90%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 1em;
}
// ==============================
.block-end {
    margin: 10px 0;
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    // max-width: calc(1024px / 2 + 40px);
    // min-width: calc(1024px / 3 - 20px);
    animation-duration: 1s;
    animation-name: fadeInUp;

    &.add {
        background-color: #cef2ff;
        color: #00aeef;
        border-radius: 5px;
        justify-content: center;
        align-items: center;
        padding: 10px;
    }
    &:hover {
        box-shadow: 0px 0px 9px 0px #d6d6d6;
    }
}

.block {
    margin: 10px 0;
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    // max-width: calc(1024px / 2 + 40px);
    // min-width: calc(1024px / 3 - 20px);
    animation-duration: 1s;
    animation-name: fadeInUp;

    &.add {
        background-color: #cef2ff;
        color: #00aeef;
        border-radius: 5px;
        justify-content: center;
        align-items: center;
        padding: 10px;
    }

    &:hover {
        box-shadow: 0px 0px 9px 0px #d6d6d6;
    }

    .title {
        line-height: 2;
        font-weight: bold;

        .required {
            color: #f0524b;
        }
    }

    input {
        font-size: 0.9em;
        line-height: 1.8;
        outline: 0;
        background-color: #fff;
        border: 1px solid #949494;
        padding: 8px 10px;
        border-radius: 5px;
    }

    .select {
        font-size: 0.9em;
        line-height: 1.8;
        outline: 0;
        background-color: #fff;
        border: 1px solid #949494;
        // padding: 11px 10px;
        border-radius: 5px;
    }

    option {
        padding: 8px 10px;
        line-height: 1.8;
        font-size: 1em;
        background-color: #fff;
        color: #364250;
    }

    textarea {
        font-size: 0.9em;
        line-height: 1.8;
        outline: 0;
        background-color: #fff;
        border: 1px solid #949494;
        padding: 8px 10px;
        border-radius: 5px;
        resize: vertical;
        min-height: 80px;
        // padding: 0 10px;
    }

    mat-select {
        padding: 10px 0;
        background-color: #f1f1f1;
        border: 1px solid #ccc;
    }

    mat-radio-button,
    mat-checkbox {
        padding: 0 10px;
    }
}
.close-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px;
    button {
        margin: 0 10px;
    }
}

.level-block {
    margin: 5px 0;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #cccccc33;
    width: 100%;
}

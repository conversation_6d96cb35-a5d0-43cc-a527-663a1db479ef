<!--
  備註: 外層物件記得要給CSS "position=relative"
 -->
<div class="out-block" [ngClass]="{ move: move }" *ngIf="policyOk">
  <div class="btns" [ngClass]="{ isRight: isRight }">
    <button mat-flat-button (click)="settingBtnClick()">
      <mat-icon>format_color_fill</mat-icon>
    </button>
    <button *ngIf="isLayout" mat-flat-button (click)="layoutBtnClick()">
      <mat-icon>settings</mat-icon>
    </button>
  </div>
</div>

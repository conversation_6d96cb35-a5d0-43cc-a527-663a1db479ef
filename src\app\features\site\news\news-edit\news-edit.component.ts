import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { NewsService } from '../../../../core/services/news.service';
import { ActivatedRoute, Router } from '@angular/router';

import { MatDialog } from '@angular/material/dialog';

import { contentData } from '../../../../interface/editor.interface';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { HttpErrorResponse } from '@angular/common/http';
import {
  createUpdateNewsReq,
  getNewsResp,
  getNewsUserGroupResp,
} from '../../../../interface/news.interface';
import { EDITORTYPE } from '../../../../enum/editor.enum';
import { ShareService } from '../../../../core/services/share.service';
import {
  createUpdateResp,
  defaultItem,
  getTypeListResp,
} from '../../../../interface/share.interface';
import { v4 as uuidv4 } from 'uuid';
import Swal from 'sweetalert2';
import { ContentEditorComponent } from '../../../components/content-editor/content-editor.component';
import { FileBoxComponent } from '../../../components/file-box/file-box.component';
import { format, toZonedTime } from 'date-fns-tz';
import { ApiCode, ApprovalStatus } from '../../../../enum/share.enum';

export enum TYPE {
  TYPE = '前台最新消息',
}
@Component({
  selector: 'app-news-edit',
  standalone: false,

  templateUrl: './news-edit.component.html',
  styleUrl: './news-edit.component.scss',
})
export class NewsEditComponent implements OnInit, AfterViewInit {
  @ViewChild(ContentEditorComponent)
  ContentEditorComponent: ContentEditorComponent | undefined;

  menuItemId: string = '';
  contentData: contentData[] = [];
  typeGroupId: string = '';
  isPendingApproval: boolean = false;

  title: string = '';
  type: string = '';
  typeList: { typeValue: string; typeName: string }[] = [];
  sportType: string = '';
  sportTypeList: { typeValue: string; typeName: string }[] = [];
  startTime: string = '';
  isEnd: boolean = false;
  endTime: string = '';
  isTop: boolean = false;
  createUser: string = '';
  editUser: string = '';
  levelDecision: number = 2;
  userGroup: string = '';
  groupList: {
    userGroupId: string;
    userGroupName: string;
  }[] = [];

  description: string = '';

  cover: string = '';
  coverId: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  constructor(
    private newsService: NewsService,
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id')) {
        this.typeGroupId = queryParams.get('id')!;
        this.getNews();
      } else {
        this.getTypeList(TYPE.TYPE);
        this.getGroupList();
      }
    });
  }

  ngAfterViewInit(): void {
    if (!this.typeGroupId && this.ContentEditorComponent) {
      setTimeout(() => {
        this.ContentEditorComponent!.addEditorBlock('content');
      });
    }
  }

  getGroupList() {
    this.newsService
      .getNewsUserGroup()
      .subscribe((resp: getNewsUserGroupResp) => {
        this.groupList = resp.data;
      });
  }

  getTypeList(type: string) {
    this.shareService.getTypeList(type).subscribe({
      next: (resp: getTypeListResp) => {
        if (type === TYPE.TYPE) {
          this.typeList = resp.data;
        } else {
          this.sportTypeList = resp.data.filter((itme) => {
            return itme.typeValue !== '0';
          });
        }
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  getNews() {
    this.contentData = [];
    this.newsService.getNews(this.typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        if (resp.code === ApiCode.SUCCESS) {
          this.title = resp.data.titleName;
          this.type = resp.data.type;
          this.sportType = resp.data.sportType;
          this.startTime = resp.data.startTime;
          this.isEnd = resp.data.endTime ? true : false;
          this.endTime = resp.data.endTime;
          this.isTop = resp.data.isTop;
          this.coverId = resp.data.coverDataId;
          this.cover = resp.data.coverUrl;
          this.editUser = resp.data.editUser;
          this.createUser = resp.data.createUser;
          this.userGroup = resp.data.userGroupId;
          this.levelDecision = resp.data.levelDecision;
          this.isPendingApproval = resp.data.isPendingApproval;
          this.reason = resp.data.reason;
          this.reviewFileName = resp.data.reviewFileName;
          this.reviewFileUrl = resp.data.reviewFileUrl;
          this.description = resp.data.description;
          resp.data.new_NewsTagDatas.map((item) => {
            this.contentData.push({
              id: uuidv4(),
              type: item.tagName as EDITORTYPE,
              data: item.tagData,
            });
          });
          if (resp.data.new_NewsTagDatas.length > 0) {
            if (this.ContentEditorComponent) {
              this.ContentEditorComponent.initializeForm(this.contentData);
            }
          } else {
            this.ContentEditorComponent!.addEditorBlock('content');
          }
        }

        this.getTypeList(TYPE.TYPE);
        this.getGroupList();
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  getContentData(contentData: contentData[]) {
    this.contentData = contentData;
  }

  changeIsEnd(newValue: boolean) {
    if (newValue === false) {
      this.endTime = '';
    }
  }

  selectGalleryImage() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.cover = resp.data.previewImageUrl;
          this.coverId = resp.data.previewImageDataId;
        }
      });
  }

  cancel() {
    history.back();
    // this.router.navigate([`/manage/${this.menuItemId}/news/list`]);
  }
  save() {
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
    }).then((result) => {
      if (result.isConfirmed) {
        if (!this.userGroup) {
          Swal.fire('警告', `尚未選擇群組`, 'warning');
          return;
        }
        if (!this.title) {
          Swal.fire('警告', `尚未填寫標題`, 'warning');
          return;
        }
        if (!this.type) {
          Swal.fire('警告', `尚未選擇類型`, 'warning');
          return;
        }
        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateNewsReq = {
          menuitemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          titleName: this.title,
          userGroupId: this.userGroup,
          type: this.type,
          startTime: this.startTime
            ? format(
                toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          endTime: this.endTime
            ? format(
                toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          isTop: this.isTop,
          coverDataId: this.coverId,
          levelDecision: this.levelDecision,
          new_NewsTagDatas: newsData,
          lang: sessionStorage.getItem('lang') || 'zh',
          isNews: true,
          description: this.description,
        };
        this.newsService.createUpdateNews(req).subscribe({
          next: (resp: defaultItem) => {
            if (resp.code === 200) {
              Swal.fire('成功', `儲存成功`, 'success').then(() => {
                this.router.navigate([`/manage/${this.menuItemId}/news/list`]);
              });
            } else {
              Swal.fire('失敗', `${resp.message}`, 'error');
            }
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'News',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }

    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
    }).then((result) => {
      if (result.isConfirmed) {
        if (!this.title) {
          Swal.fire('警告', `尚未填寫標題`, 'warning');
          return;
        }
        if (!this.userGroup) {
          Swal.fire('警告', `尚未選擇群組`, 'warning');
          return;
        }
        if (!this.type) {
          Swal.fire('警告', `尚未選擇類型`, 'warning');
          return;
        }
        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateNewsReq = {
          menuitemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          titleName: this.title,
          userGroupId: this.userGroup,
          type: this.type,
          startTime: this.startTime
            ? format(
                toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          endTime: this.endTime
            ? format(
                toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
                "yyyy-MM-dd'T'HH:mm:ss",
                { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
              )
            : '',
          isTop: this.isTop,
          coverDataId: this.coverId,
          levelDecision: this.levelDecision,
          new_NewsTagDatas: newsData,
          lang: sessionStorage.getItem('lang') || 'zh',
          isNews: true,
          description: this.description,
        };
        this.newsService.createUpdateNews(req).subscribe({
          next: (resp: createUpdateResp) => {
            if (resp.code === 200) {
              this.router.navigate(['manage/view'], {
                queryParams: {
                  menuItemId: this.menuItemId,
                  typeGroupId: resp.data,
                  type: 'News',
                  status: ApprovalStatus.BeforeApproval,
                },
              });
            } else {
              Swal.fire('失敗', `${resp.message}`, 'error');
            }
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }
}

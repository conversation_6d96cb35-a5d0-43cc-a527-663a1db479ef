<div>
    <div class="news-layout">
        <span class="white">
            <h1>{{status}}-電子報</h1>
        </span>
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">電子報名稱</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>電子報名稱</mat-label>
                    <input matInput type="text" [(ngModel)]="name">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">關鍵字</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>關鍵字</mat-label>
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">出刊日期</span>
                </div>
                <div>
                    <mat-form-field appearance="outline">
                        <input matInput [matDatepicker]="pickerStart" [(ngModel)]="publishTime" [min]="today"
                            readonly />
                        <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                        <mat-datepicker #pickerStart></mat-datepicker>
                    </mat-form-field>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">模板</span>
                </div>
                <mat-radio-group [(ngModel)]="template">
                    <mat-radio-button [value]="1">
                        <div class="template-layout">
                            模板一&nbsp;&nbsp;
                            <img src="assets/images/ENewsletter/template1.png" alt="模板一">
                        </div>
                    </mat-radio-button>
                    <mat-radio-button [value]="2">
                        <div class="template-layout">
                            模板二&nbsp;&nbsp;
                            <img src="assets/images/ENewsletter/template2.png" alt="模板二">
                        </div>
                    </mat-radio-button>
                    <mat-radio-button [value]="3">
                        <div class="template-layout">
                            模板三&nbsp;&nbsp;
                            <img src="assets/images/ENewsletter/template3.png" alt="模板三">
                        </div>
                    </mat-radio-button>
                </mat-radio-group>

            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">組報</span>
                </div>
                <mat-radio-group [(ngModel)]="groupReport">
                    <mat-radio-button [value]="1">最新消息</mat-radio-button>
                    <mat-radio-button [value]="2">徵才訊息</mat-radio-button>
                </mat-radio-group>
                <div>
                    <button mat-flat-button (click)="addGroupReport()"> 新增</button>
                </div>
                <div class="group-report-layout">
                    @for (item of groupReportList; track item; let i = $index) {
                    <div class="group-report-item">
                        <span>{{item.title}}</span>
                        <div>
                            @if(!$first){
                            <mat-icon (click)="moveUp(i)">arrow_upward</mat-icon>
                            }
                            @if(!$last){
                            <mat-icon (click)="moveDown(i)">arrow_downward_alt</mat-icon>
                            }
                            <i class="material-icons" (click)="deleteGroupReport(item)">close</i>
                        </div>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
    <app-content-editor [editContentData]="contentData" [isENewsletter]="true"
        (contentData)="getContentData($event)"></app-content-editor>
    <div class="news-layout">
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{createUser}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editUser}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="cancel()">回上一頁</button>
            <button mat-flat-button (click)="save(saveStatus.SAVE)">存檔</button>
            <button mat-flat-button (click)="save(saveStatus.SAVELEAVE)">存檔並離開</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
        <app-loading [loading]="loading"></app-loading>
    </div>
</div>
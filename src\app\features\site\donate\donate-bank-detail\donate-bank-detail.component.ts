import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { DonateService } from '../../../../core/services/donate.service';
import { ShareService } from '../../../../core/services/share.service';
import {
  donateItem,
  getDonateItemListReq,
  getDonateItemListResp,
} from '../../../../interface/donate.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';

@Component({
  selector: 'app-donate-bank-detail',
  standalone: false,

  templateUrl: './donate-bank-detail.component.html',
  styleUrl: './donate-bank-detail.component.scss',
})
export class DonateBankDetailComponent {
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  donateItemList: donateItem[] = [];
  startDate: string = '';
  endDate: string = '';
  keyword: string = '';

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService,
    private donateService: DonateService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDonateItemList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getDonateItemList();
  }

  getDonateItemList() {
    let req: getDonateItemListReq = {
      keyword: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.donateService.getDonateItemList(req).subscribe({
      next: (resp: getDonateItemListResp) => {
        this.loading = false;
        this.donateItemList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.nowPage = 1;
    this.getDonateItemList();
  }

  export() {}

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getDonateItemList();
  }
}

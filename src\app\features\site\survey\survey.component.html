<div>
    <div>
        <div class="news-layout">
            <span class="white">
                <h1>問卷編輯</h1>
            </span>
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="export()">匯出</button>
        </div>

        <div class="news-layout">
            <div class="contents" cdkDropList cdkDropListLockAxis="y" (cdkDropListDropped)="drop($event)">
                <div class="level-block">
                    <div class="title-ctn" style="margin-bottom: 10px;">
                        <span class="title">關鍵字</span>
                    </div>
                    <mat-form-field class="example-form-field" appearance="outline">
                        <mat-label>關鍵字</mat-label>
                        <input matInput type="text" [(ngModel)]="keyword">
                    </mat-form-field>
                </div>
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title">發布時間</span>
                    </div>
                    <div style="margin: 1em;">
                        <mat-form-field appearance="outline">
                            <mat-label>發布日期</mat-label>
                            <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startTime"
                                (dateChange)="endTime=''" readonly />
                            <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                            <button mat-icon-button matSuffix (click)="startTime=''">
                                <mat-icon>close</mat-icon>
                            </button>
                            <mat-datepicker #pickerStart></mat-datepicker>
                        </mat-form-field>&nbsp;&nbsp;&nbsp;
                        <mat-form-field appearance="outline">
                            <mat-label>發布時間</mat-label>
                            <input matInput [matTimepicker]="startTimepicker" [(ngModel)]="startTime"
                                [ngModelOptions]="{updateOn: 'blur'}" [matTimepickerMax]="maxStartTime">
                            <mat-timepicker interval="1h" #startTimepicker />
                            <mat-timepicker-toggle [for]="startTimepicker" matSuffix />
                        </mat-form-field>&nbsp;&nbsp;&nbsp;
                        <mat-form-field appearance="outline">
                            <mat-label>結束日期</mat-label>
                            <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endTime" [min]="startTime"
                                readonly />
                            <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                            <button mat-icon-button matSuffix (click)="endTime=''">
                                <mat-icon>close</mat-icon>
                            </button>
                            <mat-datepicker #pickerEnd></mat-datepicker>
                        </mat-form-field>&nbsp;&nbsp;&nbsp;
                        <mat-form-field appearance="outline">
                            <mat-label>結束時間</mat-label>
                            <input matInput [matTimepicker]="endTimepicker" [(ngModel)]="endTime"
                                [ngModelOptions]="{updateOn: 'blur'}" [matTimepickerMin]="minEndTime">
                            <mat-timepicker interval="1h" #endTimepicker />
                            <mat-timepicker-toggle [for]="endTimepicker" matSuffix />
                        </mat-form-field>&nbsp;&nbsp;&nbsp;
                    </div>
                </div>
                @for (item of surveyItem;let index=$index ;track item;) {
                <span class="block" cdkDrag>
                    <div style="display: flex;justify-content: space-between;align-items: center;">
                        <label class="title" [attr.for]="item.fieldName">
                            <span *ngIf="item.required" class="required">*</span>
                            {{ item.fieldName }}
                        </label>
                        <div class="close-btn">
                            <button mat-flat-button style="color:white;background-color: green;"
                                (click)="edit(index)">編輯</button>
                            <button mat-flat-button style="color:white;background-color: red;"
                                (click)="remove(index)">刪除</button>
                        </div>
                    </div>
                    <app-question-option [surveyItem]="item" [index]="index"></app-question-option>
                </span>
                }
                <span class="block-end add" (click)="create()">
                    <i class="material-icons">add</i>
                </span>
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title">審核層級</span>
                        <mat-radio-group [(ngModel)]="levelDecision">
                            <mat-radio-button [value]="1">一層決</mat-radio-button>
                            <mat-radio-button [value]="2">二層決</mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title">消息建立者 {{createUser}}</span>
                    </div>
                </div>
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title">最後修改者 {{editUser}}</span>
                    </div>
                </div>
                @if(reason){
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                    </div>
                </div>
                }
                @if(reviewFileUrl){
                <div class="level-block">
                    <div class="title-ctn">
                        <span class="title">審核檔案 : <a [href]="reviewFileUrl"
                                target="_blank">{{reviewFileName}}</a></span>
                    </div>
                </div>
                }

                <div class="close-btn">
                    <button mat-flat-button type="button" (click)="saveSurvey()">存檔</button>
                    <button mat-flat-button type="button" (click)="view()">預覽 </button>
                </div>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-setting-block',
  standalone: false,

  templateUrl: './setting-block.component.html',
  styleUrl: './setting-block.component.scss'
})
export class SettingBlockComponent {
  @Input() isRight = true;
  @Input() isLayout = true;
  @Input() move = true;
  @Input() requireFunctionPolicy: string | null = null;
  @Output() setting = new EventEmitter();
  @Output() layout = new EventEmitter();

  get policyOk() {
    if (!this.requireFunctionPolicy) {
      return true;
    }
    const functionPolicies = sessionStorage.getItem('functionPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(this.requireFunctionPolicy) > -1) {
      return true;
    }
    return false;
  }

  constructor() {

  }

  ngOnInit(): void {

  }

  settingBtnClick() {
    this.setting.emit();
  }

  layoutBtnClick() {
    this.layout.emit();
  }

}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { createrorUpdateBannerReq, getBannerListResp, } from '../../shared/models/banner.model';
import { environment } from '../../../environments/environment';
import { defaultItem } from '../../interface/share.interface';
import { ShareService } from './share.service';

@Injectable({
  providedIn: 'root',
})
export class BannerService {

  constructor(private http: HttpClient,
    private shareService: ShareService
  ) { }

  /**
   * 取得指定子網站的橫幅圖片列表
   *
   * @param webSiteId 子網站唯一識別號
   */
  getBannerList(): Observable<getBannerListResp> {
    const lang = this.shareService.getLang();
    return this.http.get<getBannerListResp>(`api/Banner/GetBannerList?lang=${lang}`);
  }

  getBannerListBackstage(): Observable<getBannerListResp> {
    const lang = this.shareService.getLang();
    return this.http.get<getBannerListResp>(`api/Manage/Banner/GetBannerList?lang=${lang}`);
  }



  getFile(id: string): Observable<string> {
    return this.http.get<string>(
      environment.dataServerUrl + `/api/Storage/${id}/realPath`
    );
  }

  createrorUpdateBanner(req: createrorUpdateBannerReq): Observable<defaultItem> {
    return this.http.post<defaultItem>('api/Manage/Banner/UpdateOrInsertBanner', req);
  }

  deleteBanner(id: string): Observable<defaultItem> {
    return this.http.delete<defaultItem>('api/Manage/Banner/DeleteBanner', {
      params: { bannerId: id },
    });
  }

  updateBannerEnable(): Observable<defaultItem> {
    return this.http.put<defaultItem>(`api/Manage/MenuItem/EnableHomeItem`, { item: 'banner' });
  }
}

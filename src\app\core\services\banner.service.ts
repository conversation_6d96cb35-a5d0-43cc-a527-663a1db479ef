import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Banner, createrBannerReq } from '../../shared/models/banner.model';
import { environment } from '../../../environments/environment';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class BannerService {
  private bannerApi: string = '/api/Banner';

  constructor(private http: HttpClient) {}

  /**
   * 取得指定子網站的橫幅圖片列表
   *
   * @param webSiteId 子網站唯一識別號
   */
  getBannerList(webSiteId?: string): Observable<Banner[]> {
    return this.http.get<Banner[]>(`${this.bannerApi}?webSiteId=${webSiteId}`);
  }

  getFile(id: string): Observable<string> {
    return this.http.get<string>(
      environment.dataServerUrl + `/api/Storage/${id}/realPath`
    );
  }

  createrBanner(req: createrBannerReq) {
    const formData = new FormData();
    if (req.bannerId) {
      formData.append('bannerId', req.bannerId);
    }
    if (req.mobileFile) {
      formData.append('mobileFile', req.mobileFile);
    }
    if (req.mobileDataId) {
      formData.append('mobileDataId', req.mobileDataId);
    }
    if (req.uploadType) {
      formData.append('uploadType', req.uploadType);
    }
    if (req.content) {
      formData.append('content', req.content);
    }
    if (req.url) {
      formData.append('url', req.url);
    }

    formData.append('websiteId', req.websiteId);
    formData.append('pcDataId', req.pcDataId);
    return this.http.post('api/Manage/Banner/InsertOrUpdateBanner', formData);
  }

  deleteBanner(id: string): Observable<defaultItem> {
    return this.http.delete<defaultItem>('api/Manage/Banner', {
      params: { id: id },
    });
  }
}

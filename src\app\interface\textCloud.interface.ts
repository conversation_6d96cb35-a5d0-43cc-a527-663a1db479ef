import { defaultItem } from './share.interface';

export interface getTextCloudListReq {
  text: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getTextCloudListResp extends defaultItem {
  data: {
    data: textCloudItem[];
    totalCount: number;
    totalPage: number;
    currentPage: number;
  };
}

export interface textCloudItem {
  wordCloudId: string;
  text: string;
  weight: number;
  color?: string;
}

export interface createOrUpdateTextCloudReq {
  wordCloudId?: string;
  text: string;
  weight: number;
  lang: string;
  color?: string;
}

// 變數定義

.list-container {
    padding: 10px 20px;
    max-width: 1200px;
    width: 100%;
    align-items: center;
    margin: 0 auto;
}

.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;
    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;
        h1 {
            color: #2eaddb;
        }
        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;
        }
    }
}

.add-layout {
    display: flex;
    justify-content: flex-end;
}

.user-search {
    padding: 10px 0;
}

// 書櫃網格佈局
.bookcase-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    margin-bottom: 2rem;
}

// 書籍卡片樣式
.book-card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    text-align: center;
    transition: transform 0.2s ease-in-out;

    &:hover {
        transform: translateY(-5px);
    }
}

.book-cover {
    position: relative;
    width: 100%;
    /* 設定 2:3 的高寬比，確保封面比例正確 */
    padding-top: 150%;
    overflow: hidden;
}

.cover-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.book-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    padding: 0.2rem;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.book-status {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    padding: 10px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    border-top: 1px solid #e5e7eb;
}

.action-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease-in-out;

    &:first-child {
        border-right: 1px solid #e5e7eb;
    }

    &.edit-btn {
        color: #4b5563;
        &:hover {
            background-color: #d1e5ff;
        }
    }

    &.delete-btn {
        color: #ef4444;
        &:hover {
            background-color: #fee2e2;
        }
    }
}

.top {
    margin-top: 10px;
    margin-left: 4.7rem;
    display: flex;
    align-self: start;
    border: solid #00bcd4;
    border-radius: 20px;
    width: 60px;
    align-items: center;
    justify-content: center;
    color: #005cbb;
}

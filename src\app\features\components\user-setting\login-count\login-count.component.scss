.setting-layout {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    h2 {
      margin: 0;
      display: flex;
      align-items: center;
    }

    .close {
      font-size: 40px;
      color: #7f7f7f;
      cursor: pointer;

      &:hover {
        color: #000;
      }
    }
  }

  .contents {
    margin: 1em 0;
    display: flex;
    flex-direction: column;
    width: 100%;

    .count {
      font-size: 3em;
      margin: 0;
      color: #4d63bc;
    }

    .title {
      line-height: 2;
    }

    .chart {
      height: 400px;
      margin-top: -5px;
    }

    .btn {
      margin-left: 10px;
    }
  }

  .btns {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
}

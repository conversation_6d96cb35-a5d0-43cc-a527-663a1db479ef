import { Component } from '@angular/core';
import {
  donateItem,
  getDonateItemListReq,
  getDonateItemListResp,
  updateDonateItemReq,
} from '../../../../interface/donate.interface';
import { DonateService } from '../../../../core/services/donate.service';
import { HttpErrorResponse } from '@angular/common/http';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../interface/share.interface';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../../core/services/share.service';
import { DonateItemDialogComponent } from './donate-item-dialog/donate-item-dialog.component';

@Component({
  selector: 'app-donate-item-manage',
  standalone: false,

  templateUrl: './donate-item-manage.component.html',
  styleUrl: './donate-item-manage.component.scss',
})
export class DonateItemManageComponent {
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  donateItemList: donateItem[] = [];
  keyword: string = '';

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService,
    private donateService: DonateService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDonateItemList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getDonateItemList();
  }

  getDonateItemList() {
    let req: getDonateItemListReq = {
      keyword: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.donateService.getDonateItemList(req).subscribe({
      next: (resp: getDonateItemListResp) => {
        console.log(resp);
        this.loading = false;
        this.donateItemList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.nowPage = 1;
    this.getDonateItemList();
  }

  add() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: DonateItemDialogComponent,
          showHeader: true,
          title: '新增捐款項目',
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getDonateItemList();
      });
  }

  edit(item: donateItem) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: DonateItemDialogComponent,
          showHeader: true,
          title: '編輯',
          data: item,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getDonateItemList();
      });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.donateService.deleteDonateItem(id).subscribe({
          next: (resp: defaultItem) => {
            this.loading = false;
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                  this.getDonateItemList();
                })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  activeChange(item: donateItem) {
    const originalEnable = item.enable;
    let req: updateDonateItemReq = {
      ...item,
      enable: !item.enable,
    };
    this.donateService.updateDonateItem(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code !== 200) {
          Swal.fire('失敗', resp.message, 'error');
          item.enable = !originalEnable;
        } else {
          item.enable = !item.enable;
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
        item.enable = !originalEnable;
      },
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getDonateItemList();
  }
}

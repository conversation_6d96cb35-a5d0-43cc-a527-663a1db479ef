import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { AnalysisService } from '../../../../core/services/analysis.service';
import { EChartsCoreOption } from 'echarts/core';

@Component({
  selector: 'app-login-count',
  standalone: false,

  templateUrl: './login-count.component.html',
  styleUrl: './login-count.component.scss'
})
export class LoginCountComponent {
  form: any = {
    length: null,
    startTime: new Date(),
    endTime: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 7),
    PerDayView: []
  };

  ConutUpoptions = {
    duration: 2,
    separator: '',
    prefix: '',
  };

  sub: number = 0;
  options: any;
  popupData: any;

  constructor(
    public dialogRef: MatDialogRef<DialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _analysis: AnalysisService
  ) {

  }

  ngOnInit(): void {
    this.popupData = this.data.data;
    if (this.popupData) {
      this._analysis.getUserLoginCount(this.popupData.id).subscribe(sub => {
        this.form.length = sub.toString.length;
        if (this.form.length < 8) {
          for (let index = 1; index < 9 - Number(this.form.length); index++) {
            this.ConutUpoptions.prefix += '0';
          }
        } else {
          this.ConutUpoptions.prefix = '';
        }
        this.sub = sub;
        console.log('options', this.ConutUpoptions);
        // new CountUp('myTargetElement', sub, this.ConutUpoptions).start();
      });
      this.query();
    } else {
      this.dialogRef.close();
    }
  }

  query() {
    this._analysis.getUserPerDayLoginCount(this.popupData.id,
      Math.floor(new Date(this.form.startTime).getTime() / 1000),
      Math.floor(new Date(this.form.endTime).getTime() / 1000)).subscribe(sub => {
        this.form.PerDayView = sub;
        this.setecharts(this.form.PerDayView);
      });
  }

  setecharts(value: any) {
    const xdata: any = [];
    const series: any = [];
    value.map((res: any, index: number) => {
      xdata.push(res.year + '/' + res.month + '/' + res.day);
      series.push(res.loginCount);
    });

    this.options = {
      title: {
        text: '登入次數區間表'
      },
      tooltip: {
        trigger: 'axis'
      },
      toolbox: {
        show: true,
        feature: {
          magicType: {
            type: ['line', 'bar'],
            title: { line: '折線圖', bar: '柱狀圖' }
          },
          restore: {
            title: '還原'
          },
          saveAsImage: {
            title: '儲存'
          }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xdata
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} 人數'
        }
      },
      series: [{
        data: series,
        type: 'line',
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }]
    };
  }
}

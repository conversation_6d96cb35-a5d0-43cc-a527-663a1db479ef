import { defaultItem } from './share.interface';

export interface getHtmlResp extends defaultItem {
  data: {
    keyword: string
    menuitemId: string;
    typeGroupId: string;
    content: string;
    creater: string;
    editor: string;
    publishDateTime: string;
    currentLevel: number;
    lang: string;
    title: string;
    isPendingApproval: boolean;
    levelDecision: number;
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

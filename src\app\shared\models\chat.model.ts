export interface Chat {
  id?: string;
  username?: string;
  userId?: string;
  message?: string;
  channelId?: string;
  time? : string;
  isDeleted?: boolean;
  webSiteId?: string;
  chatQuestionId?: string;
  isServe?: boolean;
  serveMessage?: any[];
  ansNumbers?: number;
  isEndChoose?: boolean;
  type?: string ;
}

export interface ChatArray {
  currentChatQuesionId: string| "" ;
  chatData: Chat[] | [] ;
}

export interface ChatRoom {
  id?: string;
  userId: string| "";
  name?: string;
  msg?: string;
}

export interface ChatQuestionCount {
  mineQuestionCount?: number;
  allQuestionCount?: number;
  mineCompeleteCount?: number;
}

export interface WmgAuthMemberData {
  id?: string;
  memberIdentity?: string;
  engFirstName?: string;
  engLastName?: string;
  originalName?: string;
  email?: string;
  mobilePhone?: string;
  sex?: string;
  dateOfBirth?: string;
  personalPhoto?: string;
  nationality?: string;
  passportLastName?: string;
  passportFirstName?: string;
  documentType?: string;
  documentNumber?: string;
  documentPhoto?: string;
  houseNumber?: string;
  district?: string;
  registrationAddress?: string;
  city?: string;
  postalCode?: string;
  countryOfResidence?: string;
  mobileNumber?: string;
  firstLanguage?: string;
  secondLanguage?: string;
  emgLastName?: string;
  emgFirstName?: string;
  emgMobile?: string;
  emgRelation?: string;
  playerInfo?: string;
  nonPlayerInfo?: string;
  supporterInfo?: string;
  paraAthleteAssistantInfo?: string;
  paymentRecord?: string;
  json?: string;
  sourceSystem?: string;
}

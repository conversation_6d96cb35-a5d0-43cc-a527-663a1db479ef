import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { ShareService } from '../../../../core/services/share.service';
import { FaqService } from '../../../../core/services/faq.service';
import {
  createUpdateFaqReq,
  getFaqResp,
} from '../../../../interface/faq.interface';
import { v4 as uuidv4 } from 'uuid';
import { HttpErrorResponse } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { EDITORTYPE } from '../../../../enum/editor.enum';
import {
  contentData,
  contentItem,
} from '../../../../interface/editor.interface';
import {
  createUpdateResp,
  defaultItem,
  getTypeListResp,
} from '../../../../interface/share.interface';
import { ContentEditorComponent } from '../../../components/content-editor/content-editor.component';
import { ApprovalStatus } from '../../../../enum/share.enum';

@Component({
  selector: 'app-faq-edit',
  standalone: false,

  templateUrl: './faq-edit.component.html',
  styleUrl: './faq-edit.component.scss',
})
export class FaqEditComponent implements OnInit, AfterViewInit {
  @ViewChild(ContentEditorComponent)
  ContentEditorComponent: ContentEditorComponent | undefined;

  menuItemId: string = '';
  contentData: contentData[] = [];
  typeGroupId: string = '';
  faqType: string = '';
  question: string = '';
  answer: string = '';
  levelDecision: number = 2;
  creater: string = '';
  editor: string = '';
  typeList: {
    typeValue: string;
    typeName: string;
  }[] = [];

  isPendingApproval: boolean = false;
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  constructor(
    private faqService: FaqService,
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.getTypeList();
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id')) {
        this.typeGroupId = queryParams.get('id')!;
        this.getFaq();
      }
    });
  }

  ngAfterViewInit(): void {
    if (!this.typeGroupId && this.ContentEditorComponent) {
      setTimeout(() => {
        this.ContentEditorComponent!.addEditorBlock('content');
      });
    }
  }

  getTypeList() {
    this.shareService.getTypeList('問題分類').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
      },
    });
  }
  getFaq() {
    this.faqService.getFaq(this.typeGroupId).subscribe({
      next: (resp: getFaqResp) => {
        this.creater = resp.data.createUser;
        this.editor = resp.data.editUser;
        this.faqType = resp.data.faqType;
        this.question = resp.data.question;
        this.isPendingApproval = resp.data.isPendingApproval;
        this.levelDecision = resp.data.levelDecision;
        this.reason = resp.data.reason;
        this.reviewFileName = resp.data.reviewFileName;
        this.reviewFileUrl = resp.data.reviewFileUrl;
        if (resp.data.answer.length > 0) {
          this.answer = (resp.data.answer[0].tagData as contentItem).content;
        }
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  cancel() {
    this.router.navigate([`/manage/${this.menuItemId}/faq/list`]);
  }
  save() {
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
    }).then((result) => {
      if (result.isConfirmed) {
        const status: string = this.typeGroupId ? '編輯' : '新增';
        let newsData: { tagName: string; dataString: string | null }[] = [];
        newsData.push({
          tagName: EDITORTYPE.Content,
          dataString: JSON.stringify({ content: this.answer }),
        });
        let req: createUpdateFaqReq = {
          menuitemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          faqType: this.faqType,
          faqName: this.question,
          new_NewsTagDatas: newsData,
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
        };
        this.faqService.createUpdateFaq(req).subscribe({
          next: (resp: defaultItem) => {
            resp.code === 200
              ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
                  this.router.navigate([`/manage/${this.menuItemId}/faq/list`]);
                })
              : Swal.fire('失敗', `${resp.message}`, 'error');
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'FAQ',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
    }).then((result) => {
      if (result.isConfirmed) {
        const status: string = this.typeGroupId ? '編輯' : '新增';
        let newsData: { tagName: string; dataString: string | null }[] = [];
        newsData.push({
          tagName: EDITORTYPE.Content,
          dataString: JSON.stringify({ content: this.answer }),
        });

        let req: createUpdateFaqReq = {
          menuitemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          faqType: this.faqType,
          faqName: this.question,
          new_NewsTagDatas: newsData,
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
        };
        this.faqService.createUpdateFaq(req).subscribe({
          next: (resp: createUpdateResp) => {
            resp.code === 200
              ? this.router.navigate(['manage/view'], {
                  queryParams: {
                    menuItemId: this.menuItemId,
                    typeGroupId: resp.data,
                    type: 'FAQ',
                    status: ApprovalStatus.BeforeApproval,
                  },
                })
              : Swal.fire('失敗', `${resp.message}`, 'error');
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }
}

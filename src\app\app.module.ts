import { NgModule } from '@angular/core';
import { MAT_DATE_LOCALE } from '@angular/material/core';

/** Others */
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { AppComponent } from './app.component';
import { ManageComponent } from './features/manage/manage.component';
import { HomeComponent } from './features/components/home/<USER>';
import { HeaderComponent } from './shared/components/header/header.component';
import { FooterComponent } from './shared/components/footer/footer.component';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { NgxLoadingModule } from 'ngx-loading';
import { CountUpModule } from 'ngx-countup';
import { LoadingComponent } from './shared/utilities/loading/loading.component';
import { BannerComponent } from './features/components/banner/banner.component';
import 'hammerjs';
import { NgxHmCarouselModule } from 'ngx-hm-carousel';
import { SafePipe } from './shared/pipes/safe.pipe';
import { SettingBlockComponent } from './features/components/setting-block/setting-block.component';

import { MenuItemComponent } from './features/components/menu-item/menu-item.component';
import { AuthComponent } from './features/components/dialogs/auth/auth.component';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { DialogComponent } from './shared/components/dialog/dialog.component';
import { AuthInterceptor } from './core/interceptors/auth.interceptor';
import { MenuSettingComponent } from './features/components/menu-setting/menu-setting.component';
import { TypeSettingComponent } from './features/components/type-setting/type-setting.component';
import { ErrorInterceptor } from './core/interceptors/http-error.interceptor';
import { CreateMenuNameComponent } from './features/components/create-menu-name/create-menu-name.component';
import { HyperLinkEditorComponent } from './features/components/menu-setting/hyper-link-editor/hyper-link-editor.component';
import { FileBoxComponent } from './features/components/file-box/file-box.component';
import { ClipboardModule } from 'ngx-clipboard';
import { TagSettingComponent } from './features/components/file-box/tag-setting/tag-setting.component';
import { VideoCreateComponent } from './features/components/video-create/video-create.component';
import { LinkEditComponent } from './features/components/link-edit/link-edit.component';
import { MorePageSettingComponent } from './features/components/more-page-setting/more-page-setting.component';
import { RoleSettingComponent } from './features/components/role-setting/role-setting.component';
import { UserSettingComponent } from './features/components/user-setting/user-setting.component';
import { CreateUserComponent } from './features/components/create-user/create-user.component';
import { ChangePasswordComponent } from './features/components/change-password/change-password.component';
import { CreateRoleComponent } from './features/components/role-setting/create-role/create-role.component';
import { RoleContextMenuComponent } from './features/components/role-setting/role-context-menu/role-context-menu.component';
import { RegistrationSettingComponent } from './features/components/user-setting/registration-setting/registration-setting.component';
import { NgxEchartsModule } from 'ngx-echarts';
import { ContentEditorComponent } from './features/components/content-editor/content-editor.component';
import { FroalaEditorModule, FroalaViewModule } from 'angular-froala-wysiwyg';
import { LoginCountComponent } from './features/components/user-setting/login-count/login-count.component';
import { MaterialModule } from './material.module ';
import { ContentComponent } from './features/site/content/content.component';
import { NewsEditComponent } from './features/site/news/news-edit/news-edit.component';
import { NewsListComponent } from './features/site/news/news-list/news-list.component';
import { NewsComponent } from './features/site/news/news.component';
import { ImgsComponent } from './features/site/imgs/imgs.component';
import { ImgsListComponent } from './features/site/imgs/imgs-list/imgs-list.component';
import { ImgsEditComponent } from './features/site/imgs/imgs-edit/imgs-edit.component';
import { LineComponent } from './features/site/line/line.component';
import { LineEditComponent } from './features/site/line/line-edit/line-edit.component';
import { LineListComponent } from './features/site/line/line-list/line-list.component';
import { ShareResourceComponent } from './features/site/share-resource/share-resource.component';
import { ShareResourceListComponent } from './features/site/share-resource/share-resource-list/share-resource-list.component';
import { ShareResourceDialogComponent } from './features/components/dialogs/share-resource-dialog/share-resource-dialog.component';
import { FaqComponent } from './features/site/faq/faq.component';
import { FaqListComponent } from './features/site/faq/faq-list/faq-list.component';
import { FaqEditComponent } from './features/site/faq/faq-edit/faq-edit.component';
import { HtmlComponent } from './features/site/html/html.component';
import { CropperImageComponent } from './features/components/dialogs/cropper-image/cropper-image.component';
import { ImageCropperComponent } from 'ngx-image-cropper';
import { BannerDialogComponent } from './features/components/dialogs/banner/banner-dialog/banner-dialog.component';
import { AddBannerDialogComponent } from './features/components/dialogs/banner/add-banner-dialog/add-banner-dialog.component';
import { VideoComponent } from './features/site/video/video.component';
import { VideoListComponent } from './features/site/video/video-list/video-list.component';
import { CreateWebComponent } from './features/site/create-web/create-web.component';
import { CreateWebListComponent } from './features/site/create-web/create-web-list/create-web-list.component';
import { MorePageSettingListComponent } from './features/site/more-page-setting/more-page-setting-list/more-page-setting-list.component';
import { MorePageSettingContextMenuComponent } from './features/site/more-page-setting/more-page-setting-context-menu/more-page-setting-context-menu.component';
import { MorePageSettingEditComponent } from './features/site/more-page-setting/more-page-setting-edit/more-page-setting-edit.component';
import { FileLinkComponent } from './features/components/dialogs/file-link/file-link.component';

import { CheckCodeComponent } from './features/components/dialogs/auth/check-code/check-code.component';
import { ChangePwComponent } from './changePw/change-pw/change-pw.component';
import { ForgetPwComponent } from './changePw/forget-pw/forget-pw.component';
import { SurveyComponent } from './features/site/survey/survey.component';
import { AddQuestionComponent } from './features/site/survey/add-question/add-question.component';
import { QuestionOptionComponent } from './features/site/survey/question-option/question-option.component';
import { GroupSettingComponent } from './features/components/group-setting/group-setting.component';
import { CreateGroupComponent } from './features/components/group-setting/create-group/create-group.component';
import { SelectUserComponent } from './features/components/group-setting/select-user/select-user.component';
import { EditQuestionComponent } from './features/site/survey/edit-question/edit-question.component';
import { ReviewListComponent } from './features/site/review/review-list/review-list.component';
import { ViewComponent } from './features/site/view/view.component';
import { HtmlZipEditorComponent } from './features/components/menu-setting/html-zip-editor/html-zip-editor.component';
import { ENewsletterComponent } from './features/site/e-newsletter/e-newsletter.component';
import { ENewsletterEditComponent } from './features/site/e-newsletter/e-newsletter-edit/e-newsletter-edit.component';
import { ENewsletterListComponent } from './features/site/e-newsletter/e-newsletter-list/e-newsletter-list.component';
import { GroupReportDialogComponent } from './features/components/group-report-dialog/group-report-dialog.component';
import { ENewsletterViewComponent } from './features/site/e-newsletter/e-newsletter-view/e-newsletter-view.component';
import { ViewENewsLetterColumnDialogComponent } from './features/components/view-enews-letter-column-dialog/view-enews-letter-column-dialog.component';
import { ENewsLetterManageDialogComponent } from './features/components/enews-letter-manage-dialog/enews-letter-manage-dialog.component';
import { EbookComponent } from './features/site/ebook/ebook.component';
import { EbookListComponent } from './features/site/ebook/ebook-list/ebook-list.component';
import { EbookEditComponent } from './features/site/ebook/ebook-edit/ebook-edit.component';
import { FileDescriptionComponent } from './features/components/file-box/file-description/file-description.component';
import { TextCloudComponent } from './features/site/text-cloud/text-cloud.component';
import { TextCloudSettingDialogComponent } from './features/components/text-cloud-setting-dialog/text-cloud-setting-dialog.component';
import { AchievementsSettingComponent } from './features/components/achievements-setting/achievements-setting.component';
import { AuditLogComponent } from './features/site/audit-log/audit-log.component';
import { CustomerServiceSettingComponent } from './features/site/customer-service-setting/customer-service-setting.component';
import { DonateItemManageComponent } from './features/site/donate/donate-item-manage/donate-item-manage.component';
import { DonateBankDetailComponent } from './features/site/donate/donate-bank-detail/donate-bank-detail.component';
import { DonateAccountReconciliationComponent } from './features/site/donate/donate-account-reconciliation/donate-account-reconciliation.component';
import { DonateItemDialogComponent } from './features/site/donate/donate-item-manage/donate-item-dialog/donate-item-dialog.component';
import { ElUploadBoxComponent } from './features/components/file-box/el-upload-box/el-upload-box.component';
import { CustomerServiceDialogComponent } from './features/components/customer-service-dialog/customer-service-dialog.component';
import { DonateAccountReconciliationItemDialogComponent } from './features/site/donate/donate-account-reconciliation/donate-account-reconciliation-item-dialog/donate-account-reconciliation-item-dialog.component';
import { ClickLogListComponent } from './features/site/click-log-list/click-log-list.component';
import { GuestViewLogComponent } from './features/site/guest-view-log/guest-view-log.component';
import { SearchKeywordLogComponent } from './features/site/search-keyword-log/search-keyword-log.component';
import { SortComponent } from './utils/sort/sort.component';
import { ChartComponent } from './features/site/chart/chart.component';
import { ChartDialogComponent } from './features/site/chart/dialog/chart-dialog/chart-dialog.component';
import { ChartColumnComponent } from './features/site/chart/dialog/chart-dialog/chart-column/chart-column.component';
import { DatasetManageComponent } from './features/site/chart/dataset-manage/dataset-manage.component';
import { SmtpSettingComponent } from './features/components/smtp-setting/smtp-setting.component';
import { EbookViewComponent } from './features/site/ebook/ebook-view/ebook-view.component';
import { VideoEditComponent } from './features/site/video/video-edit/video-edit.component';
import { VideoViewComponent } from './features/site/video/video-view/video-view.component';
import { ENewsLetterSmtpSettingComponent } from './features/components/e-news-letter-smtp-setting/e-news-letter-smtp-setting.component';
import { SetSortDialogComponent } from './features/components/set-sort-dialog/set-sort-dialog.component';

@NgModule({
  declarations: [
    AppComponent,
    ManageComponent,
    HomeComponent,
    HeaderComponent,
    FooterComponent,
    LoadingComponent,
    BannerComponent,
    SafePipe,
    SettingBlockComponent,
    MenuItemComponent,
    AuthComponent,
    DialogComponent,
    MenuSettingComponent,
    TypeSettingComponent,
    CreateMenuNameComponent,
    HyperLinkEditorComponent,
    NewsComponent,
    NewsListComponent,
    NewsEditComponent,
    FileBoxComponent,
    TagSettingComponent,
    VideoCreateComponent,
    LinkEditComponent,
    MorePageSettingComponent,
    RoleSettingComponent,
    UserSettingComponent,
    CreateUserComponent,
    ChangePasswordComponent,
    CreateRoleComponent,
    RoleContextMenuComponent,
    LoginCountComponent,
    RegistrationSettingComponent,
    ContentEditorComponent,
    LoginCountComponent,
    ContentComponent,
    ImgsComponent,
    ImgsListComponent,
    ImgsEditComponent,
    LineComponent,
    LineEditComponent,
    LineListComponent,
    ShareResourceComponent,
    ShareResourceListComponent,
    ShareResourceDialogComponent,
    FaqComponent,
    FaqListComponent,
    FaqEditComponent,
    HtmlComponent,
    CropperImageComponent,
    BannerDialogComponent,
    AddBannerDialogComponent,
    VideoComponent,
    VideoListComponent,
    CreateWebComponent,
    CreateWebListComponent,
    MorePageSettingListComponent,
    MorePageSettingContextMenuComponent,
    MorePageSettingEditComponent,
    FileLinkComponent,
    ChangePwComponent,
    ForgetPwComponent,
    CheckCodeComponent,
    SurveyComponent,
    AddQuestionComponent,
    QuestionOptionComponent,
    GroupSettingComponent,
    CreateGroupComponent,
    SelectUserComponent,
    EditQuestionComponent,
    ReviewListComponent,
    ViewComponent,
    HtmlZipEditorComponent,
    ENewsletterComponent,
    ENewsletterEditComponent,
    ENewsletterListComponent,
    GroupReportDialogComponent,
    ENewsletterViewComponent,
    ViewENewsLetterColumnDialogComponent,
    ENewsLetterManageDialogComponent,
    EbookComponent,
    EbookListComponent,
    EbookEditComponent,
    FileDescriptionComponent,
    TextCloudComponent,
    TextCloudSettingDialogComponent,
    AchievementsSettingComponent,
    AuditLogComponent,
    CustomerServiceSettingComponent,
    DonateItemManageComponent,
    DonateBankDetailComponent,
    DonateAccountReconciliationComponent,
    DonateItemDialogComponent,
    ElUploadBoxComponent,
    CustomerServiceDialogComponent,
    DonateAccountReconciliationItemDialogComponent,
    ClickLogListComponent,
    GuestViewLogComponent,
    SearchKeywordLogComponent,
    SortComponent,
    ChartComponent,
    ChartDialogComponent,
    ChartColumnComponent,
    DatasetManageComponent,
    SmtpSettingComponent,
    EbookViewComponent,
    VideoEditComponent,
    VideoViewComponent,
    ENewsLetterSmtpSettingComponent,
    SetSortDialogComponent,
  ],
  imports: [
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    NgxLoadingModule.forRoot({}),
    NgxHmCarouselModule,
    ClipboardModule,
    CountUpModule,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts'),
    }),
    FroalaEditorModule.forRoot(),
    FroalaViewModule.forRoot(),
    AppRoutingModule,
    ImageCropperComponent,
  ],
  providers: [
    provideAnimationsAsync(),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true,
    },
    { provide: MAT_DATE_LOCALE, useValue: 'zh' },
    provideHttpClient(withInterceptorsFromDi()),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

<div class="auth-layout">
    <h1>{{data.status==="ADD"?"新增":"編輯"}}</h1>
    <!-- <div class="block">
        <div class="title-ctn">
            <span class="title">類型</span>
        </div>
        <mat-form-field appearance="outline">
            <mat-select [(ngModel)]="sportType">
                @for ( item of typeList; track $index) {
                <mat-option [value]="item.typeValue">{{item.typeName}}</mat-option>
                }
            </mat-select>
        </mat-form-field>
    </div> -->

    <div class="block">
        <div class="title-ctn">
            <span class="title">名稱</span>
        </div>
        <mat-form-field class="example-form-field" appearance="outline">
            <mat-label>名稱</mat-label>
            <input matInput type="text" [(ngModel)]="name">
        </mat-form-field>
    </div>

    <div class="block">
        <div class="title-ctn">
            <span class="title">檔案</span>
        </div>
        <div>
            <button mat-flat-button class="btn" (click)="selectGalleryFile()">
                選擇檔案
            </button>&nbsp;&nbsp;&nbsp;
            @if(file){
            <span class="file">{{file}}</span>
            }
        </div>

    </div>

    <div class="btn-group">
        <button mat-flat-button (click)="close()">取消</button>
        <button mat-flat-button (click)="create()">建立</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
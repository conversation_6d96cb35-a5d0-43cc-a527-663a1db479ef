import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Link } from '../../shared/models/link.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LinkService {
  private mngLinkApi: string = '/api/Manage/Link';

  constructor(
    private http: HttpClient
  ) { }

  /**
     * 取得指定選單項目的Link列表
     *
     * @param menuItemId 選單項目唯一識別號
     * @param enable 啟用停用過濾
     */
  getMenuItemLinkList(
    menuItemId?: string,

    enable?: boolean
  ): Observable<Link[]> {
    let url = '/api/Manage/Link';
    const queryList = [];

    if (menuItemId !== null && menuItemId !== undefined) {
      queryList.push('menuItemId=' + encodeURIComponent(menuItemId.toString()));
    }

    if (enable !== null && enable !== undefined) {
      queryList.push('enable=' + encodeURIComponent(enable.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<Link[]>(url);
  }

  /**
     * 刪除指定實例
     *
     * @param id 唯一識別號
     */
  delete(
    id: string
  ): Observable<any> {
    return this.http.delete<any>(`${this.mngLinkApi}/${id}`);
  }

  /**
     * 建立實例
     *
     * @param instance 實例內容
     */
  create(
    instance: Link
  ): Observable<Link> {
    return this.http.post<Link>(this.mngLinkApi, instance);
  }

  /**
     * 更新實例
     *
     * @param instance 實例內容
     */
  update(
    instance: Link
  ): Observable<Link> {
    return this.http.put<Link>(this.mngLinkApi, instance)
  }
}

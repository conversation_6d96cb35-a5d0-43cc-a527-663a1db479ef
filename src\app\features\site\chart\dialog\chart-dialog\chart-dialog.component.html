<div style="display: flex; overflow-y: auto; overflow-x: clip;">
    <div style="min-width: 50%;overflow-y: auto;border-right: solid #E0E0E0 0.5px;">
        <form [formGroup]="form">
            <div style="display: flex;align-items: center;justify-content: left;margin: 2em;">
                <mat-form-field style="width: 48%;" appearance="outline">
                    <mat-label>區塊標題:</mat-label>
                    <input matInput formControlName="title" placeholder="標題">
                    @if(form.controls['title'].hasError('maxlength')){
                    <mat-error>
                        區塊標題字數限制10個字
                    </mat-error>
                    }
                </mat-form-field>
            </div>
            <div style="display: flex;align-items: center;justify-content: left;margin: 2em;">
                <mat-form-field style=" width: 50%;margin-right: 2em;" appearance="outline">
                    <mat-label>區塊尺寸(橫)</mat-label>
                    <mat-select formControlName="colspan">
                        @for( item of [1,2,3,4]; track item) {
                        <mat-option [value]="item">
                            {{item}}
                        </mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <mat-form-field style=" width: 50%;" appearance="outline">
                    <mat-label>區塊尺寸(縱)</mat-label>
                    <mat-select formControlName="rowspan">
                        @for( item of [1,2,3,4]; track item) {
                        <mat-option [value]="item">
                            {{item}}
                        </mat-option>
                        }
                    </mat-select>
                </mat-form-field>
            </div>

            <app-chart-column [blockListItem]="blockListItem" [menuitemId]="menuitemId"
                (customerChart)="getCustomerChart($event)" (loadingStatus)="getLoading($event)"
                (customerChartStatus)="getCustomerChartStatus($event)" (formStatusChange)="getFormStatus($event)"
                #appCustomerChart></app-chart-column>

            <div class="close-btn">
                <button mat-stroked-button (click)="close()">取消</button>
                <button mat-flat-button (click)="submit()" [disabled]="!form.valid||!isFormValid">確定</button>
            </div>
        </form>
    </div>
    <div style=" min-width: 50%;overflow-y: hidden;">
        @if(customerChartStatus){
        <div style="margin-top: 5em; ">
            <!-- todo 圖表顯示 -->
            <!-- <div echarts [options]="getOptions()"></div> -->
        </div>
        }@else{
        <div class="setting-msg">
            <span>1.選擇圖表所需資料集</span><br />
            <span>2.選擇圖表類型</span><br />
            <span>3.選擇圖表顯示之類別</span><br />
            <span>4.選擇圖表顯示之計算列</span><br />
        </div>
        }
    </div>
</div>
<div>
    <div class="news-layout">
        <span class="white">
            <h1>{{status}}-書櫃</h1>
        </span>
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">標題</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>標題</mat-label>
                    <input matInput type="text" [(ngModel)]="title">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">作者</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>作者</mat-label>
                    <input matInput type="text" [(ngModel)]="author">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">簡介</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>簡介</mat-label>
                    <input matInput type="text" [(ngModel)]="introduction">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">大綱</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>大綱</mat-label>
                    <input matInput type="text" [(ngModel)]="outline">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">小標題(原住民族語)</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>小標題(原住民族語)</mat-label>
                    <input matInput type="text" [(ngModel)]="littleTitle">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">小標題(中文)</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>小標題(中文)</mat-label>
                    <input matInput type="text" [(ngModel)]="littleTitleZh">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">期數</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>期數</mat-label>
                    <input matInput type="text" [(ngModel)]="period">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">發布時間</span>
                </div>
                <div style="display: felx; margin: 1em;">
                    <mat-form-field appearance="outline">
                        <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startTime"
                            (ngModelChange)="endTime = ''" readonly />
                        <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                        <mat-datepicker #pickerStart></mat-datepicker>
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    <mat-slide-toggle class="example-margin" [(ngModel)]="isEnd" (ngModelChange)="changeIsEnd($event)">
                        設定到期日
                    </mat-slide-toggle>&nbsp;&nbsp;&nbsp;
                    @if(isEnd){
                    <mat-form-field appearance="outline">
                        <input matInput [matDatepicker]="pickerStart" [(ngModel)]="endTime" [min]="startTime"
                            readonly />
                        <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                        <mat-datepicker #pickerStart></mat-datepicker>
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    }
                    <mat-slide-toggle class="example-margin" [(ngModel)]="isTop">
                        置頂
                    </mat-slide-toggle>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">封面上傳</span>
                </div>
                <p>&nbsp;※&nbsp;只能上傳jpg、png檔案</p>
                <div>
                    <button mat-flat-button class="btn" (click)="selectGalleryImage()">
                        選擇圖片
                    </button>
                </div>
                @if(coverId){
                <div style="display: flex;flex-direction: column;">
                    <div>
                        <img [src]="cover" alt="" width="100px"><br />
                        <mat-icon style="margin-left:30px" (click)="cover='' " (click)="coverId='' ">delete</mat-icon>
                    </div>
                </div>
                }
            </div>
            <div class="block">
                <div class="file-layout">
                    <span class="title">上傳檔案</span>
                    <div>
                        <input type="file" #fileInput accept=".aio" (change)="selectGalleryFile($event)">
                        <button mat-flat-button color="primary" (click)="fileInput.click()">
                            選擇檔案
                        </button>
                        @if(fileName){
                        <span class="file-name">{{fileName}}</span>
                        }@if(ebookLink){
                        <a [href]="ebookLink" target="_blank">電子書預覽</a>
                        }
                    </div>
                </div>
                <span>*只能上傳AIO檔案，首頁檔名請設為index.html</span>
            </div>
        </div>
    </div>
    <div class="news-layout">
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{createUser}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editUser}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="cancel()">回上一頁</button>
            <button mat-flat-button (click)="save()">存檔</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
        <app-loading [loading]="loading"></app-loading>
    </div>
</div>
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { finalize, tap } from 'rxjs';
import Swal from 'sweetalert2';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor() {}

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    const shouldModify = this.shouldModifyRequest(req.url);
    let ok: string;
    // Get the auth token from the service. 
    const lang: string = location.pathname.split('/')[1];
    const authToken = sessionStorage.getItem('token')!
      ? sessionStorage.getItem('token')!
      : '';

    const authReq = shouldModify
      ? req.clone({
          headers: req.headers.set('Authorization', authToken),
        })
      : req;

    // send cloned request with header to the next handler.
    return next.handle(authReq).pipe(
      tap({
        next: (event) => {
          ok = event instanceof HttpResponse ? 'succeeded' : '';
        },
        // Operation failed; error is an HttpErrorResponse
        error: (_error) => {},
      }),
      finalize(() => {})
    );
  }
  private shouldModifyRequest(url: string): boolean {
    const exemptUrls = [
      'api/Manage/UserData/CheckEmail',
      'api/Manage/UserData/UpdateMima',
    ];
    return !exemptUrls.includes(url);
  }
}

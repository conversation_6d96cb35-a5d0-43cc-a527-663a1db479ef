<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>智能客服設定-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <!-- <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button> -->
            </div>
            <div>
                <button mat-flat-button (click)="add()">新增</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">項次</th>
                            <th width="100px">問題</th>
                            <th width="70px">AI回答</th>
                            <th width="70px">啟用</th>
                            <th width="70px">建立日期</th>
                            <th width="200px">功能</th>
                    </thead>
                    <tbody>
                        @for (item of customerServicesList; track item; let i = $index;) {
                        <tr>
                            <td data-label="項次">{{i+1}}</td>
                            <td data-label="問題">{{item.question}}</td>
                            <td data-label="AI回答">
                                <div [innerHTML]="getAnswer(item.answer)"></div>
                            </td>
                            <td data-label="啟用">
                                <mat-slide-toggle class="example-margin" [checked]="item.enable"
                                    (change)="activeChange(item)">
                                </mat-slide-toggle>
                            </td>
                            <td data-label="建立日期">{{item.createTime|date:'yyyy/MM/dd'}}</td>
                            <td data-label="功能">
                                <button mat-flat-button (click)="edit(item)">編輯</button>&nbsp;
                                <button mat-flat-button class="danger" (click)="delete(item.id)">刪除</button>
                            </td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="7" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
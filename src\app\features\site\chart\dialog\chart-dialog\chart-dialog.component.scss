::ng-deep .mat-mdc-dialog-container {
    border-radius: 10px;
}

// ::ng-deep .mat-mdc-dialog-surface {
//     overflow: hidden;
// }

select {
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    line-height: 1.42857;
    padding: 8px 16px;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    border-radius: 5px;
}

.select_control {
    margin: 2px;
    padding: 0;
    overflow: hidden;
}

.select_control select {
    border: 1px solid #ccc;
    padding: 10px 16px 10px 10px;
    margin: 0;
}
.select_control select > option {
    border-bottom: 1px solid #ccc;
    padding: 4px;
}

.select_control_auto select {
    width: auto;
}

.input-style {
    margin: 2px 4px 4px 0px;
    padding: 8px 16px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    display: inline-block;
    line-height: 1.42857;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    border-radius: 5px;
}
.input-style:focus {
    border: 1px solid #00b4ff;
}

.spinner-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 998;
    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"] {
    -moz-appearance: textfield;
}

// /* 網頁捲軸【寬度】 */
// ::-webkit-scrollbar {
//     width: 20px;
// }

// /* 網頁捲軸【背景】顏色 */
// ::-webkit-scrollbar-track {
//     background: #f5f5f5;
// }

// /* 網頁捲軸【把手】顏色 */
// ::-webkit-scrollbar-thumb {
//     border-radius: 5px;
//     background: #4284f3;
// }

// /* 網頁捲軸【滑過時】把手的顏色 */
// ::-webkit-scrollbar-thumb:hover {
//     background: #32b3e2;
// }

.setting-msg {
    margin-top: 2em;
    display: flex;
    align-items: center;
    flex-direction: column;
    font-size: 26px;
}

.normal-background-color {
    border: solid #bfbfbf;
    border-radius: 0.5em;
}

.close-btn {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    button {
        margin: 0 10px;
    }
}

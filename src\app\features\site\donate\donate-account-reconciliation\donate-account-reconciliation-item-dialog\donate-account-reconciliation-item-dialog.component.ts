import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DonateService } from '../../../../../core/services/donate.service';
import { ShareService } from '../../../../../core/services/share.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  addOrUpdateDonateAccountReconciliationReq,
  donateAccountReconciliationItem,
  getDonateAccountReconciliationResp,
} from '../../../../../interface/donate.interface';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../../../interface/share.interface';
import { format } from 'date-fns';

@Component({
  selector: 'app-donate-account-reconciliation-item-dialog',
  standalone: false,

  templateUrl: './donate-account-reconciliation-item-dialog.component.html',
  styleUrl: './donate-account-reconciliation-item-dialog.component.scss',
})
export class DonateAccountReconciliationItemDialogComponent {
  form: FormGroup;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: donateAccountReconciliationItem;
    },
    private dialogRef: MatDialogRef<DonateAccountReconciliationItemDialogComponent>,
    private donateService: DonateService,
    private shareService: ShareService,
    private formBuilder: FormBuilder
  ) {
    this.form = this.formBuilder.group({
      donateItem: ['', Validators.required],
      donorName: ['', Validators.required],
      donorInfo: ['自然人', Validators.required],
      donateNumber: ['', Validators.required],
      donateAmount: ['', Validators.required],
      donateWay: ['', Validators.required],
      donateDate: ['', Validators.required],
      idNumber: ['', Validators.required],
      isNoName: [false],
      email: [''],
      address: [''],
      trueAmount: ['', Validators.required],
      receiptNumber: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.getDonateAccountReconciliation();

    this.form.get('isNoName')?.valueChanges.subscribe((isNoName: boolean) => {
      if (isNoName) {
        this.form.get('donorName')?.patchValue('無名氏', { emitEvent: false });
      } else {
        this.form.get('donorName')?.patchValue('', { emitEvent: false });
      }
    });

    this.form.get('donorName')?.valueChanges.subscribe((donorName: string) => {
      // 只有當手動輸入內容時才進行處理
      if (donorName && donorName !== '無名氏') {
        this.form.get('isNoName')?.patchValue(false, { emitEvent: false });
      }
    });
  }

  getDonateAccountReconciliation() {
    if (this.data.data && this.data.data.donateInfoId) {
      this.donateService
        .getDonateAccountReconciliation(this.data.data.donateInfoId)
        .subscribe({
          next: (resp: getDonateAccountReconciliationResp) => {
            this.form.patchValue({
              donateItem: resp.data.donateItem,
              donorName: resp.data.donorName,
              donateNumber: resp.data.donateNumber,
              donateAmount: resp.data.donateAmount,
              donateWay: resp.data.donateWay,
              donorInfo: resp.data.donorInfo,
              donateDate: resp.data.donateTime,
              isNoName: resp.data.isNoName,
              idNumber: resp.data.idNumber,
              email: resp.data.email,
              address: resp.data.address,
              trueAmount: resp.data.trueAmount,
              receiptNumber: resp.data.receiptNumber,
            });
          },
        });
    }
  }

  submit() {
    let req: addOrUpdateDonateAccountReconciliationReq = {
      donateItem: this.form.value.donateItem,
      donorName: this.form.value.donorName,
      isNoName: this.form.value.isNoName,
      donateNumber: this.form.value.donateNumber,
      donateAmount: this.form.value.donateAmount,
      donorInfo: this.form.value.donorInfo,
      donateWay: this.form.value.donateWay,
      donateTime: this.form.value.donateDate ? format(new Date(this.form.value.donateDate), 'yyyy-MM-dd') : '',
      idNumber: this.form.value.idNumber,
      email: this.form.value.email,
      address: this.form.value.address,
      trueAmount: this.form.value.trueAmount,
      receiptNumber: this.form.value.receiptNumber,
    };

    if (this.data.data && this.data.data.donateInfoId) {
      let status: string = '編輯';
      req.donateDataId = this.data.data.donateInfoId;
      this.donateService.addOrUpdateDonateAccountReconciliation(req).subscribe({
        next: (resp: defaultItem) => {
          resp.code === 200
            ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
              this.dialogRef.close();
            })
            : Swal.fire('錯誤', resp.message, 'error');
        },
      });
    } else {
      let status: string = '新增';
      this.donateService.addOrUpdateDonateAccountReconciliation(req).subscribe({
        next: (resp: defaultItem) => {
          resp.code === 200
            ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
              this.dialogRef.close();
            })
            : Swal.fire('錯誤', resp.message, 'error');
        },
      });
    }
  }

  close() {
    this.dialogRef.close();
  }
}

<div class="iframe-container">
    <iframe #myIframe [src]="url"></iframe>
</div>

<button class="review-switch-btn">
    @if(openStatus){
    <mat-icon (click)="openStatus = false">menu_open</mat-icon>
    }@else{
    <mat-icon (click)="openStatus = true">menu</mat-icon>
    }
</button>

@if(openStatus){
@if(status === ApprovalStatus.BeforeApproval){
<div id="reviewPanel" class="review-panel">
    <h2>審核意見</h2>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">取消</button>
        <button mat-flat-button (click)="submitForReview()">送審</button>
        @if(getPolicy('PublishPermission')){

        <button mat-flat-button (click)="submitForPublish()">發布</button>
        }
    </div>
</div>
}
@if(status === ApprovalStatus.AfterApproval){
<div id="reviewPanel" class="review-panel">
    <h2>審核意見</h2>
    <div class="review-actions">
        <mat-radio-group [(ngModel)]="approval">
            <mat-radio-button [value]="true">通過</mat-radio-button>
            <mat-radio-button [value]="false">不通過</mat-radio-button>
        </mat-radio-group>
        @if(!approval){
        <textarea placeholder="請輸入不通過原因" [(ngModel)]="reason">
            </textarea>
        }
    </div>
    @if(!approval){
    <div class="block">
        <div class="file-layout">
            <div>
                <span class="title">上傳檔案</span>
                <input type="file" #fileInput accept=".zip,.jpg,.png,.pdf" (change)="selectGalleryFile($event)">
                <button mat-flat-button color="primary" (click)="fileInput.click()">
                    選擇檔案
                </button>
                @if(fileName){
                <span class="file-name">{{fileName}}</span>
                }
            </div>
        </div>
        <span>*只能上傳jpg或png或zip或pdf檔案</span>
    </div>
    }
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">取消</button>
        <button mat-flat-button (click)="send()">確認</button>
    </div>
</div>
}
@if(status === ApprovalStatus.ViewApproval){
<div id="reviewPanel" class="review-panel">
    <h2>審核狀態:審核中</h2>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">返回</button>
    </div>
</div>
}
@if(status === ApprovalStatus.EndApproval){
<div id="reviewPanel" class="review-panel">
    <h2>審核狀態:已通過</h2>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">返回</button>
    </div>
</div>
}
}


<app-loading [loading]="loading"></app-loading>
import { Component, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { GeoLocationService } from '../../../core/services/geoLocation.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';

@Component({
  selector: 'app-get-location-address',
  standalone: false,

  templateUrl: './get-location-address.component.html',
  styleUrl: './get-location-address.component.scss'
})
export class GetLocationAddressComponent {
  form!: FormGroup;
  providerList: any = [];
  popupData: any;

  constructor(
    private _fb: FormBuilder,
    private _geoLocationService: GeoLocationService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<DialogComponent>,
  ) {

  }

  ngOnInit(): void {
    this.popupData = this.data.data;
    this.initForm();

    if (this.popupData) {
      this._geoLocationService.getRoleFunctionPolicy().subscribe(x => {
        this.form.get('address')?.setValue(this.popupData.address);
        this.form.get('provider')?.setValue(this.popupData.provider || 'Google');
        this.providerList = [{
          "type": "Google",
          "name": "Google Map",
          "description": "Google Map"
        }];
      });
    } else {
      this.providerList = [{
        "type": "Google",
        "name": "Google Map",
        "description": "Google Map"
      }];
    }
  }

  initForm() {
    this.form = this._fb.group({
      address: ['', Validators.required],
      provider: [null, Validators.required]
    });
  }

  submit(formVal: any) {
    this.dialogRef.close(formVal);
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GetLocationAddressComponent } from './get-location-address.component';

describe('GetLocationAddressComponent', () => {
  let component: GetLocationAddressComponent;
  let fixture: ComponentFixture<GetLocationAddressComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [GetLocationAddressComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(GetLocationAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>書櫃-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <!-- <div>
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="search()">搜尋</button>
            </div> -->
            <div class="add-layout">
                <button mat-flat-button (click)="addEbook()">+ 新增</button>
            </div>
        </div>
        <div class="contents">
            <div class="bookcase-grid">
                @for ( item of ebookList; track item) {
                <!-- 書籍卡片範例 1 -->
                <div class="book-card">
                    <div class="book-cover">
                        <img [src]="item.coverUrl" [alt]="item.title" class="cover-image">
                    </div>
                    <div *ngIf="item.isTop" class="top">
                        <span>置頂</span>
                    </div>
                    <h3 class="book-title">{{item.title}}</h3>
                    <span class="book-status">消息狀態 : {{item.status}}</span>
                    <span class="book-status">排序 : {{item.sort}}</span>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" (click)="setSort(item)">排序</button>
                        <button class="action-btn edit-btn"
                            (click)="editEbook(item.bookId, item.typeGroupId)">編輯</button>
                        <button class="action-btn delete-btn"
                            (click)="deleteEbook(item.bookId, item.typeGroupId)">刪除</button>
                    </div>
                </div>
                }
            </div>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
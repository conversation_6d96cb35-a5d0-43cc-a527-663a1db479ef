<div class="popup-layout">
  <div
    class="popup-block"
    [ngStyle]="{ width: data.width, height: data.height }"
  >
    <span class="popup-close" *ngIf="data.showHeader || data.title">
      <label style="color: rgb(0, 0, 0)">{{ data.title }}</label>
      <span class="close-btn">
        <!-- <span class="left" style="background-color: rgb(0, 0, 0)"></span>
        <span class="right" style="background-color: rgb(0, 0, 0)"></span> -->
        <mat-icon
          aria-hidden="false"
          aria-label="close icon"
          fontIcon="close"
          mat-dialog-close
        ></mat-icon>
      </span>
    </span>
    <div class="popup-content">
      <ng-container *ngComponentOutlet="data.contentTemplate"></ng-container>
    </div>
  </div>
</div>

import { EChartsOption } from 'echarts';
import { defaultItem } from './share.interface';

export interface getDatasetListResp extends defaultItem {
  data: {
    totalPage: number;
    totalCount: number;
    data: datasetItem[];
  };
}

export interface datasetItem {
  datasetId: string;
  name: string;
}

export interface getDatasetXListResp extends defaultItem {
  data: string;
}

export interface getDatasetYListResp extends defaultItem {
  data: datasetYItem[];
}

export interface datasetYItem {
  datasetYId: string;
  nameY: string;
}

export interface getBlockListResp extends defaultItem {
  data: blockListItem[];
}

export interface blockListItem {
  blockId: string;
  datasetId: string;
  name: string;
  colspan: number;
  rowspan: number;
  options: any;
  searchOptions: string[];
  dataset: EChartsOption;
  errorStatus?: boolean;
  errorMsg?: string;
}

export interface getBlockResp extends defaultItem {
  data: blockItem;
}

export interface blockItem {
  name: string;
  colspan: number;
  rowspan: number;
  options: string; //bar, line, pie
  datasetId: string;
  xAxis: string;
  datasetYId: string[];
}

export interface createOrUpdateBlockReq {
  blockId?: string;
  menuitemId: string;
  datasetId: string;
  name: string;
  colspan: number;
  rowspan: number;
  options: string; //bar, line, pie
  searchOptions: string[];
  lang: string;
}

export interface updateBlockSortReq {
  menuitemId: string;
  firstBlockId: string;
  secondBlockId: string;
}

import { Type } from '@angular/core';
import { MenuItem } from './menuItem.model';
import { Video } from './video.model';

export interface DialogData {
  title?: string;
  width?: string;
  height?: string;
  showHeader?: boolean;
  isMultiple?: boolean;
  contentTemplate: Type<any>;
}

export interface typeSettingDialog extends DialogData {
  dataGroup: MenuItem;
}

export interface typeMenuItem {
  name: string;
  type: string;
  tag: string;
  url: string;
  selected: boolean;
  MenuItemType?: string;
  MenuItemId?: string;
  linkUrl?: string;
}

export interface createMenuNameData {
  selectedData: typeMenuItem | MenuItem;
  method: string;
  parentId: string;
}

export interface createMenuNameDialog extends DialogData {
  dataGroup: createMenuNameData;
}

export interface createFileTage extends DialogData {
  selector: 'Image' | 'Video' | 'File';
}

export interface linkEditorDialog extends DialogData {
  dataContent: MenuItem;
}

export interface fileBoxDialog extends DialogData {
  data: fileBoxDigData;
}

export interface fileBoxDigData {
  type: string;
  isMultiple: boolean;
  isOnlyView: boolean;
}

export interface videoCreateDialog extends DialogData {
  data: Video | string | { type: string };
  isENewsletter?: boolean;
}

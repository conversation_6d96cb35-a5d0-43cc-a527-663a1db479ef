<div class="Setting-layout">
  <span class="contents">
    <span class="block">
      <span class="title">搜尋</span>
      <input type="text" [(ngModel)]="keyword" (keyup.enter)="getList()">
    </span>
  </span>
  <span class="group-list">
    <span class="group-block" *ngFor="let item of userList">
      <span class="group-name" [ngClass]="{ 'selected': item.selected }" (click)="selectedUser(item)">
        {{ item.name }}
      </span>
    </span>
  </span>
  <span class="lazyload">
    <button mat-icon-button (click)="loadMore()" *ngIf="userList.length < totalCount">
      <mat-icon>keyboard_arrow_down</mat-icon>
    </button>
  </span>
  <app-loading [loading]="loading"></app-loading>
</div>
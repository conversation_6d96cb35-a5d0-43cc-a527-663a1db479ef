import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import { getHtmlResp } from '../../interface/html.interface';
import { createUpdateResp, defaultItem } from '../../interface/share.interface';

export interface getHtmlZipFileResp extends defaultItem {
  data: string;
}

@Injectable({
  providedIn: 'root',
})
export class HtmlService {
  constructor(private httpClient: HttpClient) { }

  getHtml(id: string): Observable<getHtmlResp> {
    return this.httpClient.get<getHtmlResp>('api/Manage/Html/GetHtmlData', {
      params: {
        menuitemId: id,
        lang: sessionStorage.getItem('lang') || 'zh',
      },
    });
  }

  createHtml(
    id: string,
    keyword: string,
    content: string,
    levelDecision: number
  ): Observable<createUpdateResp> {
    return this.httpClient.post<createUpdateResp>(
      'api/Manage/Html/UpdateOrInsertHTML',
      {
        menuitemId: id,
        keyword: keyword,
        content: content,
        levelDecision: levelDecision,
        lang: sessionStorage.getItem('lang') || 'zh',
      }
    );
  }

  getHtmlZipFile(id: string): Observable<getHtmlZipFileResp> {
    return this.httpClient.get<getHtmlZipFileResp>(
      'api/Manage/Html/GetHtmlIndexData',
      {
        params: {
          menuitemId: id,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }

  uploadHtmlZipFile(id: string, file: File): Observable<defaultItem> {
    let formData = new FormData();
    formData.append('MenuitemId', id);
    formData.append('HtmlIndex', file);
    formData.append('Lang', sessionStorage.getItem('lang') || 'zh');
    return this.httpClient.post<defaultItem>(
      'api/Manage/Html/UpLoadHTMLIndex',
      formData
    );
  }
}

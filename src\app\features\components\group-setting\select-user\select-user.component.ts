import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserGroupService } from '../../../../core/services/userGroup.service';
import { UserDataService } from '../../../../core/services/userData.service';
import { PagingOfUserData } from '../../../../shared/models/pagingOfUserData.model';
import { UserData } from '../../../../shared/models/userData.model';

@Component({
  selector: 'app-select-user',
  standalone: false,
  templateUrl: './select-user.component.html',
  styleUrls: ['./select-user.component.scss'],
})
export class SelectUserComponent implements OnInit {
  selectUser: any[] = [];
  groupId: string = '';
  userList: UserData[] = [];
  nowPage: number = 0;
  totalCount: number = 0;
  pageSize: number = 10;

  loading = false;
  keyword: string = '';

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private userGroupService: UserGroupService,
    private userDataService: UserDataService,
    private dialogRef: MatDialogRef<SelectUserComponent>
  ) {
    this.selectUser = this.data.userList;
    this.groupId = this.data.id;
  }

  ngOnInit() {
    this.getList();
  }

  getList() {
    this.loading = true;
    this.userDataService
      .list(
        sessionStorage.getItem('webSiteId') as string,
        this.keyword,
        ['name'],
        null,
        null,
        this.nowPage,
        this.pageSize
      )
      .subscribe((resp: PagingOfUserData) => {
        this.userList = resp.result || [];
        this.totalCount = resp.totalCount || 0;
        this.userList.map((user) => {
          if (this.selectUser.find((y) => y.id === user.id)) {
            user.selected = true;
          }
        });
        this.loading = false;
      });
  }

  selectedUser(user: UserData) {
    user.selected = !user.selected;
    if (user.selected) {
      this.selectUser.push(user);
    } else {
      this.selectUser = this.selectUser.filter((x) => x.id !== user.id);
    }
    this.loading = true;
    this.userGroupService
      .updateUserGroupMembers(
        this.groupId,
        this.selectUser.map((x) => x.id)
      )
      .subscribe(() => (this.loading = false));
  }

  loadMore() {
    this.loading = true;
    this.nowPage = this.nowPage + 1;
    this.userDataService
      .list(
        sessionStorage.getItem('webSiteId') as string,
        this.keyword,
        ['name'],
        null,
        null,
        this.nowPage,
        this.pageSize
      )
      .subscribe((resp: PagingOfUserData) => {
        this.totalCount = resp.totalCount || 0;
        resp.result?.map((item) => {
          this.userList.push(item);
        });
        this.userList.map((user) => {
          if (this.selectUser.find((y) => y.id === user.id)) {
            user.selected = true;
          }
        });
        this.loading = false;
      });
  }

  close() {
    this.dialogRef.close();
  }
}

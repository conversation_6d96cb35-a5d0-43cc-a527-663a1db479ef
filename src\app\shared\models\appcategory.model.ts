export class alias {
  zh_tw!: boolean;
  en!: boolean;
  jp!: boolean;
  kr!: boolean;
}

export class name {
  zh_twName!: string | "";
  enName!: string | "";
  jpName!: string | "";
  krName!: string | "";
}

export class appCategoryList {
  alias!: alias;
  name!: name;
}

export class AppCategoryRequest {
  categoryId?: string;
  imageUrl?: string;
  appCategoryList!: appCategoryList;
  permissions?: string;
  appCategoryLinks!: AppCategoryLinkDto[];
}

export class AppCategoryLinkInfo {
  name?: string;
  url?: string;
  isPrivacyChecked?: boolean;
  privacyDescription?: string;
}

export class linkInFo {
  zh_tw!: AppCategoryLinkInfo;
  en!: AppCategoryLinkInfo;
  jp!: AppCategoryLinkInfo;
  kr!: AppCategoryLinkInfo;
}

export class AppCategoryLinkDto {
  appCategoryLinkId!: string;
  alias!: alias;
  linkInFo!: linkInFo;
  permissions!: string;
}

export class AppCategoryInfo {
  categoryId!: string;
  categoryName!: string;
}

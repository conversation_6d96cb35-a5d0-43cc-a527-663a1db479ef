<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>銷帳作業</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>時間區間 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startDate" (dateChange)="endDate = ''" />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endDate" [min]="startDate" />
                    <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                    <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
                <span>捐款項目 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
        </div>
        <div class="button-group">
            <button mat-flat-button (click)="downloadTemplate()">範本</button>
            <button mat-flat-button (click)="fileInput.click();">ECXEL匯入</button>
            <input type="file" accept=".xlsx" (change)="importExcel($event)" #fileInput  hidden />
            <button mat-flat-button (click)="export()">匯出</button>
            <button mat-flat-button (click)="add()">新增</button>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th>項次</th>
                            <th>捐款項目</th>
                            <th>姓名/單位名稱</th>
                            <th>捐款單號</th>
                            <th>捐款金額</th>
                            <th>捐款方式</th>
                            <th>捐款時間</th>
                            <th>身份證字號/統一編號</th>
                            <th>E-mail</th>
                            <th>地址</th>
                            <th>實收金額</th>
                            <th>收據號碼</th>
                            <th width="80px">核對狀態</th>
                            <th width="80px">公布狀態</th>
                            <th width="50px">功能</th>
                    </thead>
                    <tbody>
                        @for (item of donateItemList; let index=$index;track item;) {
                        <tr>
                            <td data-label="項次"> {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}</td>
                            <td data-label="捐款項目">
                                {{item.donateItem}}
                            </td>
                            <td data-label="姓名/單位名稱">
                                {{item.donorName}}
                            </td>
                            <td data-label="捐款單號">
                                {{item.donateNumber}}
                            </td>
                            <td data-label="捐款金額">
                                {{item.donateAmount}}
                            </td>
                            <td data-label="捐款方式">
                                {{item.donateWay}}
                            </td>
                            <td data-label="捐款時間">{{item.date|date:'yyyy/MM/dd'}}</td>
                            <td data-label="身份證字號/統一編號">{{item.idNumber}}</td>
                            <td data-label="E-mail">{{item.email}}</td>
                            <td data-label="地址">{{item.address}}</td>
                            <td data-label="實收金額">{{item.trueAmount}}</td>
                            <td data-label="收據號碼">{{item.receiptNumber}}</td>
                            <td data-label="核對狀態">
                                <mat-slide-toggle class="example-margin" [checked]="item.isCheck"
                                    (change)="checkChange(item)">
                                </mat-slide-toggle>
                            </td>
                            <td data-label="公布狀態">
                                <mat-slide-toggle class="example-margin" [checked]="item.isPublish"
                                    (change)="publishChange(item)">
                                </mat-slide-toggle>
                            </td>
                            <td data-label="功能">
                                <span class="material-symbols-outlined" (click)="edit(item)">
                                    edit
                                </span>
                                <span class="material-symbols-outlined" (click)="delete(item.donateInfoId)">
                                    delete
                                </span>

                            </td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="15" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize"
                    [hidePageSize]="true" (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
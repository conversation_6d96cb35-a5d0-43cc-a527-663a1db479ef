import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { ShareResourceDialogComponent } from '../../../components/dialogs/share-resource-dialog/share-resource-dialog.component';
import { ShareResourceService } from '../../../../core/services/shareResource.service';
import {
  getShareResourceListReq,
  getShareResourceListResp,
  shareResourceItem,
} from '../../../../interface/shareResource.interface';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-share-resource-list',
  standalone: false,
  templateUrl: './share-resource-list.component.html',
  styleUrl: './share-resource-list.component.scss',
})
export class ShareResourceListComponent {
  loading: boolean = false;
  menuItemId: string = '';
  title: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  shareResourceList: shareResourceItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private matDialog: MatDialog,
    private shareResourceService: ShareResourceService
  ) {}

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getShareResourceList();
    });
  }

  getShareResourceList() {
    let req: getShareResourceListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareResourceService.getShareResourceList(req).subscribe({
      next: (resp: getShareResourceListResp) => {
        this.loading = false;
        this.title = resp.data.title;
        this.shareResourceList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
    });
  }

  addShaeResource() {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: ShareResourceDialogComponent,
          status: 'ADD',
          title: this.title,
          menuItemId: this.menuItemId,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          Swal.fire('成功', '新增成功', 'success').then(() => {
            this.getShareResourceList();
          });
        }
      });
  }

  editShaeResource(item: any) {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: ShareResourceDialogComponent,
          status: 'EDIT',
          title: this.title,
          item: item,
          menuItemId: this.menuItemId,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          Swal.fire('成功', '編輯成功', 'success').then(() => {
            this.getShareResourceList();
          });
        }
      });
  }
  deleteShaeResource(item: shareResourceItem) {
    this.shareResourceService
      .deleteShareResource(item.shareResourceId)
      .subscribe({
        next: () => {
          Swal.fire('成功', '刪除成功', 'success').then(() => {
            this.getShareResourceList();
          });
        },
        error: () => {
          Swal.fire('失敗', '刪除失敗', 'error');
        },
      });
  }

  /** 換頁 */
  changePage($event: any) {
    this.nowPage = $event.pageIndex + 1;
    this.getShareResourceList();
  }
}

.accessibility-reminder-list-layout {
    margin: 0;
    padding: 0; }
  
  .accessibility-reminder-icon {
    margin: 0;
    padding: 10px;
    text-align: center; }
    .accessibility-reminder-icon img {
      width: 150px; }
  
  .accessibility-reminder-title {
    margin: 0;
    padding: 10px;
    text-align: center;
    background-color: #7e0000;
    color: #fff;
    font-size: 0.9em;
    font-weight: bold;
    border-radius: 10px; }
  
  .accessibility-reminder-important {
    margin: 0;
    padding: 10px 10px 0;
    display: flex;
    align-items: center;
    font-size: 1.4em;
    font-weight: bold;
    color: #973f00; }
  
  .accessibility-reminder-important-icon {
    margin: 0;
    padding: 0; }
    .accessibility-reminder-important-icon img {
      width: 35px; }
  
  .accessibility-reminder-cont {
    margin: 0;
    padding: 0 10px 10px;
    list-style: none; }
  
  .accessibility-reminder-list {
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #dedede;
    font-size: 1.25em; }
  
  .accessibility-reminder-list-title {
    font-weight: bold; }
  
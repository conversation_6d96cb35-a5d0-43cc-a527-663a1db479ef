<div>
    <div class="news-layout">
        <span class="white">
            <h1>{{title}}</h1>
        </span>
    </div>
    <app-content-editor [editContentData]="contentData" (contentData)="getContentData($event)"></app-content-editor>
    <div class="news-layout">
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{createUser}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editUser}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <!-- <div class="btn-group">
            <button mat-flat-button (click)="create()">建立</button>
        </div> -->
        <div class="btn-group">
            <button mat-flat-button (click)="save()">存檔</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
    </div>
</div>
<app-loading [loading]="loading"></app-loading>
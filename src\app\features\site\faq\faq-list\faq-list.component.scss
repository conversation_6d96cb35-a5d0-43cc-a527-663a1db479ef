.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;

    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;

        h1 {
            color: #2eaddb;
        }

        .title-description {
            max-width: 50%;
        }

        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;

            .block {
                min-height: 150px;
                display: flex;
                border-bottom: 1px solid #ccc;
                cursor: pointer;
                transition: 0.3s ease-in-out;
                padding: 1em 0;

                &:hover {
                    box-shadow: 0px 0px 9px 0px #ccc;
                }

                &.add {
                    justify-content: center;
                    align-items: center;
                    background-color: #b8d3f3;
                }

                .titles {
                    min-width: 250px;
                    display: flex;
                    flex-direction: column;
                    padding: 10px;

                    img {
                        width: 250px;
                    }

                    h4 {
                        line-height: 1.5;
                    }
                }

                .cont {
                    line-height: 2;
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .title {
                        font-size: 25px;
                        font-weight: bold;
                    }

                    .time {
                        color: #a57868;
                        font-style: italic;
                    }
                }
            }
        }

        .add-layout {
            width: 90%;
            max-width: 1024px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
    }
}

// 搜索區域樣式
.search-section {
    width: 90%;
    max-width: 1024px;
    margin-bottom: 20px;

    .search-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;

        .search-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 16px;

            .material-icons {
                margin-right: 8px;
                color: #3498db;
            }
        }

        .search-form {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;

            .search-field {
                flex: 1;
                min-width: 300px;

                .search-input {
                    width: 100%;

                    ::ng-deep .mat-mdc-form-field-flex {
                        background-color: white;
                        border-radius: 8px;
                    }

                    ::ng-deep .mat-mdc-text-field-wrapper {
                        background-color: white;
                        border-radius: 8px;
                    }
                }
            }

            .search-actions {
                display: flex;
                gap: 12px;
                align-items: center;

                .search-btn {
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    border-radius: 8px;
                    padding: 0 20px;
                    height: 40px;
                    font-weight: 500;
                    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
                    }

                    mat-icon {
                        margin-right: 4px;
                    }
                }

                .reset-btn {
                    border: 2px solid #95a5a6;
                    color: #7f8c8d;
                    border-radius: 8px;
                    padding: 0 20px;
                    height: 40px;
                    font-weight: 500;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: #7f8c8d;
                        color: #2c3e50;
                        background-color: #ecf0f1;
                    }

                    mat-icon {
                        margin-right: 4px;
                    }
                }
            }
        }
    }
}

// 操作區域樣式
.action-section {
    width: 90%;
    max-width: 1024px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;

    .action-info {
        .total-count {
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 500;
        }
    }

    .action-buttons {
        .add-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 8px;
            padding: 0 24px;
            height: 44px;
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
            }

            mat-icon {
                margin-right: 6px;
            }
        }
    }
}

// 響應式設計
@media (max-width: 768px) {
    .search-section .search-container .search-form {
        flex-direction: column;
        align-items: stretch;

        .search-field {
            min-width: unset;
        }

        .search-actions {
            justify-content: center;
        }
    }

    .action-section {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}

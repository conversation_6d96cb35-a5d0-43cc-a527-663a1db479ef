import { Observable } from 'rxjs';
import { Navigation } from './../../shared/models/navigation.model';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { WebSiteLang } from '../../shared/models/webSiteLang.model';

@Injectable({
  providedIn: 'root',
})
export class WebSiteService {
  private websiteApi: string = '/api/WebSite';
  private mngWebsiteApi: string = '/api/Manage/WebSite';

  constructor(private http: HttpClient) {}

  /**
   * 取得指定子網站首頁
   *
   * @param id 子網站唯一識別號
   */
  listNavigation(id: string): Observable<Navigation[]> {
    return this.http.get<Navigation[]>(`${this.websiteApi}/${id}/navigation`);
  }

  /**
   * 取得子網站樣式
   *
   * @param id 子網站唯一識別號
   */
  getWebSiteStyle(id: string): Observable<string> {
    return this.http.get<string>(`${this.websiteApi}/${id}/webSiteStyle`);
  }

  /**
   * 取得LOGO
   *
   * @param id 子網站唯一識別號
   */
  getLogoUrl(id: string): Observable<string> {
    return this.http.get<string>(`${this.websiteApi}/${id}/logo`);
  }

  /**
   * 更新子網站LOGO
   *
   * @param id 子網站唯一識別號
   * @param dataId 檔案DataId
   */
  updateLogo(
    id: string,

    dataId: string
  ): Observable<string> {
    return this.http.put<string>(
      `${this.mngWebsiteApi}/${id}/logo`,
      JSON.stringify(dataId)
    );
  }

  /**
   * 檢驗網站首頁是否已經有項目
   *
   * @param id 子網站唯一識別號
   * @param menuItemId 選單唯一識別號
   */
  hasNavigation(id: string, menuItemId: string): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.mngWebsiteApi}/${id}/navigation/${menuItemId}`
    );
  }

  /**
   * 檢驗網站首頁是否已經有項目
   *
   * @param id 子網站唯一識別號
   * @param menuItemId 選單唯一識別號
   */
  hasHomePageContent(id: string, menuItemId: string): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.mngWebsiteApi}/${id}/homepage/${menuItemId}`
    );
  }

  /**
   * 取得子網站支援MenuItemTypes
   *
   * @param id 子網站唯一識別號
   */
  getAllowTypes(id: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.mngWebsiteApi}/${id}/allowTypes`);
  }

  /**
   * 取得網站語言支援列表
   *
   * @param id 網站唯一識別號
   */
  listLang2(id: string): Observable<WebSiteLang[]> {
    return this.http.get<WebSiteLang[]>(`${this.mngWebsiteApi}/${id}/lang`);
  }

  /**
   * 取得子網站Registerable
   *
   * @param id 子網站唯一識別號
   */
  getRegisterable2(id: string): Observable<boolean> {
    return this.http.get<boolean>(`${this.mngWebsiteApi}/${id}/Registerable`);
  }

  /**
   * 取得子網站RegistrationRequired
   *
   * @param id 子網站唯一識別號
   */
  getRegistrationRequired2(id: string): Observable<string[]> {
    return this.http.get<string[]>(
      `${this.mngWebsiteApi}/${id}/RegistrationRequired`
    );
  }

  /**
   * 更新子網站Registerable
   *
   * @param id 子網站唯一識別號
   * @param registerable 子網站Registerable
   */
  updateRegisterable(
    id: string,

    registerable: boolean
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.mngWebsiteApi}/${id}/Registerable`,
      registerable
    );
  }

  /**
   * 更新子網站RegistrationRequired
   *
   * @param id 子網站唯一識別號
   * @param registrationRequireds 子網站RegistrationRequired
   */
  updateRegistrationRequired(
    id: string,

    registrationRequireds: string[]
  ): Observable<string[]> {
    let url = '/api/Manage/WebSite/{id}/RegistrationRequired';
    return this.http.put<string[]>(
      `${this.mngWebsiteApi}/${id}/RegistrationRequired`,
      registrationRequireds
    );
  }

  /**
   * 取得指定子網站後台網址
   *
   * @param id 子網站唯一識別號
   */
  getWebSiteBackstageUrl(id: string): Observable<string> {
    let url = '/api/Manage/WebSite/{id}/backstage';

    url = url.replace('{id}', id.toString());

    return this.http.get<string>(url);
  }
}

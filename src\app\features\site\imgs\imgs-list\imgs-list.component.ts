import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { NewsService } from '../../../../core/services/news.service';

import { MenuItem } from '../../../../shared/models/menuItem.model';
import { ImgsService } from '../../../../core/services/imgs.service';
import {
  getImgsListReq,
  getImgsListResp,
  ImgsItem,
} from '../../../../interface/imgs.interface';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { SetSortDialogComponent } from '../../../components/set-sort-dialog/set-sort-dialog.component';

@Component({
  selector: 'app-imgs-list',
  standalone: false,

  templateUrl: './imgs-list.component.html',
  styleUrl: './imgs-list.component.scss',
})
export class ImgsListComponent {
  loading: boolean = false;
  menuItemId: string = '';

  title: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  imgsList: ImgsItem[] = [];

  constructor(
    private newsService: NewsService,
    private imgsService: ImgsService,
    private _route: ActivatedRoute,
    private _router: Router,
    public dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getImgsList();
    });
  }

  getImgsList() {
    let req: getImgsListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.imgsService.getImgsList(req).subscribe({
      next: (resp: getImgsListResp) => {
        this.title = resp.data.title;
        this.imgsList = resp.data.data;
        this.totalCount = resp.data.totalCount;
        this.loading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  addImgs() {
    this._router.navigate([`/manage/${this.menuItemId}/img/edit`]);
  }

  editImgs(id: string) {
    this._router.navigate([`/manage/${this.menuItemId}/img/edit`], {
      queryParams: { id: id },
    });
  }

  /** 刪除消息 */
  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.newsService.deleteNews(id).subscribe({
          next: (res) => {
            this.loading = false;
            Swal.fire({
              title: '刪除成功',
              icon: 'success',
              showCancelButton: false,
              reverseButtons: true,
            }).then(() => { });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire({
              title: '刪除失敗',
              icon: 'error',
              showCancelButton: false,
              reverseButtons: true,
            });
          },
        });
      }
    });
  }

  setSort(item: ImgsItem) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        title: '設定排序',
        typeGroupId: item.typeGroupId,
        menuitemId: this.menuItemId,
        sort: item.sort,
        totalCount: this.totalCount,
        contentTemplate: SetSortDialogComponent,
      },
    }).afterClosed().subscribe(() => {
      this.getImgsList();
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getImgsList();
  }
}

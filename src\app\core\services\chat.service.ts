import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  addCustomerServiceReq,
  getCustomerServiceListResp,
} from '../../interface/chat.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ChatService {
  constructor(private http: HttpClient) {}

  getCustomerServiceList(): Observable<getCustomerServiceListResp> {
    return this.http.get<getCustomerServiceListResp>(
      'api/Manage/Faq/GetAiQuestionList'
    );
  }

  addCustomerService(req: addCustomerServiceReq): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      'api/Manage/Faq/AddOrUpdateAIQuestion',
      req
    );
  }

  deleteCustomerService(id: string): Observable<defaultItem> {
    return this.http.delete<defaultItem>('api/Manage/Faq/DeleteAIQuestion', {
      params: {
        aiQuestionId: id,
      },
    });
  }
}

import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { forkJoin, Subscription } from 'rxjs';
import { ManageService } from '../../../core/services/manage.service';
import { MenuItemService } from '../../../core/services/menuItem.service';
import { WebSiteService } from '../../../core/services/website.service';
import { UserDataService } from '../../../core/services/userData.service';
import { Router } from '@angular/router';
import { UserGroupService } from '../../../core/services/userGroup.service';
import { MenuItem } from '../../../shared/models/menuItem.model';
import { UserGroup } from '../../../shared/models/userGroup.model';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TypeSettingComponent } from '../type-setting/type-setting.component';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { CreateMenuNameComponent } from '../create-menu-name/create-menu-name.component';
import Swal from 'sweetalert2';
import { HyperLinkEditorComponent } from './hyper-link-editor/hyper-link-editor.component';
import { ShareService } from '../../../core/services/share.service';
import { changeSortReq, defaultItem } from '../../../interface/share.interface';
import { TypeList } from '../../../enum/share.enum';
import { HtmlZipEditorComponent } from './html-zip-editor/html-zip-editor.component';
import { MatSelectChange } from '@angular/material/select';
import { HttpErrorResponse } from '@angular/common/http';

export interface treeNode {
  id: string;
  name: string;
  englishName: string;
  parentId: string;
  children?: treeNode[];
}

@Component({
  selector: 'app-menu-setting',
  standalone: false,

  templateUrl: './menu-setting.component.html',
  styleUrl: './menu-setting.component.scss',
})
export class MenuSettingComponent {
  // treeNode: INodeModel = {};
  content: MenuItem | null = null;
  hasHomePage: boolean = false;
  hasNavigation: boolean = false;
  treeLoading: boolean = false;
  contentLoading: boolean = true;
  onHomepageable: boolean = true;
  onNavigationable: boolean = true;

  $sub!: Subscription;
  // $treeSub!: Subscription;

  menuItemVisibilityTypes = [
    {
      name: '開放',
      type: 'Visable',
    },
    {
      name: '隱藏',
      type: 'Hidden',
    },
  ];
  count: number = 0;
  postform: any;
  selectedType?: string = 'Visable';
  userGroups = new FormControl();
  userGroupList: UserGroup[] = [];
  dataSource: treeNode[] = [];
  isAdmin: boolean = false;
  parentMenuList: MenuItem[] = [];
  currentParentItem: MenuItem = {} as MenuItem;
  nestedParentItem: MenuItem = {} as MenuItem;

  selectedParentId: string | null = null;

  childrenAccessor = (node: treeNode) => node.children ?? [];
  hasChild = (_: number, node: treeNode) =>
    !!node.children && node.children.length > 0;

  constructor(
    private _manageService: ManageService,
    private _menuItemService: MenuItemService,
    private _webSiteService: WebSiteService,
    private _userDataService: UserDataService,
    private _router: Router,
    private _userGroupService: UserGroupService,
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<DialogComponent>,
    private shareService: ShareService
  ) { }

  ngOnInit() {
    this.getTree();
    this.isAdmin = sessionStorage.getItem('isAdmin') === 'true';
    this.$sub = this._manageService.menuItemChange.subscribe((res) => {
      this.treeLoading = true;
      this.contentLoading = true;
      this.getTree();
      this.updateFunctionPolicy();
      this.updateMenuPolicy();
    });
  }


  getMenuTypeName(typeValue: string): string {
    const item = TypeList.find(item => item.type === typeValue);
    return item ? item.name : typeValue === 'Donate' || typeValue === 'DonateList' ? '捐款系列' : typeValue;
  }

  /** 取得目前的角色功能權限列表 */
  updateFunctionPolicy() {
    this._userDataService.getUserFunctionPolicy().subscribe((res: any) => {
      sessionStorage.setItem('functionPolicy', res);
    });
  }

  /** 取得目前的角色選單權限列表，如為null則表示為系統管理員 */
  updateMenuPolicy() {
    this._userDataService.getUserMenuPolicy().subscribe((res: any) => {
      sessionStorage.setItem('menuPolicy', res);
    });
  }

  /** 取得分類tree */
  getTree() {
    this.treeLoading = true;
    this.contentLoading = true;
    this._menuItemService
      .getMenu2(sessionStorage.getItem('webSiteId')!)
      .subscribe((tree: MenuItem[]) => {
        this.dataSource = this.transformToTreeData(tree);
        this.treeLoading = false;
        if (tree.length) {
          this.getContent(tree[0].id);
        } else {
          this.content = null;
        }
      });
  }

  transformToTreeData(menuData: any) {
    return menuData.map((menuItem: MenuItem) => {
      return {
        id: menuItem.id,
        name: menuItem.name,
        englishName: menuItem.englishName,
        parentId: menuItem.parentId,
        children: menuItem.inverseParent?.length
          ? this.transformToTreeData(menuItem.inverseParent)
          : [],
      };
    });
  }

  nodeOnClick(node: treeNode) {
    this.contentLoading = true;
    this.getContent(node.id);
  }

  /**
   * 取得可以變更父層選單的選單列表
   * @param type
   */
  getParentMenu(type: string) {
    this._menuItemService
      .getMenuMoveable(sessionStorage.getItem('webSiteId')!, this.content!.type)
      .subscribe((resp: MenuItem[]) => {
        this.parentMenuList = resp;
        this.parentMenuList.unshift({
          id: 'null',
          name: '無父層選單',
          englishName: 'No parent menu',
          type: 'Folder',
          inverseParent: [],
          webSiteId: sessionStorage.getItem('webSiteId')!,
          menuItem_R_Type: [],
          sort: 0,
          visibility: 'Visable',
          onSiteMap: false,
          amisName: '',
          atayaiName: '',
          paiwanName: '',
          bununName: '',
          puyumaName: '',
          rukaiName: '',
          tsouName: '',
          saisiyatName: '',
          taoName: '',
          thaoName: '',
          kavalanName: '',
          trukuName: '',
          sakizayaName: '',
          seediqName: '',
          saaroaName: '',
          kanakanavuName: '',
        });
      });
  }

  setCurrentParentItem(item: MenuItem): void {
    this.currentParentItem = item;
  }
  setNestedParentItem(item: MenuItem): void {
    this.nestedParentItem = item;
  }

  // 去重邏輯
  getUniqueChildren(children: MenuItem[]): MenuItem[] {
    const seenIds = new Set<string>();
    return children.filter((child) => {
      if (child.id && !seenIds.has(child.id)) {
        seenIds.add(child.id);
        return true;
      }
      return false;
    });
  }

  getChildrenLang(children: MenuItem[]) {
    const tabCount = this.getUniqueChildren(children).filter(
      (child) => child.type === 'Tab'
    ).length;
    return tabCount;
  }

  change(item: MenuItem) {
    let currentMenuitemId: string = this.content!.id;
    let parentMenuitemId: string | null = item.id === 'null' ? null : item.id;
    this._menuItemService
      .changeParentMenu(currentMenuitemId, parentMenuitemId)
      .subscribe({
        next: (resp: defaultItem) => {
          this.contentLoading = false;
          if (resp.code === 200) {
            this.getTree();
            this.selectedParentId = null;
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.contentLoading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }

  /** 取得右邊內容 */
  getContent(id: string) {
    this.contentLoading = true;
    this._menuItemService.get2(id).subscribe((res: MenuItem) => {
      this.content = res;
      this.getParentMenu(res.type as string);
      this.selectedType = res.visibility;
      if (this.selectedType === 'OnlyMember') {
        this.getGroup();
      }
      /** 檢驗menuItem是否可設定顯示首頁狀態(Tab內的menuItem不行) */
      forkJoin(
        this._menuItemService.onHomepageable(this.content.id),
        this._menuItemService.onNavigationable(this.content.id),
        this._webSiteService.hasNavigation(
          sessionStorage['webSiteId'],
          this.content.id
        )
      ).subscribe((data: any) => {
        this.onHomepageable = data[0];
        this.onNavigationable = data[1];
        this.hasNavigation = data[2];
        if (this.onHomepageable) {
          /** 取得是否首頁項目狀態 */
          this._webSiteService
            .hasHomePageContent(sessionStorage['webSiteId'], this.content!.id)
            .subscribe((x) => {
              this.hasHomePage = x;
              this.contentLoading = false;
            });
        } else {
          this.contentLoading = false;
        }
      });
    });
  }

  /** 取得選單群組 */
  getGroup() {
    this._userGroupService
      .list(sessionStorage.getItem('webSiteId')!)
      .subscribe((x: UserGroup[]) => {
        this.userGroupList = x;
        this._menuItemService
          .listGroups(this.content!.id)
          .subscribe((groups: UserGroup[]) => {
            this.userGroups.setValue(groups.map((y) => y.id));
            this.contentLoading = false;
          });
      });
  }

  hasFunctionPolicy(policy: string) {
    if (!policy) {
      return true;
    }
    const functionPolicies = sessionStorage.getItem('functionPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(policy) > -1) {
      return true;
    }
    return false;
  }

  /** 新增樣式 選擇樣式 */
  openTypePopup() {
    this.dialog.open(DialogComponent, {
      data: {
        showHeader: true,
        title: '選擇樣式',
        width: '60%',
        height: '60%',
        contentTemplate: TypeSettingComponent,
      },
    });
  }

  close(isHome: boolean) {
    this.$sub.unsubscribe();
    this.dialogRef.close(isHome);
  }

  selectionChange() {
    this.contentLoading = true;
    this._menuItemService.update(this.content!).subscribe(() => {
      this.selectedType = this.content!.visibility;
      if (this.selectedType === 'OnlyMember') {
        this.getGroup();
      } else {
        this.contentLoading = false;
      }
    });
  }

  editContent() {
    const typeList = TypeList;
    if (this.content!.type === 'HyperLink') {
      const hyperlinkDialog = this.dialog.open(DialogComponent, {
        data: {
          showHeader: true,
          width: '30%',
          title: '內容管理',
          contentTemplate: HyperLinkEditorComponent,
          dataContent: this.content,
        },
      });

      hyperlinkDialog.afterClosed().subscribe((res) => {
        if (res) {
          this.contentLoading = true;
          this._menuItemService.update(res).subscribe((x) => {
            this.contentLoading = false;
          });
        }
      });
    } else if (this.content!.type === 'HtmlZip') {
      this.dialog.open(DialogComponent, {
        data: {
          showHeader: true,
          width: '30%',
          title: '新增',
          contentTemplate: HtmlZipEditorComponent,
          dataContent: this.content,
        },
      });
    } else if (this.content!.type === 'ENewsletter') {
      this._router
        .navigate(['/manage', this.content!.id, 'eNewsletter'])
        .then(() => {
          this.close(false);
        });
    } else {
      this._router
        .navigate([
          '/manage',
          this.content!.id,
          typeList.find((x) => x.type === this.content!.type)!.linkUrl,
        ])
        .then(() => {
          this.close(false);
        });
      // window.scrollTo(0, 0);
    }
  }

  hasMenuPolicy(menuId: string) {
    if (!menuId) {
      return true;
    }
    const functionPolicies = sessionStorage.getItem('menuPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(menuId) > -1) {
      return true;
    }
    return false;
  }

  /** 新增子選單 */
  createChild() {
    this.dialog.open(DialogComponent, {
      data: {
        title: '選擇樣式',
        width: '60%',
        height: '60%',
        showHeader: true,
        contentTemplate: TypeSettingComponent,
        dataGroup: {
          type: this.content!.type,
          id: this.content!.id,
        },
      },
    });
  }

  /** 編輯選單 */
  editMenuItem() {
    this.dialog.open(DialogComponent, {
      data: {
        title: '編輯選單',
        width: '30%',
        showHeader: true,
        contentTemplate: CreateMenuNameComponent,
        dataGroup: {
          selectedData: this.content,
          method: 'edit',
        },
      },
    });
  }

  /** 刪除選單 */
  deleteMenuItem() {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.treeLoading = true;
        this.contentLoading = true;
        this._menuItemService
          .delete(this.content!.id)
          .subscribe((res) => {
            this.getTree();
          })
          .add(() => {
            this.treeLoading = false;
            this.contentLoading = false;
          });
        this.updateFunctionPolicy();
        this.updateMenuPolicy();
      }
    });
  }

  changeSort(data: any, down: boolean) {
    let req: changeSortReq = {
      id: data.id,
      down: down,
      type: 'menu',
      hasParent: data.parentId ? true : false,
    };
    this.shareService.changeSort(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code === 0) {
          this.getTree();
        } else {
          Swal.fire('', resp.message, 'error');
        }
      },
      error: () => { },
    });
  }
}

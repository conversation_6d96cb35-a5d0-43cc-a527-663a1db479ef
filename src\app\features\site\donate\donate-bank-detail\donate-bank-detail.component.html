<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>銀行明細</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>時間區間 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startDate" (dateChange)="endDate = ''" />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endDate" [min]="startDate" />
                    <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                    <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
                <span>捐款項目 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
            </div>
            <div class="button-group">
                <div>
                    <button mat-flat-button (click)="searchlist()">搜尋</button>
                    &nbsp;
                    <button mat-flat-button (click)="resetsearchlist()">清空</button>
                    &nbsp;
                </div>
                <div>
                    <button mat-flat-button (click)="export()">匯出</button>
                </div>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="25px">項次</th>
                            <th>捐款項目</th>
                            <th>姓名/單位名稱</th>
                            <th>捐款單號</th>
                            <th>捐款金額</th>
                            <th>捐款方式</th>
                            <th>捐款時間</th>
                            <th>身份證字號/統一編號</th>
                            <th>E-mail</th>
                            <th>地址</th>
                            <th>實收金額</th>
                    </thead>
                    <tbody>
                        @for (item of donateItemList; track item) {
                        <tr>
                            <td data-label="項次"></td>
                            <td data-label="捐款項目"></td>
                            <td data-label="姓名/單位名稱"></td>
                            <td data-label="捐款單號"></td>
                            <td data-label="捐款金額"></td>
                            <td data-label="捐款方式"></td>
                            <td data-label="捐款時間"></td>
                            <td data-label="身份證字號/統一編號"></td>
                            <td data-label="E-mail"></td>
                            <td data-label="地址"></td>
                            <td data-label="實收金額"></td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="11" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
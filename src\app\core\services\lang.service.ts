import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LangService {
  private mngLangApi: string = '/api/Manage/Lang';

  constructor(
    private http: HttpClient
  ) { }

  /**
     * 更新與建立指定資源的指定語言資訊
     *
     * @param objectId 資源唯一識別號
     * @param lang 語系
     * @param data
     */
  createOrUpdate(
    objectId: string,

    lang: string,

    data: any
  ): Observable<any> {
    return this.http.put<any>(`${this.mngLangApi}/${objectId}/${lang}`, data);
  }
}

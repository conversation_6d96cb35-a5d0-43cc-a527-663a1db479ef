// Scss Document
//首頁
.index-news-layout{
	display: flex;
	flex-direction: column;
	.index-news-cont{
		display: grid;
		grid-template-columns: repeat(3, minmax(0, 1fr));
		gap: 1.5rem;
		&-item{
			margin: 0;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			border-radius: 10px;
			background-color: #fff;
			box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1); 
			&-img{
				overflow: hidden;
				aspect-ratio: 16 / 9;
				img{
					object-fit: cover;
					width: 100%;
					aspect-ratio: 16 / 9;
				}
			}
			&-list{
				margin: 0;
				padding: 20px;
				color: #202D3F;
			}
			&-date{
				font-size: 1em;
				color: #65676B;
			}
			&-title{
				margin: 0;
				padding: 10px 0;
				font-size: 1.5em;
				overflow:hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			&-text{
				margin: 0;
				padding: 0;
				font-size: 1.125em;
				line-height: 1.6;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
				box-sizing:border-box;
			}
		}
	}
}

//內頁-圖片式列表
.news-pic-layout{
	display: flex;
	flex-direction: column;
	.news-pic-cont{
		padding: 20px 0 0;
		display: grid;
		grid-template-columns: repeat(3, minmax(0, 1fr));
		gap: 1.5rem;
		&-item{
			margin: 0;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			border-radius: 10px;
			background-color: #fff;
			&-img{
				overflow: hidden;
				aspect-ratio: 16 / 9;
				border-radius: 10px;
				img{
					object-fit: cover;
					width: 100%;
					aspect-ratio: 16 / 9;
				}
			}
			&-list{
				margin: 0;
				padding: 20px;
				color: #202D3F;
			}
			&-date{
				font-size: 1em;
				color: #65676B;
			}
			&-title{
				margin: 0;
				padding: 10px 0;
				font-size: 1.5em;
				overflow:hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			&-text{
				margin: 0;
				padding: 0;
				font-size: 1.125em;
				line-height: 1.6;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
				box-sizing:border-box;
			}
		}
	}
}


//內頁-條列片式列表
.news-list-layout{
	display: flex;
	flex-direction: column;
	.news-list-cont{
		padding: 20px 0 0;
		&-item{
			padding: 0 0 20px;
			font-size: 1.125em;
			&-list{
				padding: 15px 20px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				gap: 1rem;
				border-bottom: 1px solid #65676B;
			}
			&-title{
				margin: 0;
				padding: 0;
				display: block;
				overflow:hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				width: 100%;
				font-weight: bold;
				text-align-last: left;
			}
			&-tag{
				margin: 0;
				padding: 0;
				width: 150px;
				text-align: right;
			}
		}
		
	}
}
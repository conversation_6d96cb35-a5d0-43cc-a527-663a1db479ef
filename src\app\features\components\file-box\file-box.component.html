<div class="popup-layout">
  <div class="header">

    <div>
      <span class="title">關鍵字 :</span>&nbsp;&nbsp;
      <input type="text" [(ngModel)]="keyword">&nbsp;&nbsp;
      <button mat-flat-button color="primary" (click)="getList()">查詢 </button>
    </div>
    <button mat-flat-button (click)="openUploadFile()">
      <i class="material-icons">add</i>上傳{{
      selector === "Image" ? "圖片" : selector === 'Video' ? '影片' : '檔案'
      }}
    </button>
  </div>
  <div class="menuSet-layout">
    <span class="tree">
      <span *ngFor="let item of typeList" [ngClass]="{ selected: item.selected }" (click)="selectType(item)">
        {{item.name }}</span>
    </span>
    <span class="contents">
      <span class="space-between">
        <span #content class="content-blocks">
          <span class="tags">
            <button *ngFor="let tag of tagList" [ngClass]="{ selected: tag.selected }" (click)="selectTag(tag)">
              {{ tag.name }}
            </button>
          </span>
          <section class="block" *ngFor="let item of contentList" [ngClass]="{ selected: item.selected }"
            (click)="selectedBlock(item)" (dblclick)="dbClickBlock(item)" [matTooltip]="item.name">
            <span class="cover" [ngStyle]="{
                'background-image': 'url(' + item.previewImageUrl + ')'
              }"></span>
            <span class="name">{{ item.name }}</span>
            <div class="icon-group">
              <mat-icon (click)="editFileDescription(item.id)">edit</mat-icon>
              <mat-icon (click)="deleteFile(item.id)">delete</mat-icon>
            </div>
            <button mat-icon-button class="copy-btn" color="primary" (click)="copy($event, item)">
              <mat-icon>file_copy</mat-icon>
            </button>
          </section>
          <section #lastBlock class="block add" (click)="openUploadFile()">
            <i class="material-icons">add</i>
            <span>上傳{{ selector === "Image" ? "圖片" : selector === 'Video' ? '影片' : '檔案' }}</span>
          </section>
        </span>
        <div class="close-btn">
          <button mat-stroked-button mat-dialog-close>關閉</button>
          <button mat-flat-button color="primary" (click)="submit()">
            確定
          </button>
        </div>
      </span>
      <app-loading [loading]="loading"></app-loading>
    </span>
  </div>
</div>
<!-- <app-context-menu (closeContextMenu)="closeContextMenu($event)"></app-context-menu> -->
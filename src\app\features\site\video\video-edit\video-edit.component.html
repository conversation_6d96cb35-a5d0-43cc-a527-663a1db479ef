<div>
    <div class="news-layout">
        <span class="white">
            <h1>影片內容編輯</h1>
        </span>
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">標題</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>標題</mat-label>
                    <input matInput type="text" [(ngModel)]="title">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">Youtube影片連結</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>Youtube影片連結</mat-label>
                    <input matInput type="text" [(ngModel)]="youtubeUrl">
                </mat-form-field>
            </div>
            <div class="block" *ngIf="needIsTop">
                <div class="title-ctn">
                    <span class="title">日期</span>
                </div>
                <mat-slide-toggle class="example-margin" [(ngModel)]="isShowTime">
                    日期開關
                </mat-slide-toggle>
                <br />
                @if(isShowTime){
                <mat-form-field appearance="outline" (click)="pickerStart.open()">
                    <input matInput [matDatepicker]="pickerStart" readonly [(ngModel)]="showTime" />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker>
                </mat-form-field>&nbsp;&nbsp;&nbsp;
                }
            </div>
            <div class="block" *ngIf="needIsTop">
                <div class="title-ctn">
                    <span class="title">置頂</span>
                </div>
                <mat-slide-toggle class="example-margin" [(ngModel)]="isTop">
                    置頂
                </mat-slide-toggle>
            </div>
        </div>
    </div>
    <div class="news-layout">
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{createUser}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editUser}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="cancel()">回上一頁</button>
            <button mat-flat-button (click)="save(saveStatus.SAVE)">存檔</button>
            <button mat-flat-button (click)="save(saveStatus.SAVELEAVE)">存檔並離開</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
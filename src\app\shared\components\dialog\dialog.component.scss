.popup-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #0000004d;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;

  .popup-block {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 50px 0 #9c9c9c;
    padding: 1em;
    display: flex;
    flex-direction: column;
    max-width: 90%;
    max-height: 90%;

    .popup-close {
      font-size: 1.5em;
      font-weight: 700;
      line-height: 1.5;
      font-family: Microsoft JhengHei;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      min-height: 36px;
      .close-btn {
        // display: flex;
        // flex-direction: column;
        // width: 36px;
        // height: 36px;
        cursor: pointer;
        // opacity: 0.5;
        span {
          height: 3px;
          margin: 3px;
          .left {
            transform: rotate(45deg) translate(8px, 7.5px);
          }
        }

        span.right {
          transform: rotate(-45deg) translate(-1px, 2px);
        }
      }
    }

    .popup-content {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.popup-layout .popup-block .popup-close .close-btn span.left {
  transform: rotate(45deg) translate(8px, 7.5px);
}

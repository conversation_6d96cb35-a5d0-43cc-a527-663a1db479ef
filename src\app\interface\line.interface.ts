import { defaultItem } from './share.interface';

export interface getLineListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
}

export interface getLineListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: lineItem[];
  };
}
export interface lineItem {
  new_NewsId: string;
  typeGroupId: string;
  publishDateTime: string; //是否有發布版及當前發布版發布時間null為無
  title: string;
  startTime: string;
  newStatus: string;
  createUser: string;
  editUser: string;
  lang: string;
}

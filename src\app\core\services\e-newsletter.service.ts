import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  createUpdateENewsletterReq,
  getENewsletterForViewResp,
  getENewsLetterInfoResp,
  getENewsletterListReq,
  getENewsletterListResp,
  getENewsletterResp,
  getGroupListReq,
  getGroupListResp,
  getGroupReportListResp,
  getGroupReportNewsListReq,
  getGroupReportRecruitListReq,
  getUserListReq,
  getUserListResp,
  getViewENewsLetterColumnReq,
  getViewENewsLetterColumnResp,
  setENewsLetterInfoReq,
} from '../../interface/eNewsletter.interface';
import { createUpdateResp, defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ENewsletterService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得電子報列表
   * @param req
   * @returns
   */
  getENewsletterList(
    req: getENewsletterListReq
  ): Observable<getENewsletterListResp> {
    return this.httpClient.get<getENewsletterListResp>(
      'api/Manage/E_Newsletter/GetE_NewsletterList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   *  取得電子報內容
   * @param id
   * @returns
   */
  getENewsletter(id: string): Observable<getENewsletterResp> {
    return this.httpClient.get<getENewsletterResp>(
      'api/Manage/E_Newsletter/GetE_NewsletterInfo',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  /**
   * 取得組報最新消息列表
   * @param req
   * @returns
   */
  getGroupReportNewsList(
    req: getGroupReportNewsListReq
  ): Observable<getGroupReportListResp> {
    return this.httpClient.get<getGroupReportListResp>(
      'api/Manage/E_Newsletter/GetNew_NewsList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   *  取得組報徵才列表
   * @param req
   * @returns
   */
  getGroupReportRecruitList(
    req: getGroupReportRecruitListReq
  ): Observable<getGroupReportListResp> {
    return this.httpClient.get<getGroupReportListResp>(
      'api/Manage/E_Newsletter/GetRecruitList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   * 新增或更新電子報
   * @param req
   * @returns
   */
  createUpdateENewsletter(
    req: createUpdateENewsletterReq
  ): Observable<createUpdateResp> {
    return this.httpClient.post<createUpdateResp>(
      'api/Manage/E_Newsletter/CreateOrUpdateE_Newsletter',
      req
    );
  }

  /**
   *  取得電子報發送佇列
   * @param req
   * @returns
   */
  getViewENewsLetterColumn(
    req: getViewENewsLetterColumnReq
  ): Observable<getViewENewsLetterColumnResp> {
    return this.httpClient.get<getViewENewsLetterColumnResp>(
      'api/Manage/E_Newsletter/GetE_NewsletterSendQueue',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   *  刪除電子報
   * @param id
   * @returns
   */
  deleteENewsletter(id: string): Observable<defaultItem> {
    return this.httpClient.delete<defaultItem>(
      'api/Manage/E_Newsletter/DeleteE_Newsletter',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  /**
   *  取得電子報預覽(審核完成)
   * @param id
   * @returns
   */
  getEnewsletter(id: string): Observable<getENewsletterForViewResp> {
    return this.httpClient.get<getENewsletterForViewResp>(
      'api/E_Newsletter/PreviewFrontE_Newsletter',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  /**
   *  取得電子報預覽(審核中和未審核)
   * @param id
   * @returns
   */
  getEnewsletterForView(id: string): Observable<getENewsletterForViewResp> {
    return this.httpClient.get<getENewsletterForViewResp>(
      'api/E_Newsletter/PreviewBackE_Newsletter',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  /**
   *  取得電子報管理頁面資料
   * @param id
   * @returns
   */
  getENewsLetterInfo(id: string): Observable<getENewsLetterInfoResp> {
    return this.httpClient.get<getENewsLetterInfoResp>(
      'api/Manage/E_Newsletter/GetSendE_NewsletterInfo',
      {
        params: {
          typeGroupId: id,
        },
      }
    );
  }

  setENewsLetterInfo(req: setENewsLetterInfoReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/E_Newsletter/StartE_NewsletterSchedule',
      req
    );
  }

  /**
   *  取得個別訂閱者列表
   * @param req
   * @returns
   */
  getUserList(req: getUserListReq): Observable<getUserListResp> {
    return this.httpClient.get<getUserListResp>(
      'api/Manage/E_Newsletter/GetSubscriber',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   *  取得群組訂閱者列表
   * @param req
   * @returns
   */
  getGroupList(req: getGroupListReq): Observable<getGroupListResp> {
    return this.httpClient.get<getGroupListResp>(
      'api/Manage/E_Newsletter/GetMemberType',
      {
        params: {
          ...req,
        },
      }
    );
  }
}

<div class="news-layout">
  <span class="white">
    <h1>內容編輯</h1>
  </span>
  <div class="contents">
    <!-- <div class="block">
      <div class="title-ctn">
        <span class="title">問題分類</span>
      </div>
      <mat-form-field appearance="outline">
        <mat-select [(ngModel)]="faqType">
          @for ( item of typeList; track $index) {
          <mat-option [value]="item.typeValue">{{
            item.typeName
            }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div> -->
    <div class="block">
      <div class="title-ctn">
        <span class="title">問題</span>
      </div>
      <mat-form-field class="example-form-field" appearance="outline">
        <mat-label>問題</mat-label>
        <input matInput type="text" [(ngModel)]="question" />
      </mat-form-field>
    </div>
    <div class="block">
      <div class="title-ctn">
        <span class="title">回答</span>
      </div>
      <div id="mytextarea" [froalaEditor]="froalaOptions" placeholder="請輸入內容..." [(ngModel)]="answer"></div>
    </div>
  </div>
</div>
<div class="news-layout">
  <div class="contents">
    <div class="block">
      <div class="title-ctn">
        <span class="title">審核層級</span>
        <mat-radio-group [(ngModel)]="levelDecision">
          <mat-radio-button [value]="1">一層決</mat-radio-button>
          <mat-radio-button [value]="2">二層決</mat-radio-button>
        </mat-radio-group>
      </div>
    </div>
    <div class="block">
      <div class="title-ctn">
        <span class="title">消息建立者 {{creater}}</span>
      </div>
    </div>
    <div class="block">
      <div class="title-ctn">
        <span class="title">最後修改者 {{editor}}</span>
      </div>
    </div>
    @if(reason){
    <div class="block">
      <div class="title-ctn">
        <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
      </div>
    </div>
    }
    @if(reviewFileUrl){
    <div class="block">
      <div class="title-ctn">
        <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
      </div>
    </div>
    }

    <div class="btn-group">
      <button mat-flat-button (click)="cancel()">回上一頁</button>
      <button mat-flat-button (click)="save(saveStatus.SAVE)">存檔</button>
      <button mat-flat-button (click)="save(saveStatus.SAVELEAVE)">存檔並離開</button>
      <button mat-flat-button (click)="view()">預覽</button>
    </div>
  </div>
</div>
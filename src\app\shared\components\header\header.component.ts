import { Component, Input } from '@angular/core';
import { Navigation } from '../../models/navigation.model';
import { WebSiteService } from '../../../core/services/website.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  standalone: false,

  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  @Input() navigation: Navigation[] = [];
  @Input() style: any;
  loading = true;
  logo: string = '';

  constructor(
    private _router: Router,
    private _webSiteService: WebSiteService,
  ) {

  }

  ngOnInit(): void {
    this.getLogo();
  }

  goHome(url: string) {
    this._router.navigate([url]);
    window.scrollTo(0, 0);
  }

  getLogo() {
    this._webSiteService.getLogoUrl(sessionStorage.getItem('webSiteId')!).subscribe((x: string) => {
      this.logo = x || 'assets/images/magic_modules_logo.png';
      this.loading = false;
    });
  }

  setting() {
    // this._pop.open(FileBoxComponent, {
    //   resolver: this._resolve,
    //   injector: this._injector
    // }, {
    //   width: '1000px',
    //   height: '500px',
    //   data: {
    //     type: 'Image'
    //   }
    // }).subscribe(res => {
    //   if (res) {
    //     this.loading = true;
    //     this._webSiteService.updateLogo(sessionStorage.getItem('webSiteId')!, res.data.dataId).subscribe(x => {
    //       this.logo = x;
    //       this.loading = false;
    //     });
    //   }
    // });
  }
}

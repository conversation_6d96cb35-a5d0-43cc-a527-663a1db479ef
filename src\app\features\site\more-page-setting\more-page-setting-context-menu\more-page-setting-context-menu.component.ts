import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
@Component({
  selector: 'app-more-page-setting-context-menu',
  standalone: false,

  templateUrl: './more-page-setting-context-menu.component.html',
  styleUrl: './more-page-setting-context-menu.component.scss',
})
export class MorePageSettingContextMenuComponent {
  contextMenuPosition = { x: '0px', y: '0px' };
  // @Input() positionData: MouseEvent | null = null;
  @ViewChild(MatMenuTrigger) contextMenu!: MatMenuTrigger;
  selectedItemData: any;
  @Output() closeContextMenu = new EventEmitter();

  ngOnInit(): void {}

  open() {
    this.contextMenu.openMenu();
  }

  edit() {
    this.closeContextMenu.emit({
      type: 'edit',
      data: this.selectedItemData,
    });
  }

  menuRole() {
    this.closeContextMenu.emit({
      type: 'menu',
      data: this.selectedItemData,
    });
  }

  functionRole() {
    this.closeContextMenu.emit({
      type: 'function',
      data: this.selectedItemData,
    });
  }

  delete() {
    this.closeContextMenu.emit({
      type: 'delete',
      data: this.selectedItemData,
    });
  }
}

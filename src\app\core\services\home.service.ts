import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';
import { getNewsListWithHomeResp } from '../../interface/home.interface';

export interface getHomeSortResp extends defaultItem {
  data: string[];
}

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得首頁排序
   * @returns
   */
  getHomeSort(): Observable<getHomeSortResp> {
    return this.httpClient.get<getHomeSortResp>('api/MenuItem/HompageSort');
  }
  getManageHomeSort(): Observable<getHomeSortResp> {
    return this.httpClient.get<getHomeSortResp>('api/Manage/MenuItem/HompageSort');
  }

  /**
   * 更新首頁排序
   * @param data
   * @returns
   */
  updateHomeSort(data: string[]) {
    return this.httpClient.post<defaultItem>(
      'api/Manage/MenuItem/UpdateHomeItemSort',
      {
        item: data,
      }
    );
  }

  getNewsListWithHome(type: string): Observable<getNewsListWithHomeResp> {
    return this.httpClient.get<getNewsListWithHomeResp>(
      'api/New_News/ListNewsResponseHomepage',
      {
        params: {
          websiteId: sessionStorage.getItem('webSiteId') as string,
          type: type,
          lang: localStorage.getItem('lang') || 'zh',
        },
      }
    );
  }
}

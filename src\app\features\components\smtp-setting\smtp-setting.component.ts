import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { SmtpSettingService } from '../../../core/services/smtp-setting.service';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import {
  getSmtpSettingResp,
  updateSmtpSettingReq,
} from '../../../interface/smtpSetting.interface';
import { defaultItem } from '../../../interface/share.interface';

@Component({
  selector: 'app-smtp-setting',
  standalone: false,

  templateUrl: './smtp-setting.component.html',
  styleUrl: './smtp-setting.component.scss',
})
export class SmtpSettingComponent {
  form!: FormGroup;
  loading: boolean = false;
  constructor(
    private _fb: FormBuilder,
    public dialogRef: MatDialogRef<DialogComponent>,
    private smtpSettingService: SmtpSettingService
  ) {
    this.form = this._fb.group({
      url: ['', Validators.required],
      port: ['', Validators.required],
      isSSL: [],
      account: ['', Validators.required],
      password: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.getSmtpSetting();
  }

  getSmtpSetting() {
    this.loading = true;
    this.smtpSettingService.getSmtpSetting().subscribe({
      next: (resp: getSmtpSettingResp) => {
        this.loading = false;
        resp.code === 200
          ? this.form.patchValue({
              url: resp.data.host,
              port: resp.data.port,
              isSSL: resp.data.enableSsl,
              account: resp.data.account,
              password: resp.data.password,
            })
          : Swal.fire('錯誤', resp.message, 'error');
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', error.error.message, 'error');
      },
    });
  }

  submit() {
    let req: updateSmtpSettingReq = {
      webSiteId: sessionStorage.getItem('webSiteId') as string,
      host: this.form.value.url,
      port: this.form.value.port,
      enableSsl: this.form.value.isSSL,
      account: this.form.value.account,
      password: this.form.value.password,
    };
    this.loading = true;
    this.smtpSettingService.updateSmtpSetting(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        resp.code === 200
          ? Swal.fire('成功', `儲存成功`, 'success').then(() => {
              this.dialogRef.close();
            })
          : Swal.fire('錯誤', resp.message, 'error');
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', error.error.message, 'error');
      },
    });
  }
}

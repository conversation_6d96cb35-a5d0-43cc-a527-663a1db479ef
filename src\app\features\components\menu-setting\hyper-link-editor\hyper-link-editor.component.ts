import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { linkEditorDialog } from '../../../../shared/models/dialog.model';

@Component({
  selector: 'app-hyper-link-editor',
  standalone: false,

  templateUrl: './hyper-link-editor.component.html',
  styleUrl: './hyper-link-editor.component.scss'
})
export class HyperLinkEditorComponent {
  url: string = '';

  constructor(
    public dialogRef: MatDialogRef<HyperLinkEditorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: linkEditorDialog,
  ) {

  }

  ngOnInit(): void {
    this.url = this.data.dataContent!.meta!;
  }

  submit() {
    this.data.dataContent.meta = this.url;
    this.dialogRef.close(this.data.dataContent);
  }
}

import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  getSurveyResp,
  saveSurveyReq,
  saveSurveyResp,
} from '../../interface/survey.interface';
import { Observable } from 'rxjs';
import { ShareService } from './share.service';

@Injectable({
  providedIn: 'root',
})
export class SurveyService {
  constructor(
    private httpClient: HttpClient,
    private shareService: ShareService
  ) {}

  getSurvey(req: {
    menuitemId: string;
    lang: string;
  }): Observable<getSurveyResp> {
    return this.httpClient.get<getSurveyResp>('api/Manage/Survey/GetSurvey', {
      params: {
        ...req,
      },
    });
  }

  saveSurvey(req: saveSurveyReq): Observable<saveSurveyResp> {
    return this.httpClient.post<saveSurveyResp>(
      'api/Manage/Survey/UpdateOrInsertSurvey',
      req
    );
  }

  exportSurvey(menuitemId: string): Observable<HttpResponse<Blob>> {
    return this.httpClient.get('api/Manage/Survey/result/export', {
      params: {
        menuitemId: menuitemId,
        lang: this.shareService.getLang(),
      },
      responseType: 'blob', // 告訴 HttpClient 要 blob
      observe: 'response', // 取得完整 HttpResponse 以抓 headers
    });
  }
}

<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>檔案集管理</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
            <div>
                <button mat-flat-button (click)="donloadTemplate()">範例下載</button>&nbsp;
                <button mat-flat-button (click)="fileInput.click()">新增</button>&nbsp;
                <input hidden type="file" accept=".xlsx,.csv" (change)="uploadFile($event)" #fileInput />
                <button mat-flat-button (click)="back()">回上一頁</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="40px">項次 </th>
                            <th>名稱 </th>
                            <th width="90px">功能</th>
                    </thead>
                    <tbody>
                        @for (item of datasetList;let index=$index ;track item.datasetId) {
                        <tr>
                            <td data-label="項次">
                                {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                            </td>
                            <td data-label="名稱">
                                {{item.name}}
                            </td>
                            <td data-label="功能">
                                <button mat-flat-button class="danger" (click)="delete(item.datasetId)">刪除</button>
                            </td>
                        </tr>
                        }
                        @empty {
                        <tr>
                            <td colspan="2" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize"
                    [hidePageSize]="true" (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getSmtpSettingResp, updateSmtpSettingReq } from '../../interface/smtpSetting.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class SmtpSettingService {
  constructor(private http: HttpClient) {}

  getSmtpSetting(): Observable<getSmtpSettingResp> {
    return this.http.get<getSmtpSettingResp>('api/Manage/WebSite/GetSMTP', {
      params: {
        webSiteId: sessionStorage.getItem('webSiteId') as string,
      },
    });
  }
  updateSmtpSetting(req: updateSmtpSettingReq): Observable<defaultItem> {
    return this.http.post<defaultItem>('api/Manage/WebSite/CreateOrUpdateSMTP', req);
  }
}

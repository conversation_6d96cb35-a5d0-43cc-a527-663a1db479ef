import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { fileBoxDigData } from '../../../../shared/models/dialog.model';
export enum UploadStatus {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
}

@Component({
  selector: 'app-el-upload-box',
  standalone: false,
  templateUrl: './el-upload-box.component.html',
  styleUrl: './el-upload-box.component.scss',
})
export class ElUploadBoxComponent implements OnInit {
  status: UploadStatus = UploadStatus.SINGLE;
  fileTypeList: string[] = [];
  fileData = new EventEmitter<{
    fileList: File[];
    fileNameList: string[];
  }>();

  fileAcceptList: string[] = [];

  uploadStatus = UploadStatus;
  fileList: File[] = [];
  fileNameList: string[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ElUploadBoxComponent>
  ) {
    this.status = this.data.data.status;
    this.fileTypeList = this.data.data.fileTypeList;
    console.log(this.data);
  }

  ngOnInit() {
    this.getfileAcceptList();
  }

  getfileAcceptList() {
    this.fileAcceptList = this.fileTypeList.map((ext) => '.' + ext);
  }

  onFileSelected(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.files && inputElement.files.length > 0) {
      this.fileList = [];
      this.fileNameList = [];
      const fileExtension = inputElement.files[0].name
        .split('.')
        .pop()
        ?.toLowerCase();
      const hasFileTypeList =
        Array.isArray(this.fileTypeList) && this.fileTypeList.length > 0;
      if (
        !hasFileTypeList ||
        this.fileTypeList.includes(fileExtension as string)
      ) {
        this.fileList.push(inputElement.files[0]);
        this.fileNameList.push(inputElement.files[0].name);
      } else {
        Swal.fire('檔案類型錯誤', '', 'warning');
      }
    }
  }

  onFileMultipleSelected(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.files && inputElement.files.length > 0) {
      const hasFileTypeList =
        Array.isArray(this.fileTypeList) && this.fileTypeList.length > 0;
      for (let i = 0; i < inputElement.files.length; i++) {
        const file = inputElement.files[i];
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const isValidExtension =
          !hasFileTypeList || this.fileTypeList.includes(fileExtension || '');
        if (!isValidExtension) {
          Swal.fire('錯誤', `檔案類型錯誤：${file.name}`, 'warning');
          continue;
        }
        const isDuplicate = this.fileList.some(
          (f) => f.name === file.name && f.size === file.size
        );
        if (isDuplicate) {
          console.warn(`檔案重複已略過：${file.name}`);
          continue;
        }

        this.fileList.push(file);
        this.fileNameList.push(file.name);
      }
    }
  }

  onFileDropped(event: DragEvent) {
    // this.confirmService.showWARN('檔案類型錯誤', '警告');//
    event.preventDefault();
    if (event.dataTransfer?.files.length) {
      this.fileList = [];
      this.fileNameList = [];
      const fileExtension = event.dataTransfer.files[0].name
        .split('.')
        .pop()
        ?.toLowerCase();
      const hasFileTypeList =
        Array.isArray(this.fileTypeList) && this.fileTypeList.length > 0;

      if (
        !hasFileTypeList ||
        this.fileTypeList.includes(fileExtension as string)
      ) {
        this.fileList.push(event.dataTransfer.files[0]);
        this.fileNameList.push(event.dataTransfer.files[0].name);
      } else {
        Swal.fire('檔案類型錯誤', '', 'warning');
      }
    }
  }
  onFileMultipleDropped(event: DragEvent) {
    event.preventDefault();
    if (event.dataTransfer?.files.length) {
      for (let i = 0; i < event.dataTransfer.files.length; i++) {
        const file = event.dataTransfer.files[i];
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const hasFileTypeList =
          Array.isArray(this.fileTypeList) && this.fileTypeList.length > 0;
        const isValidExtension =
          !hasFileTypeList || this.fileTypeList.includes(fileExtension || '');
        if (!isValidExtension) {
          Swal.fire('錯誤', `檔案類型錯誤：${file.name}`, 'warning');
          continue;
        }
        const isDuplicate = this.fileList.some(
          (f) => f.name === file.name && f.size === file.size
        );
        if (isDuplicate) {
          console.warn(`檔案重複已略過：${file.name}`);
          continue;
        }
        this.fileList.push(file);
        this.fileNameList.push(file.name);
      }
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
  }

  remove(index: number) {
    this.fileList.splice(index, 1);
    this.fileNameList.splice(index, 1);
  }

  close() {
    this.dialogRef.close();
  }

  submit() {
    this.dialogRef.close({
      fileList: this.fileList,
      fileNameList: this.fileNameList,
    });
  }
}

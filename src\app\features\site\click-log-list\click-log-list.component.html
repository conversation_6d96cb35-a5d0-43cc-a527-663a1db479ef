<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>頁面點擊統計</h1>
    </div>
    <div class="list-container">
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="40px">項次</th>
                            <th>連結</th>
                            <th width="70px">點擊數</th>
                    </thead>
                    <tbody>
                        @for (item of clickLogList;let index=$index ;track item) {
                        <tr>
                            <td data-label="項次">{{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}</td>
                            <td data-label="連結">{{item.url}}</td>
                            <td data-label="點擊數">{{item.count}}</td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="3" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
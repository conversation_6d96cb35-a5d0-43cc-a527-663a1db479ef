.block {
    margin: 5px 0;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #cccccc33;
    width: 95%;

    .tag-layout {
        display: flex;
        flex-wrap: wrap;

        .tag-title {
            width: 150px;
            padding: 0 5px;
            margin: 10px 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 0 3px #ccc;
            background-color: #ffffff;
            border: 3px solid #ffffff;
            cursor: pointer;
            transition: 0.3s ease-in-out;

            &.add {
                width: auto;
                background-color: #b8d3f3;
                border: 3px solid #b8d3f3;
            }

            &.selected {
                border: 3px solid #3f51b5;
            }

            &:hover {
                box-shadow: 0px 0px 9px 0px #ccc;
            }

            input {
                width: 100%;
                font-size: 25px;
                outline: 0;
                background-color: #ffffff;
                border: 1px solid #868585;
            }
        }
    }

    &.row {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .column {
        display: flex;
        flex-direction: column;
    }

    .title {
        line-height: 2;
    }

    .input {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        padding: 0 10px;

        &:focus {
            border: 1px solid #3f51b5;
        }
    }

    textarea {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        resize: vertical;
        min-height: 80px;
        padding: 0 10px;

        &:focus {
            border: 1px solid #3f51b5;
        }
    }

    .mat-slide-toggle {
        margin: 0 1em;
    }

    .select-image {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .preview {
            width: 200px;
            height: 100px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin: 10px 0;
            border: 4px solid #ccc;
        }

        .files-layout {
            display: flex;
            align-items: center;

            .file-block {
                border: 1px solid #ccc;
                background-color: #ffffff;
                margin: 5px;
                padding: 5px 1em;
                cursor: pointer;
                transition: 0.3s ease-in-out;

                &:hover {
                    border: 1px solid #3f51b5;
                }
            }
        }
    }
    .combine {
        padding: 0;
        .combine-block {
            display: flex;
            align-items: center;
            .website {
                font-weight: bold;
            }
        }
    }
}

.btn-group {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.file {
    border: solid #b8d3f3;
    border-radius: 10px;
    padding: 5px;
}

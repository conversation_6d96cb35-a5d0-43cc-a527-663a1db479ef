import { Component } from '@angular/core';
import { ShareService } from '../../../core/services/share.service';
import { ActivatedRoute } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import {
  getSearchKeywordLogListReq,
  getSearchKeywordLogListResp,
  searchKeywordLogItem,
} from '../../../interface/share.interface';
export enum ColumType {
  userCount = 'userCount',
  usageCount = 'usageCount',
}

@Component({
  selector: 'app-search-keyword-log',
  standalone: false,

  templateUrl: './search-keyword-log.component.html',
  styleUrl: './search-keyword-log.component.scss',
})
export class SearchKeywordLogComponent {
  lang: string = '中文';
  loading: boolean = false;
  menuItemId: string = '';
  column: string = ColumType.userCount;
  isAsc: boolean = true;
  ColumType = ColumType;

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  searchKeywordLogList: searchKeywordLogItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.getSearchKeywordLogList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getSearchKeywordLogList();
  }

  getSearchKeywordLogList() {
    let req: getSearchKeywordLogListReq = {
      webSiteId: sessionStorage.getItem('webSiteId')!,
      column: this.column,
      isAsc: this.isAsc,
      lang: this.shareService.getLang(),
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.shareService.getSearchKeywordLogList(req).subscribe({
      next: (resp: getSearchKeywordLogListResp) => {
        this.loading = false;
        this.searchKeywordLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  changeSort(column: string) {
    this.nowPage = 1;
    this.column = column;
    this.isAsc = !this.isAsc;
    this.getSearchKeywordLogList();
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getSearchKeywordLogList();
  }
  export() {
    this.shareService.exportSearchKeywordLog().subscribe({
      next: (resp: any) => {
        const contentDisposition = resp.headers.get('Content-Disposition');
        let filename = '預設檔名.xlsx'; // 提供一個預設檔名，以防萬一

        if (contentDisposition) {
          // 1. 嘗試解析 RFC 5987 標準的 filename* 屬性（推薦）
          const filenameStarMatch = contentDisposition.match(/filename\*=(.+)/);
          if (filenameStarMatch && filenameStarMatch[1]) {
            try {
              // 解析 UTF-8 編碼的檔名
              // 格式通常為 filename*=UTF-8''%E6%AA%94%E5%90%8D
              const encodedFilename = filenameStarMatch[1].split("'")[2];
              filename = decodeURIComponent(encodedFilename);
            } catch (e) {
              console.error('解析 filename* 編碼失敗', e);
            }
          }
        }

        // 建立並下載檔案
        const url = window.URL.createObjectURL(resp.body);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a); // 為了在 Firefox 上兼容
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }
}

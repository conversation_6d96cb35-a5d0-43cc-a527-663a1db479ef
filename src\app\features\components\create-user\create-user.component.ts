import { Component, contentChild, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { UserDataService } from '../../../core/services/userData.service';
import { RoleService } from '../../../core/services/role.service';
import { Role } from '../../../shared/models/role.model';
import Swal from 'sweetalert2';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { AuthService } from '../../../core/services/auth.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  bindingGoogleResp,
  unBindingGoogleResp,
} from '../../../shared/models/loginInfo.model';

const regExp =
  /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;

@Component({
  selector: 'app-create-user',
  standalone: false,

  templateUrl: './create-user.component.html',
  styleUrl: './create-user.component.scss',
})
export class CreateUserComponent {
  form!: FormGroup;
  isExists: boolean = false;
  needSetting: boolean = false;
  roleList: Role[] = [];
  loading: boolean = false;
  popupData: any;
  personalEditor: boolean = false;
  buildingGoogleStatus: boolean = false;
  constructor(
    private _userDataService: UserDataService,
    private _roleService: RoleService,
    private _fb: FormBuilder,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<CreateUserComponent>,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    if (this.data.data && this.data.data.googleId) {
      this.buildingGoogleStatus = true;
    } else {
      this.buildingGoogleStatus = false;
    }
    this.personalEditor = this.data.personalEditor;
    this.popupData = this.data.data;
    this.initForm();
    this.getRoleList();
    // var popListLength = this._pop.popupList.length-1;

    // if(this._pop.popupList[popListLength].option.title == '編輯資料') {
    //   this.needSetting = false;
    //   //console.log(this.popupData)
    //   if (this.popupData) {
    //     Object.keys(this.popupData).map(property => {
    //       if (this.form.get(property)) {
    //         this.form.get(property)?.setValue(this.popupData[property]);
    //       }
    //     });
    //   }
    // }else {
    //   this.getRoleList();
    // }
  }

  initForm() {
    this.form = this._fb.group({
      id: null,
      name: [null, Validators.required],
      account: [null, Validators.required],
      enable: [true, Validators.required],
      gender: 'Male',
      password: [null],
      checkPass: [null],
      roleId: null,
      googleId: null,
      birthday: null,
      phone: null,
      mobile: null,
      email: [null, Validators.required],
      idNumber: null,
      address: null,
      addressZip: null,
      type: 'Default',
      webSiteId: sessionStorage.getItem('webSiteId'),
      education: null,
      ethnic: null,
      constellation: null,
      industry: null,
      jobTitle: null,
    });
  }

  getRoleList() {
    this.loading = true;
    this._roleService.getRoleList().subscribe((res: Role[]) => {
      this.roleList = res;
      if (this.popupData) {
        Object.keys(this.popupData).map((property) => {
          if (this.form.get(property)) {
            this.form.get(property)?.setValue(this.popupData[property]);
          }
        });
      }

      this.loading = false;
    });
  }

  /** 檢驗帳號是否已經存在 */
  existsAccount($event: any) {
    const val = $event.target.value;
    if (val) {
      this._userDataService
        .exists(sessionStorage.getItem('webSiteId')!, val)
        .subscribe((res) => {
          this.isExists = res;
        });
    }
  }

  /** 修改密碼 */
  changePass() {
    const changePasswordDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        showHeader: true,
        title: '修改密碼',
        contentTemplate: ChangePasswordComponent,
      },
    });

    changePasswordDialog.afterClosed().subscribe((res) => {
      if (res) {
        this.loading = true;
        this.popupData.password = res.password;
        this.popupData.birthday = Math.floor(new Date().getTime() / 1000);
        this._userDataService
          .updateUserPassword(this.popupData)
          .subscribe((x) => {
            this.form.patchValue({ password: this.popupData.password });
            if (x == true) {
              Swal.fire('', '修改成功', 'success');
            } else {
              Swal.fire('', '請填入信箱以修改密碼', 'error');
            }
            this.loading = false;
          });
      }
    });
  }

  submit(formVal: any) {
    if (!this.popupData) {
      if (!formVal.password || !formVal.checkPass) {
        Swal.fire('請確認密碼正確', '', 'warning');
        return;
      }

      if (formVal.password !== formVal.checkPass) {
        Swal.fire('請確認密碼正確', '', 'warning');
        return;
      }
    }

    formVal.birthday = Math.floor(new Date(formVal.birthday).getTime() / 1000);

    if (this.needSetting == true) {
      if (formVal.email == null) {
        Swal.fire('請輸入信箱', '', 'warning');
        return;
      }
    }

    if (formVal.roleId === 'null') {
      formVal.roleId = null;
    }
    this.dialogRef.close(formVal);
  }

  bindingWithGoogle() {
    this.authService.GooglePopup().subscribe({
      next: (res: string) => {
        this.bindingGoogle(res);
      },
      error: (error) => {
        console.error(error);
      },
    });
  }

  bindingGoogle(token: string) {
    this.loading = true;
    this._userDataService.bindingGoogle(token).subscribe({
      next: (res: bindingGoogleResp) => {
        if (res.code === 200) {
          Swal.fire('綁定成功', '', 'success');
          this.buildingGoogleStatus = true;
          this.loading = false;
        } else {
          Swal.fire('綁定失敗', `${res.message}`, 'error');
          this.buildingGoogleStatus = false;
          this.loading = false;
        }
      },
      error: (error: HttpErrorResponse) => {
        Swal.fire('綁定失敗', `${error.error.message}`, 'error');
        this.buildingGoogleStatus = false;
        this.loading = false;
      },
    });
  }

  unBindingWithGoogle() {
    Swal.fire({
      title: '請問確定要解除Google綁定?',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this._userDataService.unBindingGoogle(this.form.value.id).subscribe({
          next: (res: unBindingGoogleResp) => {
            if (res.code === 200) {
              Swal.fire('解除綁定成功', '', 'success');
              this.buildingGoogleStatus = false;
              this.loading = false;
            } else {
              Swal.fire('解除綁定失敗', `${res.message}`, 'error');
              this.loading = false;
            }
          },
          error: (error: HttpErrorResponse) => {
            Swal.fire('解除綁定失敗', `${error.error.message}`, 'error');
            this.loading = false;
          },
        });
      }
    });
  }
}

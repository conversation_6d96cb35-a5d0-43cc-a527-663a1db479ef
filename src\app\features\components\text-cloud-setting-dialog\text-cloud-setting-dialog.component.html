<div class="main">
    <div class="input">
        <mat-form-field appearance="outline">
            <mat-label>名稱</mat-label>
            <input matInput placeholder="名稱" [(ngModel)]="text">
        </mat-form-field>
        <mat-form-field appearance="outline">
            <mat-label>權重</mat-label>
            <input type="number" matInput placeholder="權重" [(ngModel)]="weight" #weightModel="ngModel" min="1" max="100"
                required>
            <mat-error *ngIf="weightModel.errors?.['min'] || weightModel.errors?.['max']">
                權重必須介於 1 到 100 之間
            </mat-error>
        </mat-form-field>
    </div>
    <div class="close-btn">
        <button mat-stroked-button (click)="close()">取消</button>
        <button mat-flat-button (click)="submit()"
            [disabled]="!text || !weight || weight < 1 || weight > 100">確定</button>
    </div>
</div>
<div class="form-layout" [formGroup]="form" method="post">
  <div class="contents">
    <span class="block row">
      <span class="column">
        <span class="title">帳號</span>
        <input class="input" type="text" (keyup)="existsAccount($event)" formControlName="account" *ngIf="!popupData" />
        <span *ngIf="form.get('account')?.value && isExists" class="error">已重複使用</span>
        <span class="text" *ngIf="popupData">{{
          form.get("account")?.value
          }}</span>
      </span>
      <span class="column">
        <span class="title">姓名</span>
        <input class="input" type="text" formControlName="name" />
      </span>
    </span>
    <span class="block row" *ngIf="!popupData">
      <span class="column">
        <span class="title">密碼</span>
        <input class="input" type="password" formControlName="password" autocomplete="off" />
      </span>
      <span class="column">
        <span class="title">確認密碼</span>
        <input class="input" type="password" formControlName="checkPass" autocomplete="off" />
      </span>
    </span>
    <span class="block" *ngIf="popupData">
      <span class="title">密碼&nbsp;
        ※&nbsp;密碼長度應十二個字元以上，包含英文大小寫、數字及特殊字元</span>
      <span class="btn">
        <button mat-flat-button type="button" (click)="changePass()">
          修改密碼
        </button>
      </span>
    </span>
    <span class="block">
      <span class="title">E-mail</span>
      <input class="input" type="text" formControlName="email" />
    </span>
    <span class="block row" *ngIf="needSetting">
      <span class="column" style="display: none">
        <span class="title">身分證字號</span>
        <input class="input" type="text" formControlName="idNumber" />
      </span>
      <span class="column">
        <span class="title">啟用</span>
        <mat-slide-toggle color="primary" formControlName="enable"></mat-slide-toggle>
      </span>
    </span>
    <span class="block">
      <span class="title">角色</span>
      <mat-form-field>
        <select matNativeControl formControlName="roleId">
          <option [value]="null">無</option>
          <option *ngFor="let role of roleList" [value]="role.id">
            {{ role.name }}
          </option>
        </select>
      </mat-form-field>
    </span>
    @if(this.personalEditor){
    <span class="block">
      @if (this.buildingGoogleStatus) {
      <span class="title">解除綁定google帳號</span>
      <div class="btn">
        <button mat-flat-button type="button" (click)="unBindingWithGoogle()">
          解除綁定
        </button>
      </div>

      }@else{
      <span class="title">綁定google帳號</span>
      <div class="btn">
        <img src="assets/images/googleLogin.svg" alt="" srcset="" (click)="bindingWithGoogle()">
      </div>
      }
    </span>
    }
  </div>
  <div class="btns">
    <button mat-stroked-button mat-dialog-close type="button">取消</button>
    <button mat-flat-button color="primary" type="submit" [disabled]="!form.valid || isExists"
      (click)="submit(form.value)">
      確定
    </button>
  </div>
  <app-loading [loading]="loading"></app-loading>
</div>
import { Component, Inject } from '@angular/core';
import { UserGroupService } from '../../../core/services/userGroup.service';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { UserGroup, UserList } from '../../../shared/models/userGroup.model';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { CreateGroupComponent } from './create-group/create-group.component';
import { SelectUserComponent } from './select-user/select-user.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-group-setting',
  standalone: false,
  templateUrl: './group-setting.component.html',
  styleUrl: './group-setting.component.scss',
})
export class GroupSettingComponent {
  loading = false;
  groupList: UserGroup[] = [];

  userList: UserList[] = [];
  userLoading = false;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      width: string;
      height: string;
    },
    private _userGroupService: UserGroupService,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<CreateGroupComponent>
  ) {}

  ngOnInit() {
    this.getList();
  }

  getList() {
    this.loading = true;
    this._userGroupService
      .list(sessionStorage.getItem('webSiteId') as string)
      .subscribe((resp: UserGroup[]) => {
        this.groupList = resp;
        this.loading = false;
      });
  }

  /** 建立群組 */
  createGroup() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: CreateGroupComponent,
          showHeader: true,
          title: '建立群組',
        },
      })
      .afterClosed()
      .subscribe((data: { name: string }) => {
        if (data.name) {
          this.loading = true;
          this._userGroupService.createGroup(data.name).subscribe(() => {
            this.getList();
          });
        }
      });
  }

  getMember(item: UserGroup) {
    this.groupList.map((groupItem: UserGroup) => {
      if (groupItem === item) {
        groupItem.userShow = true;
      } else {
        groupItem.userShow = false;
      }
      return groupItem;
    });

    this.userLoading = true;
    this.userList = [];
    this._userGroupService
      .getUserGroupMembers(item.id as string)
      .subscribe((resp: UserList[]) => {
        this.userList = resp;
        this.userLoading = false;
      });
  }
  selectUser(item: UserGroup) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: SelectUserComponent,
          showHeader: true,
          title: '使用者設置',
          id: item.id,
          userList: this.userList,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getMember(item);
      });
  }

  closeUser(item: UserGroup) {
    item.userShow = false;
    this.userList = [];
  }

  deleteGroup(item: UserGroup) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this._userGroupService.deleteGroup(item.id as string).subscribe({
          next: () => {
            this.loading = false;
            this.getList();
          },
          error: () => {
            this.loading = false;
            Swal.fire('錯誤', '刪除失敗', 'error');
          },
        });
      }
    });
  }

  close() {
    this.dialogRef.close();
  }
}

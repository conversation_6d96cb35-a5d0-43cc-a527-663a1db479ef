import { defaultItem } from './share.interface';

export interface getImgsListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
}

export interface getImgsListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: ImgsItem[];
  };
}
export interface ImgsItem {
  new_NewsId: string;
  typeGroupId: string;
  title: string;
  coverUrl: string;
  startTime: string;
  newStatus: string;
  createUser: string;
  editUser: string;
  isTop: boolean;
  sort: number;
}

import { new_NewsTagDataItem } from './news.interface';
import { defaultItem } from './share.interface';

export interface getFaqListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
}

export interface getFaqListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: faqItem[];
  };
}
export interface faqItem {
  faqId: string;
  typeGroupId: string;
  question: string;
}

export interface getFaqResp extends defaultItem {
  data: {
    faqId: string;
    faqType: string;
    question: string;
    createUser: string;
    editUser: string;
    typeGroupId: string;
    isPendingApproval: boolean;
    publishDateTime: string;
    levelDecision: number;
    currentLevel: number;
    answer: new_NewsTagDataItem[];
    reason: string;
    reviewFileName: string;
    reviewFileUrl: string;
  };
}

export interface createUpdateFaqReq {
  menuitemId: string;
  typeGroupId?: string;
  faqType: string;
  faqName: string;
  new_NewsTagDatas: {
    tagName: string;
    dataString: string|null;
  }[];
  levelDecision: number;
  lang: string;
}

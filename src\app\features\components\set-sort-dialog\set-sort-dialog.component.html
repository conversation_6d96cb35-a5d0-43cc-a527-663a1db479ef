<div class="main">
    <div class="tip">
        <span>排序規則：</span>
        <span>1. 設定「置頂」的項目將絕對優先，顯示在最前。</span>
        <span>2. 「排序數值」須為正整數。</span>
        <span>3. 未置頂的項目，依「排序數值」由小到大排列（數值越小越前面）。</span>
    </div>
    <div class="input">
        <mat-form-field appearance="outline">
            <mat-label>排序</mat-label>
            <input matInput type="number" placeholder="排序" [(ngModel)]="sort" autocomplete="off" />
        </mat-form-field>

    </div>
    <div class="close-btn">
        <button mat-stroked-button mat-dialog-close type="button">取消</button>
        <button mat-flat-button color="primary" type="button" (click)="submit()"
            [disabled]="sort === null || sort === undefined ||sort<0">
            確定
        </button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
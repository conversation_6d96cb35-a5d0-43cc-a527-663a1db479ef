.main {
    margin-top: 10px;
    position: relative;

    .tags {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        overflow: hidden;

        button {
            border: 1px solid #ccc;
            background-color: #ffffff;
            border-radius: 15px;
            cursor: pointer;
            outline: 0;
            padding: 5px;
            margin: 5px;
            transition: 0.3s ease-in-out;

            &.selected {
                background-color: #ccc;
            }

            &:hover {
                border-color: #989898;
            }
        }
    }

    hr {
        margin: 20px 20px;
        border: 1px dashed rgba(128, 128, 128, 0.3);
    }

    .image-cropper-group {
        padding: 15px;

        .slide-group {
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            label {
                margin-bottom: 10px;
            }
        }
    }

    .close-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px;
    }
}

::ng-deep {
    image-cropper {
        .overlay {
            width: 100% !important;
        }
    }
}

.description-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

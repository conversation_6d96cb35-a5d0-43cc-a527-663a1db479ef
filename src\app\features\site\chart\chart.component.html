<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>圖表管理</h1>
    </div>
    <div class="list-container">
        <div class="add-block-group">
            <span (click)="addKeyword()">關鍵字管理</span>
            <span (click)="datasetManage()">檔案集管理</span>
            <span (click)="addChart()">+新增儀錶板區塊</span>
        </div>
        <!-- todo 圖表顯示 -->
        <div class="center-theme">
            <mat-grid-list [cols]="getCols()" cdkDropListGroup gutterSize="20px" rowHeight="420px">
                @for ( item of dataSource ;let i=$index; track i) {
                <mat-grid-tile cdkDropList [cdkDropListData]="dataSource" (cdkDropListDropped)="drop(item.blockId)"
                    [colspan]="getColspan(item.colspan)" [rowspan]="item.rowspan">
                    <div style="width: 100%; height: 100%" (cdkDragStarted)="dropStart(item.blockId)" cdkDrag>
                        <div class="header">
                            <span class="title">{{ item.name }}</span>
                            <div style="display: flex;align-items: center; margin-left: auto;">
                                <mat-icon class="menu-icon" [matMenuTriggerFor]="contentMenu">more_horiz</mat-icon>
                                <mat-menu #contentMenu="matMenu">
                                    @if(!item.options){
                                    <button mat-menu-item (click)="removeBlock(item.blockId)">
                                        <span>刪除</span>
                                    </button>
                                    }@else{
                                    <button mat-menu-item (click)="editBlock(item)">
                                        <span>編輯</span>
                                    </button>
                                    <button mat-menu-item (click)="removeBlock(item.blockId)">
                                        <span>刪除</span>
                                    </button>
                                    }
                                </mat-menu>
                                <img src="assets/images/chart/move-icon.svg" class="icon-color"
                                    style="width: 30px; height: 30px; cursor: move;" alt="" />&nbsp;&nbsp;&nbsp;
                            </div>
                        </div>
                        @if(!item.options&& item.errorStatus!==true;){
                        <div class="spinner-wrapper-index" (mousedown)="$event.stopPropagation()">
                            <mat-spinner></mat-spinner>
                        </div>
                        }
                        @else{
                        @if(item.errorStatus===true){
                        <span class="error-msg">{{item.errorMsg}}</span>
                        }@else{
                        <!-- todo echarts -->
                        <div echarts [options]="item.dataset"></div>
                        }
                        }
                    </div>
                </mat-grid-tile>
                }
            </mat-grid-list>
        </div>

    </div>
</div>
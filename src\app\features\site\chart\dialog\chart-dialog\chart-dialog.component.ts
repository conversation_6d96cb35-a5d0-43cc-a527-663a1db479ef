import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { customerChartInterface } from './chart-column/chart-column.component';
import { ChartService } from '../../../../../core/services/chart.service';
import {
  blockListItem,
  createOrUpdateBlockReq,
  getBlockResp,
} from '../../../../../interface/chart.interface';
import { defaultItem } from '../../../../../interface/share.interface';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { ShareService } from '../../../../../core/services/share.service';
import { EChartsOption } from 'echarts';

@Component({
  selector: 'app-chart-dialog',
  standalone: false,

  templateUrl: './chart-dialog.component.html',
  styleUrl: './chart-dialog.component.scss',
})
export class ChartDialogComponent implements OnInit {
  form: FormGroup;
  menuitemId: string = '';
  customerChartStatus: boolean = false;
  loading: boolean = false;
  blockListItem: blockListItem | null = null;

  isFormValid: boolean = false;

  datasetId: string = '';
  xAxis: string = '';
  chartType: string = '';
  yAxis: string[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { data: { data: blockListItem; menuitemId: string } },
    private dialogRef: MatDialogRef<ChartDialogComponent>,
    private formBuilder: FormBuilder,
    private chartService: ChartService,
    private shareService: ShareService
  ) {
    this.menuitemId = this.data.data.menuitemId;

    this.form = this.formBuilder.group({
      title: ['', Validators.required],
      colspan: [
        '',
        [Validators.required, Validators.min(1), Validators.max(4)],
      ],
      rowspan: [
        '',
        [Validators.required, Validators.min(1), Validators.max(4)],
      ],
    });
  }

  ngOnInit() {
    if (this.data.data.data) {
      this.form.patchValue({
        title: this.data.data.data.name,
        colspan: this.data.data.data.colspan,
        rowspan: this.data.data.data.rowspan,
      });
      this.blockListItem = this.data.data.data;
    }
  }

  close() {
    this.dialogRef.close();
  }

  getFormStatus(isValid: boolean) {
    this.isFormValid = isValid;
  }

  submit() {
    let successText: string = '建立成功';
    let req: createOrUpdateBlockReq = {
      menuitemId: this.menuitemId,
      datasetId: this.datasetId,
      name: this.form.value.title,
      colspan: this.form.value.colspan,
      rowspan: this.form.value.rowspan,
      options: this.chartType,
      searchOptions: this.yAxis,
      lang: this.shareService.getLang(),
    };
    console.log(req);
    if (this.blockListItem) {
      req.blockId = this.blockListItem.blockId;
      successText = '更新成功';
    }
    this.chartService.createOrUpdateBlock(req).subscribe({
      next: (resp: defaultItem) => {
        resp.code === 200
          ? Swal.fire('成功', successText, 'success').then(() => {
              this.dialogRef.close();
            })
          : Swal.fire('錯誤', resp.message, 'error');
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('錯誤', err.error.message, 'error');
      },
    });

    this.dialogRef.close();
  }

  // #region 圖表回傳
  getCustomerChartStatus(customerChartStatus: boolean) {
    this.customerChartStatus = customerChartStatus;
  }

  getLoading(loading: boolean) {
    this.loading = loading;
  }

  /**
   * 取得CustomerChart
   * @param data
   */
  getCustomerChart(data: customerChartInterface) {
    console.log(data);
    this.datasetId = data.datasetId;
    this.xAxis = data.xAxis;
    this.yAxis = data.yAxis;
    this.chartType = data.chartType;
    // this.datasetTypeFormat = data.datasetTypeFormat;
    // this.chartConfig = { ...data.chartConfig };
    // this.searchOptions = { ...data.searchOptions };
    // this.datasetID = data.datasetID;
    // this.chartDataset = data.dataset;
  }

  getOptions(options: string): EChartsOption {
    return options ? (JSON.parse(options) as EChartsOption) : {};
  }

  // #endregion
}

<div class="registration-layout">
  <div class="registration-block">
    <div class="title">啟用狀態</div>
    <div class="cont">
      <mat-slide-toggle
        color="primary"
        [checked]="enable"
        (change)="updateEnable($event)"
      ></mat-slide-toggle>
    </div>
    <app-loading [loading]="enableLoading"></app-loading>
  </div>
  <div class="registration-block" *ngIf="enable">
    <div class="title">欄位必填狀態</div>
    <div class="cont">
      <mat-checkbox
        *ngFor="let field of fieldList"
        color="primary"
        [checked]="field.checked"
        (change)="changeField(field)"
        >{{ field.name }}</mat-checkbox
      >
    </div>
    <app-loading [loading]="requiredLoading"></app-loading>
  </div>
</div>

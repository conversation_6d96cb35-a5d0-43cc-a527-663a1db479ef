import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, ElementRef, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { surveyItem } from '../../../interface/survey.interface';
import {
  getHomeSortResp,
  HomeService,
} from '../../../core/services/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import {
  defaultItem,
  getAchievementsResp,
  getTextCloudResp,
  getTypeListResp,
} from '../../../interface/share.interface';
import { ShareService } from '../../../core/services/share.service';
import {
  getNewsListWithHomeResp,
  newsItem,
} from '../../../interface/home.interface';
import 'echarts-wordcloud';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { BannerDialogComponent } from '../dialogs/banner/banner-dialog/banner-dialog.component';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { Banner, getBannerListResp } from '../../../shared/models/banner.model';
import { BannerService } from '../../../core/services/banner.service';

export interface achievementsAnimateCount {
  currentCount: number;
  count: number;
}

export enum SuggestType {
  OnlineDictionary = '原住民族語言線上辭典',
  Klokah = '族語E樂園',
  Lokahsu = 'lokahsu原住民族語言能力認證測驗',
  TalentDatabase = '原住民族語言人才資料庫',
  Glossary = '原住民族語學習詞表系統',
  LearningCenter = '原住民族語言學習中心課程平台',
  PromotionOrganization = '原住民族語言推動組織',
}

@Component({
  selector: 'app-home',
  standalone: false,

  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
})
export class HomeComponent implements OnInit {
  loginStatus: boolean = sessionStorage.getItem('token') ? true : false;
  // 取得輪播中所有影片元素的引用
  @ViewChildren('carouselVideo') carouselVideos!: QueryList<ElementRef<HTMLVideoElement>>;
  // 儲存視窗大小變化監聽器的引用，用於清理
  private resizeHandler?: () => void;
  // 遮罩寬度的 CSS 變數名稱
  private readonly CAROUSEL_MASK_WIDTH_VAR = '--carousel-mask-width';
  // 使用 setter 來獲取 DOM 元素並設定 IntersectionObserver
  @ViewChild('resultsLayout') set resultsLayoutSetter(element: ElementRef) {
    if (element) {
      this.resultsLayoutElement = element;
      // 確保在資料載入後才設定觀察者
      if (this.isDataLoaded) {
        this.setupIntersectionObserver();
      }
    }
  }
  // 新增一個屬性來儲存 ElementRef，以便在 API 呼叫完成後使用
  private resultsLayoutElement: ElementRef | null = null;
  // 新增狀態旗標，用來檢查資料是否已載入
  isDataLoaded = false;
  homeItem: string[] = [];
  typeList: {
    typeValue: string;
    typeName: string;
  }[] = [];
  selectNewsType: string = '';
  newsList: newsItem[] = [];
  newsMenuitemId: string = '';
  chartOption: echarts.EChartsOption = {};

  dictionaryBrowseCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  forumEventCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  klokahReachCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  instructionalKitItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  hasAnimated = false;
  suggestType = SuggestType.OnlineDictionary;
  SuggestTypeList = SuggestType;
  PageCurrentIndex: number = 0;
  isButtonVisible: boolean = false;
  bannerList: Banner[] = [];
  customOptions: OwlOptions = {
    loop: true,
    mouseDrag: false,
    touchDrag: false,
    pullDrag: false,
    dots: true,
    nav: false,
    navSpeed: 700,
    navText: [
      '<span class="material-symbols-outlined">chevron_left</span>',
      '<span class="material-symbols-outlined">chevron_right</span>'
    ], // 使用 Material Icons 箭頭
    center: true, // 置中顯示活動項目
    stagePadding: 500, // 調整此數值 (px)，以控制左右兩側露出的面積。
    margin: 10,
    autoplay: true,
    autoplayTimeout: 10000,
    // 方案A
    // autoplayHoverPause: true, //滑鼠放上去輪播會暫停
    // autoplayMouseleaveTimeout: 10000, //滑鼠離開輪播會x秒後輪播下一個內容 預設1
    // 方案B
    autoplayHoverPause: false, //滑鼠放上去輪播不會暫停

    responsive: {
      0: {
        items: 1, // 任何螢幕尺寸都只顯示 1 個完整的項目
        stagePadding: 1, // 小螢幕時減少 padding
        center: false, // 小螢幕時不置中，避免兩側項目顯示問題
      },
      768: {
        items: 1,
        stagePadding: 1, // 較大螢幕時增加 padding
        center: true,
      },
      900: {
        items: 1,
        stagePadding: 200, // 較大螢幕時增加 padding
        center: true,
      },
      1200: {
        items: 1,
        stagePadding: 300, // 大螢幕時增加 padding
        center: true,
      },
      1600: {
        items: 1,
        stagePadding: 400, // 超大螢幕時增加 padding
        center: true,
      },
      1920: {
        items: 1,
        stagePadding: 500, // 超大螢幕時增加 padding
        center: true,
      }
    }
  }

  constructor(
    private homeService: HomeService,
    private shareService: ShareService,
    public dialog: MatDialog,
    private bannerService: BannerService,
  ) { }

  ngOnInit(): void {
    this.getTypeList();
    this.getTextCloud();
    this.getAchievements();
    this.homeService.getManageHomeSort().subscribe({
      next: (resp: getHomeSortResp) => {
        this.homeItem = resp.data;
        this.getBannerList();
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', `${err.error.message}`, 'error');
      },
    });
  }

  ngAfterViewInit() {
    // 初始化遮罩寬度並監聽視窗大小變化
    this.updateCarouselMaskWidth();
    this.resizeHandler = () => {
      this.updateCarouselMaskWidth();
    };
    window.addEventListener('resize', this.resizeHandler);
  }

  // 重構後的 setupIntersectionObserver
  setupIntersectionObserver() {
    if (!this.resultsLayoutElement || this.hasAnimated) {
      return;
    }

    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5,
    };

    const observer = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        // 只有在資料已載入且元素進入可視區域時才觸發
        if (entry.isIntersecting && !this.hasAnimated && this.isDataLoaded) {
          this.animateAllCounts();
          this.hasAnimated = true;
        } else if (!entry.isIntersecting && this.hasAnimated) {
          this.resetAllCounts();
          this.hasAnimated = false;
        }
      });
    }, options);

    // 立即檢查元素是否已經在可視區域內
    const rect =
      this.resultsLayoutElement.nativeElement.getBoundingClientRect();
    const isInitiallyIntersecting =
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth);

    // 如果一開始就在可視區域內且資料已載入，直接觸發動畫
    if (isInitiallyIntersecting && !this.hasAnimated && this.isDataLoaded) {
      this.animateAllCounts();
      this.hasAnimated = true;
    }

    observer.observe(this.resultsLayoutElement.nativeElement);
  }

  // 觸發所有動畫的函數
  animateAllCounts() {
    this.animateCount(this.dictionaryBrowseCountItem, 2000);
    this.animateCount(this.forumEventCountItem, 2000);
    this.animateCount(this.klokahReachCountItem, 2000);
    this.animateCount(this.instructionalKitItem, 2000);
  }

  // 重置所有計數的函數
  resetAllCounts() {
    this.dictionaryBrowseCountItem.currentCount = 0;
    this.forumEventCountItem.currentCount = 0;
    this.klokahReachCountItem.currentCount = 0;
    this.instructionalKitItem.currentCount = 0;
  }

  animateCount(item: achievementsAnimateCount, duration: number) {
    const start = 0;
    const end = item.count;
    const startTime = performance.now();

    const step = (currentTime: number) => {
      const elapsedTime = currentTime - startTime;
      const progress = Math.min(elapsedTime / duration, 1);
      item.currentCount = Math.floor(start + (end - start) * progress);

      if (progress < 1) {
        requestAnimationFrame(step);
      }
    };
    requestAnimationFrame(step);
  }

  // 使用 crypto.getRandomValues 生成安全的隨機數 (0-1)
  private getSecureRandom(): number {
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    // 將 Uint32 值轉換為 0-1 之間的浮點數
    return array[0] / (0xFFFFFFFF + 1);
  }

  // 使用位運算實現四捨五入（不使用 Math.round）
  private secureRound(value: number): number {
    // 對於正數，使用位運算實現四捨五入
    return (value + 0.5) | 0;
  }

  getTextCloud() {
    this.shareService.getTextCloud().subscribe({
      next: (resp: getTextCloudResp) => {
        let data: { name: string; value: number }[] = [];
        resp.data.data.map((item) => {
          data.push({
            name: item.text,
            value: item.weight,
          });
        });
        this.chartOption = {
          tooltip: {
            show: false, // 關閉 tooltip 提示框
          },
          series: [
            {
              type: 'wordCloud',
              gridSize: 8,
              sizeRange: [12, 50], // 字體大小範圍
              rotationRange: [0, 0],
              shape: 'circle', // 支援 'circle', 'cardioid', 'diamond', 'triangle-forward', 'star'
              width: '100%',
              height: '100%',
              textStyle: {
                color: () => {
                  // 固定色相 (H) 在 28 度 (橘色系)
                  const h = 28;

                  // 隨機飽和度 (S): 保持較高飽和度，在 70% 到 100% 之間
                  const s = this.secureRound(this.getSecureRandom() * 40 + 70); // 70 ~ 100

                  // 隨機亮度 (L): 在 40% 到 70% 之間 (避免太深或太亮)
                  const l = this.secureRound(this.getSecureRandom() * 30 + 40); // 40 ~ 70

                  // 輸出 hsl 格式的顏色
                  return `hsl(${h}, ${s}%, ${l}%)`;
                },
              },
              emphasis: {
                focus: 'self',
                textStyle: {
                  fontWeight: 'bold',
                  textShadowBlur: 15,
                  textShadowColor: "transparent", // hover陰影顏色
                },
              },
              data: data,
            },
          ],
        };
      },
    });
  }

  getAchievements() {
    this.shareService.getAchievements().subscribe({
      next: (resp: getAchievementsResp) => {
        this.dictionaryBrowseCountItem.count = resp.data.dictionaryBrowseCount;
        this.forumEventCountItem.count = resp.data.forumEventCount;
        this.klokahReachCountItem.count = resp.data.klokahReachCount;
        this.instructionalKitItem.count = resp.data.instructionalKit;
        this.isDataLoaded = true; // 資料載入成功，設定狀態旗標
        // 如果元素已存在，重新呼叫 setupIntersectionObserver
        if (this.resultsLayoutElement) {
          this.setupIntersectionObserver();
        }
      },
    });
  }

  getTypeList() {
    this.shareService.getTypeList('前台最新消息').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
        if (this.typeList && this.typeList.length > 0) {
          this.selectNewsType = this.typeList[0].typeValue;
          this.selectNewsList(this.selectNewsType);
        }
      },
      error: () => { },
    });
  }

  selectNewsList(type: string) {
    this.selectNewsType = type;
    this.homeService.getNewsListWithHome(type).subscribe({
      next: (resp: getNewsListWithHomeResp) => {
        this.newsMenuitemId = resp.data.menuitemId;
        this.newsList = resp.data.data;
      },
      error: () => { },
    });
  }

  /** 拖拉結束 */
  drop($event: CdkDragDrop<surveyItem[]>) {
    if ($event.previousIndex === $event.currentIndex) {
      return;
    }
    Swal.fire({
      title: '確認變更排序',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        moveItemInArray(
          this.homeItem,
          $event.previousIndex,
          $event.currentIndex
        );
        this.updataHomeSort();
      }
    });
  }

  updataHomeSort() {
    this.homeService.updateHomeSort(this.homeItem).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code === 200) {
          Swal.fire('成功', '排序更新成功', 'success');
        } else {
          Swal.fire('失敗', '排序更新失敗，請重新嘗試', 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('錯誤', '連線伺服器失敗，請稍後再試', 'error');
      },
    });
  }

  getBannerList() {
    this.bannerService.getBannerList().subscribe({
      next: (resp: getBannerListResp) => {
        this.bannerList = resp.data.bannerList;
        setTimeout(() => {
          this.checkAndPlayFirstVideo();
        }, 200);
      },
      error: () => { },
    });
  }

  // 檢查第一個項目是否為影片並自動播放
  private checkAndPlayFirstVideo(): void {
    // 先暫停所有影片，確保沒有其他影片在播放
    this.pauseAllVideos();

    // 檢查第一個項目是否為影片
    if (this.bannerList && this.bannerList.length > 0 && this.bannerList[0].type === 'Video') {
      // 等待一小段時間確保暫停操作完成
      setTimeout(() => {
        // 再次確保所有影片都已暫停
        this.pauseAllVideos();

        // 計算 bannerList[0] 對應的 video 在 carouselVideos 中的索引
        // 因為 carouselVideos 只包含 Video 類型的元素，需要計算在 bannerList[0] 之前有多少個 Video
        let videoIndex = 0;
        for (let i = 0; i < 0; i++) {
          if (this.bannerList[i] && this.bannerList[i].type === 'Video') {
            videoIndex++;
          }
        }
        // 由於 bannerList[0] 是第一個項目且是 Video，所以 videoIndex 為 0

        // 使用 ViewChildren 來取得對應 bannerList[0] 的 video 元素
        if (this.carouselVideos && this.carouselVideos.length > videoIndex) {
          // 先暫停所有其他影片
          this.carouselVideos.forEach((videoRef, index) => {
            if (index !== videoIndex && videoRef && videoRef.nativeElement) {
              const video = videoRef.nativeElement;
              video.pause();
              video.currentTime = 0;
            }
          });

          // 取得對應 bannerList[0] 的 video（即 carouselVideos[0]）
          const targetVideoRef = this.carouselVideos.toArray()[videoIndex];
          if (targetVideoRef && targetVideoRef.nativeElement) {
            const video = targetVideoRef.nativeElement;
            // 等待一小段時間後播放目標影片（確保其他影片已暫停）
            setTimeout(() => {
              this.playVideoWhenReady(video);
            }, 100);
          }
        }
      }, 150); // 增加等待時間以確保 Win10 兼容性
    }
  }

  // 等待影片載入完成後播放
  private playVideoWhenReady(video: HTMLVideoElement): void {
    // 確保影片靜音
    video.muted = true;
    // 重設到開頭
    video.currentTime = 0;

    // 檢查影片是否已經載入足夠的資料
    // readyState: 0=HAVE_NOTHING, 1=HAVE_METADATA, 2=HAVE_CURRENT_DATA, 3=HAVE_FUTURE_DATA, 4=HAVE_ENOUGH_DATA
    if (video.readyState >= 2) {
      // 已經有足夠資料，直接播放
      video.play().catch((error) => {
        console.log('影片播放失敗:', error);
      });
    } else {
      // 等待影片載入完成
      const playVideo = () => {
        video.currentTime = 0;
        video.play().catch((error) => {
          console.log('影片播放失敗:', error);
        });
      };

      // 監聽多個事件以確保影片可以播放
      const onCanPlay = () => {
        playVideo();
        video.removeEventListener('canplay', onCanPlay);
        video.removeEventListener('loadeddata', onLoadedData);
        video.removeEventListener('canplaythrough', onCanPlayThrough);
      };

      const onLoadedData = () => {
        playVideo();
        video.removeEventListener('canplay', onCanPlay);
        video.removeEventListener('loadeddata', onLoadedData);
        video.removeEventListener('canplaythrough', onCanPlayThrough);
      };

      const onCanPlayThrough = () => {
        playVideo();
        video.removeEventListener('canplay', onCanPlay);
        video.removeEventListener('loadeddata', onLoadedData);
        video.removeEventListener('canplaythrough', onCanPlayThrough);
      };

      video.addEventListener('canplay', onCanPlay);
      video.addEventListener('loadeddata', onLoadedData);
      video.addEventListener('canplaythrough', onCanPlayThrough);

      // 如果影片已經開始載入，觸發 load 事件
      if (video.readyState === 0) {
        video.load();
      }
    }
  }
  settingBanner() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '800px',
        maxHeight: '90%',
        showHeader: true,
        title: '首頁輪播設定',
        contentTemplate: BannerDialogComponent,
      },
    }).afterClosed().subscribe(() => {
      window.location.reload();
    });
  }

  // 處理滑鼠進入事件
  onMouseEnter() {
    this.isButtonVisible = true;
  }

  // 處理滑鼠離開事件
  onMouseLeave() {
    this.isButtonVisible = false;
  }


  onCarouselTranslated(event: any) {
    // 取得當前活躍的 bannerList 索引
    const currentBannerIndex = event.startPosition;

    // 檢查當前索引對應的 banner 是否為 Video 類型
    if (this.bannerList && this.bannerList[currentBannerIndex]?.type === 'Video') {
      // 計算在 bannerList 中，當前索引之前有多少個 Video 類型的項目
      // 這個數量就是對應的 video 在 carouselVideos 中的索引
      let videoIndex = 0;
      for (let i = 0; i < currentBannerIndex; i++) {
        if (this.bannerList[i] && this.bannerList[i].type === 'Video') {
          videoIndex++;
        }
      }

      // 取得所有影片元素（包含克隆的）
      const allVideoElements = this.carouselVideos.toArray().map(ref => ref.nativeElement);
      const expectedUrl = this.bannerList[currentBannerIndex].url;

      // 方法1：先嘗試使用計算出的索引找到對應的 video，並檢查它是否在活躍的幻燈片中
      if (allVideoElements.length > videoIndex) {
        const targetVideo = allVideoElements[videoIndex];
        const parentSlide = targetVideo?.closest('.owl-item');

        if (parentSlide && parentSlide.classList.contains('center')) {
          // 驗證 URL 是否匹配
          if (targetVideo && (targetVideo.src.includes(expectedUrl) || targetVideo.getAttribute('src') === expectedUrl)) {
            this.pauseAllVideos();
            setTimeout(() => {
              this.playVideoWhenReady(targetVideo);
            }, 100);
            return;
          }
        }
      }

      // 方法2：如果方法1失敗，遍歷所有影片元素，找出位於活躍幻燈片中的那個
      for (const video of allVideoElements) {
        const parentSlide = video.closest('.owl-item');
        if (parentSlide && parentSlide.classList.contains('center')) {
          // 檢查 URL 是否匹配
          if (video.src.includes(expectedUrl) || video.getAttribute('src') === expectedUrl) {
            this.pauseAllVideos();
            setTimeout(() => {
              this.playVideoWhenReady(video);
            }, 100);
            return;
          }
        }
      }

      // 方法3：如果以上方法都失敗，直接從 DOM 查詢活躍的幻燈片
      const activeSlide = document.querySelector('.owl-item.center');
      if (activeSlide) {
        const activeVideo = activeSlide.querySelector('video') as HTMLVideoElement;
        if (activeVideo && (activeVideo.src.includes(expectedUrl) || activeVideo.getAttribute('src') === expectedUrl)) {
          this.pauseAllVideos();
          setTimeout(() => {
            this.playVideoWhenReady(activeVideo);
          }, 100);
          return;
        }
      }

      this.pauseAllVideos();
    } else {
      // 當前幻燈片是圖片，停止所有影片
      this.pauseAllVideos();
    }
  }

  // 暫停所有影片的輔助方法
  private pauseAllVideos(): void {
    // 方法1: 使用 ViewChildren 暫停所有影片
    if (this.carouselVideos && this.carouselVideos.length > 0) {
      this.carouselVideos.forEach((videoRef) => {
        if (videoRef && videoRef.nativeElement) {
          const video = videoRef.nativeElement;
          // 強制暫停，不管當前狀態
          video.pause();
          video.currentTime = 0; // 重設到開頭
        }
      });
    }
  }

  // 根據螢幕寬度動態計算並設定輪播遮罩寬度
  private updateCarouselMaskWidth(): void {
    const windowWidth = window.innerWidth;
    let stagePadding = 1; // 預設值
    let buffer = 0; // 緩衝值

    // 根據螢幕寬度取得對應的 stagePadding 值和緩衝值
    if (windowWidth >= 1920) {
      console.log('windowWidth >= 1920');
      stagePadding = 380;
      buffer = 105; // 較大螢幕使用較大緩衝
    } else if (windowWidth >= 1600) {
      console.log('windowWidth >= 1600');
      stagePadding = 355;
      buffer = 30; // 1600 尺寸使用適中緩衝
    } else if (windowWidth >= 1200) {
      console.log('windowWidth >= 1200');
      stagePadding = 300;
      buffer = -8; // 較小螢幕使用較小緩衝
    } else if (windowWidth >= 900) {
      console.log('windowWidth >= 900');
      stagePadding = 200;
      buffer = -5; // 小螢幕使用最小緩衝
    } else if (windowWidth >= 768) {
      console.log('windowWidth >= 768');
      stagePadding = 1;
      buffer = 0;
    } else {
      stagePadding = 1;
      buffer = 0;
    }

    // 計算遮罩寬度：stagePadding 加上對應的緩衝值
    const maskWidth = stagePadding + buffer;

    // 設定 CSS 變數到 document root
    document.documentElement.style.setProperty(this.CAROUSEL_MASK_WIDTH_VAR, `${maskWidth}px`);
  }
}

import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { surveyItem } from '../../../interface/survey.interface';
import {
  getHomeSortResp,
  HomeService,
} from '../../../core/services/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import {
  defaultItem,
  getAchievementsResp,
  getTextCloudResp,
  getTypeListResp,
} from '../../../interface/share.interface';
import { ShareService } from '../../../core/services/share.service';
import {
  getNewsListWithHomeResp,
  newsItem,
} from '../../../interface/home.interface';
import 'echarts-wordcloud';

export interface achievementsAnimateCount {
  currentCount: number;
  count: number;
}

export enum SuggestType {
  OnlineDictionary = '原住民族語言線上辭典',
  Klokah = '族語E樂園',
  Lokahsu = 'lokahsu原住民族語言能力認證測驗',
  TalentDatabase = '原住民族語言人才資料庫',
  Glossary = '原住民族語學習詞表系統',
  LearningCenter = '原住民族語言學習中心課程平台',
  PromotionOrganization = '原住民族語言推動組織',
}

@Component({
  selector: 'app-home',
  standalone: false,

  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
})
export class HomeComponent implements OnInit {
  // 使用 setter 來獲取 DOM 元素並設定 IntersectionObserver
  @ViewChild('resultsLayout') set resultsLayoutSetter(element: ElementRef) {
    if (element) {
      this.resultsLayoutElement = element;
      // 確保在資料載入後才設定觀察者
      if (this.isDataLoaded) {
        this.setupIntersectionObserver();
      }
    }
  }
  // 新增一個屬性來儲存 ElementRef，以便在 API 呼叫完成後使用
  private resultsLayoutElement: ElementRef | null = null;
  // 新增狀態旗標，用來檢查資料是否已載入
  isDataLoaded = false;
  homeItem: string[] = [];
  typeList: {
    typeValue: string;
    typeName: string;
  }[] = [];
  selectNewsType: string = '';
  newsList: newsItem[] = [];
  newsMenuitemId: string = '';
  chartOption: echarts.EChartsOption = {};

  dictionaryBrowseCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  forumEventCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  klokahReachCountItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  instructionalKitItem: achievementsAnimateCount = {
    currentCount: 0,
    count: 0,
  };
  hasAnimated = false;
  suggestType = SuggestType.OnlineDictionary;
  SuggestTypeList = SuggestType;

  constructor(
    private homeService: HomeService,
    private shareService: ShareService
  ) { }

  ngOnInit(): void {
    this.getTypeList();
    this.getTextCloud();
    this.getAchievements();
    this.homeService.getHomeSort().subscribe({
      next: (resp: getHomeSortResp) => {
        this.homeItem = resp.data;
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', `${err.error.message}`, 'error');
      },
    });
  }

  ngAfterViewInit() { }

  // 重構後的 setupIntersectionObserver
  setupIntersectionObserver() {
    if (!this.resultsLayoutElement || this.hasAnimated) {
      return;
    }

    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5,
    };

    const observer = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        // 只有在資料已載入且元素進入可視區域時才觸發
        if (entry.isIntersecting && !this.hasAnimated && this.isDataLoaded) {
          this.animateAllCounts();
          this.hasAnimated = true;
        } else if (!entry.isIntersecting && this.hasAnimated) {
          this.resetAllCounts();
          this.hasAnimated = false;
        }
      });
    }, options);

    // 立即檢查元素是否已經在可視區域內
    const rect =
      this.resultsLayoutElement.nativeElement.getBoundingClientRect();
    const isInitiallyIntersecting =
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth);

    // 如果一開始就在可視區域內且資料已載入，直接觸發動畫
    if (isInitiallyIntersecting && !this.hasAnimated && this.isDataLoaded) {
      this.animateAllCounts();
      this.hasAnimated = true;
    }

    observer.observe(this.resultsLayoutElement.nativeElement);
  }

  // 觸發所有動畫的函數
  animateAllCounts() {
    this.animateCount(this.dictionaryBrowseCountItem, 2000);
    this.animateCount(this.forumEventCountItem, 2000);
    this.animateCount(this.klokahReachCountItem, 2000);
    this.animateCount(this.instructionalKitItem, 2000);
  }

  // 重置所有計數的函數
  resetAllCounts() {
    this.dictionaryBrowseCountItem.currentCount = 0;
    this.forumEventCountItem.currentCount = 0;
    this.klokahReachCountItem.currentCount = 0;
    this.instructionalKitItem.currentCount = 0;
  }

  animateCount(item: achievementsAnimateCount, duration: number) {
    const start = 0;
    const end = item.count;
    const startTime = performance.now();

    const step = (currentTime: number) => {
      const elapsedTime = currentTime - startTime;
      const progress = Math.min(elapsedTime / duration, 1);
      item.currentCount = Math.floor(start + (end - start) * progress);

      if (progress < 1) {
        requestAnimationFrame(step);
      }
    };
    requestAnimationFrame(step);
  }

  // 使用 crypto.getRandomValues 生成安全的隨機數 (0-1)
  private getSecureRandom(): number {
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    // 將 Uint32 值轉換為 0-1 之間的浮點數
    return array[0] / (0xFFFFFFFF + 1);
  }

  // 使用位運算實現四捨五入（不使用 Math.round）
  private secureRound(value: number): number {
    // 對於正數，使用位運算實現四捨五入
    return (value + 0.5) | 0;
  }

  getTextCloud() {
    this.shareService.getTextCloud().subscribe({
      next: (resp: getTextCloudResp) => {
        let data: { name: string; value: number }[] = [];
        resp.data.data.map((item) => {
          data.push({
            name: item.text,
            value: item.weight,
          });
        });
        this.chartOption = {
          tooltip: {
            show: false, // 關閉 tooltip 提示框
          },
          series: [
            {
              type: 'wordCloud',
              gridSize: 8,
              sizeRange: [12, 50], // 字體大小範圍
              rotationRange: [0, 0],
              shape: 'circle', // 支援 'circle', 'cardioid', 'diamond', 'triangle-forward', 'star'
              width: '100%',
              height: '100%',
              textStyle: {
                color: () => {
                  // 固定色相 (H) 在 28 度 (橘色系)
                  const h = 28;

                  // 隨機飽和度 (S): 保持較高飽和度，在 70% 到 100% 之間
                  const s = this.secureRound(this.getSecureRandom() * 40 + 70); // 70 ~ 100

                  // 隨機亮度 (L): 在 40% 到 70% 之間 (避免太深或太亮)
                  const l = this.secureRound(this.getSecureRandom() * 30 + 40); // 40 ~ 70

                  // 輸出 hsl 格式的顏色
                  return `hsl(${h}, ${s}%, ${l}%)`;
                },
              },
              emphasis: {
                focus: 'self',
                textStyle: {
                  fontWeight: 'bold',
                  textShadowBlur: 15,
                  textShadowColor: "transparent", // hover陰影顏色
                },
              },
              data: data,
            },
          ],
        };
      },
    });
  }

  getAchievements() {
    this.shareService.getAchievements().subscribe({
      next: (resp: getAchievementsResp) => {
        this.dictionaryBrowseCountItem.count = resp.data.dictionaryBrowseCount;
        this.forumEventCountItem.count = resp.data.forumEventCount;
        this.klokahReachCountItem.count = resp.data.klokahReachCount;
        this.instructionalKitItem.count = resp.data.instructionalKit;
        this.isDataLoaded = true; // 資料載入成功，設定狀態旗標
        // 如果元素已存在，重新呼叫 setupIntersectionObserver
        if (this.resultsLayoutElement) {
          this.setupIntersectionObserver();
        }
      },
    });
  }

  getTypeList() {
    this.shareService.getTypeList('前台最新消息').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
        if (this.typeList && this.typeList.length > 0) {
          this.selectNewsType = this.typeList[0].typeValue;
          this.selectNewsList(this.selectNewsType);
        }
      },
      error: () => { },
    });
  }

  selectNewsList(type: string) {
    this.selectNewsType = type;
    this.homeService.getNewsListWithHome(type).subscribe({
      next: (resp: getNewsListWithHomeResp) => {
        this.newsMenuitemId = resp.data.menuitemId;
        this.newsList = resp.data.data;
      },
      error: () => { },
    });
  }

  /** 拖拉結束 */
  drop($event: CdkDragDrop<surveyItem[]>) {
    if ($event.previousIndex === $event.currentIndex) {
      return;
    }
    Swal.fire({
      title: '確認變更排序',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        moveItemInArray(
          this.homeItem,
          $event.previousIndex,
          $event.currentIndex
        );

        this.updataHomeSort();
      }
    });
  }

  updataHomeSort() {
    this.homeService.updateHomeSort(this.homeItem).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code === 200) {
          Swal.fire('成功', '排序更新成功', 'success');
        } else {
          Swal.fire('失敗', '排序更新失敗，請重新嘗試', 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('錯誤', '連線伺服器失敗，請稍後再試', 'error');
      },
    });
  }
}

import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { NewsService } from '../../../../core/services/news.service';
import { LineService } from '../../../../core/services/line.service';
import {
  getLineListResp,
  lineItem,
} from '../../../../interface/line.interface';
import { getImgsListReq } from '../../../../interface/imgs.interface';

@Component({
  selector: 'app-line-list',
  standalone: false,

  templateUrl: './line-list.component.html',
  styleUrl: './line-list.component.scss',
})
export class LineListComponent {
  loading: boolean = false;
  menuItemId: string = '';

  title: string = '';
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  lineList: lineItem[] = [];

  constructor(
    private newsService: NewsService,
    private lineService: LineService,
    private _route: ActivatedRoute,
    private _router: Router
  ) {}

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getLineList();
    });

  }

  getLineList() {
    let req: getImgsListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.lineService.getLineList(req).subscribe({
      next: (resp: getLineListResp) => {
        this.title = resp.data.title;
        this.lineList = resp.data.data;
        this.totalCount = resp.data.totalCount;
        this.loading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  addLine() {
    this._router.navigate([`/manage/${this.menuItemId}/line/edit`]);
  }

  editLine(id: string) {
    this._router.navigate([`/manage/${this.menuItemId}/line/edit`], {
      queryParams: { id: id },
    });
  }

  /** 刪除消息 */
  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.newsService.deleteNews(id).subscribe({
          next: (res) => {
            this.loading = false;
            Swal.fire({
              title: '刪除成功',
              icon: 'success',
              showCancelButton: false,
              reverseButtons: true,
            }).then(() => {
              this.getLineList();
            });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire({
              title: '刪除失敗',
              icon: 'error',
              showCancelButton: false,
              reverseButtons: true,
            });
          },
        });
      }
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getLineList();
  }
}

import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { FileService } from '../../../../core/services/file.service';
import { getFileDescriptionResp } from '../../../../interface/file.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-file-description',
  standalone: false,

  templateUrl: './file-description.component.html',
  styleUrl: './file-description.component.scss',
})
export class FileDescriptionComponent {
  loading = false;
  description: string = '';
  enDescription: string = '';
  constructor(
    public dialogRef: MatDialogRef<FileDescriptionComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fileService: FileService
  ) {
    this.getDescription();
  }

  getDescription() {
    this.fileService.getFileDescription(this.data.data).subscribe({
      next: (resp: getFileDescriptionResp) => {
        
        this.description = resp.data.description;
        this.enDescription = resp.data.enDescription;
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  submit() {
    if (!this.description && !this.enDescription) {
      Swal.fire('請輸入中英文描述', '', 'warning');
      return;
    }
    this.fileService
      .uploadFileDescription(
        this.data.data,
        this.description,
        this.enDescription
      )
      .subscribe({
        next: (resp: defaultItem) => {
          if (resp.code === 200) {
            Swal.fire('成功', '更新成功', 'success').then(() => {
              this.dialogRef.close();
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }
}

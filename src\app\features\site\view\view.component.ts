import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApprovalStatus } from '../../../enum/share.enum';
import { environment } from '../../../../environments/environment';
import { ReviewService } from '../../../core/services/review.service';
import { defaultItem } from '../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { approvalReq } from '../../../interface/review.interface';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { ShareService } from '../../../core/services/share.service';

@Component({
  selector: 'app-view',
  standalone: false,

  templateUrl: './view.component.html',
  styleUrl: './view.component.scss',
})
export class ViewComponent {
  loading: boolean = false;
  menuItemId: string = ''; //選單id
  typeGroupId: string = ''; //內容id
  type: string = ''; //選單類別
  status: ApprovalStatus = ApprovalStatus.BeforeApproval; //審核狀態 送審前or送審後
  url: SafeUrl = '';
  approval: boolean = true;
  reason: string = '';
  ApprovalStatus = ApprovalStatus;
  openStatus: boolean = true;
  file: File | null = null;
  fileName: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private reviewService: ReviewService,
    private sanitizer: DomSanitizer,
    private shareService: ShareService
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.menuItemId = queryParams.get('menuItemId') as string;
      this.typeGroupId = queryParams.get('typeGroupId') as string;
      this.type = queryParams.get('type') as string;
      this.status = Number(queryParams.get('status')) as ApprovalStatus;
    });
  }

  ngOnInit() {
    this.getUrl();
  }

  getPolicy(policy: string) {
    return this.shareService.getPolicy(policy);
  }

  getUrl() {
    const isPreviewParam =
      this.status === ApprovalStatus.EndApproval ? '' : '&isPreview=true';
    switch (this.type) {
      case 'Img':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/ImgDetail?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        );
        break;
      case 'Line':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/LineDetail?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        );
        break;
      case 'FAQ':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/FAQ?typeGroupId=${this.typeGroupId}&menuItemId=${this.menuItemId}${isPreviewParam}`
        );
        break;
      case 'HTML':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/HTML?menuItemId=${this.menuItemId}${isPreviewParam}`
        );
        break;
      case 'HtmlZip':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/HtmlZip?menuItemId=${this.menuItemId}${isPreviewParam}`
        );
        break;
      case 'Content':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/Content?menuItemId=${this.menuItemId}${isPreviewParam}`
        );
        break;
      case 'ShareResource':
        // this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
        //   `${environment.url}/Content?menuItemId=${this.menuItemId}${isPreviewParam}`
        // );
        break;
      case 'HyperLink':
        // this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
        //   `${environment.url}/Content?menuItemId=${this.menuItemId}${isPreviewParam}`
        // );
        break;
      case 'Video':
        // this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
        //   `${environment.url}/NewsDetail?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        // );
        break;
      case 'News':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/NewsDetail?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        );
        break;
      case 'Survey':
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
          `${environment.url}/Survey?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        );
        break;
      case 'Ebook':
        // this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
        //   `${environment.url}/Survey?typeGroupId=${this.typeGroupId}${isPreviewParam}`
        // );
        break;
    }
    console.log('Review:::', this.url);
  }

  cancel() {
    if (this.status === ApprovalStatus.BeforeApproval) {
      switch (this.type) {
        case 'Img':
          this.router.navigate([`/manage/${this.menuItemId}/img/edit`], {
            queryParams: { id: this.typeGroupId },
          });
          break;
        case 'Line':
          this.router.navigate([`/manage/${this.menuItemId}/line/edit`], {
            queryParams: { id: this.typeGroupId },
          });
          break;
        case 'FAQ':
          this.router.navigate([`/manage/${this.menuItemId}/faq/edit`], {
            queryParams: { id: this.typeGroupId },
          });
          break;
        case 'HTML':
          this.router.navigate([`/manage/${this.menuItemId}/html`]);
          break;
        case 'HtmlZip':
          break;
        case 'Content':
          this.router.navigate([`/manage/${this.menuItemId}/content`]);
          break;
        case 'ShareResource':
          break;
        case 'HyperLink':
          break;
        case 'Video':
          break;
        case 'News':
          this.router.navigate([`/manage/${this.menuItemId}/news/edit`], {
            queryParams: { id: this.typeGroupId },
          });
          break;
        case 'Survey':
          this.router.navigate([`/manage/${this.menuItemId}/survey`]);
          break;
        case 'Ebook':
          break;
      }
    } else {
      history.back();
    }
  }

  submitForReview() {
    this.loading = true;
    this.reviewService
      .submitForReview({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '送審完成', 'success').then(() => {
              this.backList();
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }

  selectGalleryFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    const maxFileSize = 3 * 1024 * 1024;
    if (file.size > maxFileSize) {
      Swal.fire('檔案大小超過 3MB，請重新選擇', '', 'warning');
      this.loading = false;
      return;
    }
    if (
      file.type !== 'image/jpeg' &&
      file.type !== 'image/png' &&
      file.type !== 'application/x-zip-compressed' &&
      file.type !== 'application/pdf'
    ) {
      Swal.fire('請選擇jpg或png或zip檔案', '', 'warning');
      this.loading = false;
      return;
    }
    this.file = file;

    if (file.name) {
      this.loading = false;
    }
    this.fileName = file.name;
  }

  send() {
    this.loading = true;
    let req: approvalReq = {
      typeGroupId: this.typeGroupId,
      approval: this.approval,
      menuItemType: this.type,
      reason: this.reason,
      file: this.file,
    };
    this.reviewService.approval(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '審核完成', 'success').then(() => {
            history.back();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  submitForPublish() {
    this.loading = true;
    this.reviewService
      .publish({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '發布完成', 'success').then(() => {
              this.backList();
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }

  backList() {
    switch (this.type) {
      case 'Img':
        this.router.navigate([`/manage/${this.menuItemId}/img/list`]);
        break;
      case 'Line':
        this.router.navigate([`/manage/${this.menuItemId}/line/list`]);
        break;
      case 'FAQ':
        this.router.navigate([`/manage/${this.menuItemId}/faq/list`], {
          queryParams: { id: this.typeGroupId },
        });
        break;
      case 'HTML':
        this.router.navigate([`/manage/${this.menuItemId}/html`]);
        break;
      case 'HtmlZip':
        break;
      case 'Content':
        this.router.navigate([`/manage/${this.menuItemId}/content`]);
        break;
      case 'ShareResource':
        break;
      case 'HyperLink':
        break;
      case 'Video':
        break;
      case 'News':
        this.router.navigate([`/manage/${this.menuItemId}/news/list`], {
          queryParams: { id: this.typeGroupId },
        });
        break;
      case 'Survey':
        this.router.navigate([`/manage/${this.menuItemId}/survey`]);
        break;
      case 'Ebook':
        break;
    }
  }
}

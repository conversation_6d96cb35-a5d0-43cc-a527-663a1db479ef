<div class="contents">
    <div class="block">
        <div class="table-container">
            <table class="review-table">
                <thead>
                    <tr>
                        <th width="">電子信箱</th>
                        <th width="70px">姓名</th>
                        <th width="70px">抬頭職稱</th>
                        <th width="160px">開信時間</th>
                        <th width="160px">點擊時間</th>
                        <th width="160px">發送佇列時間</th>
                    </tr>
                </thead>
                <tbody>
                    @for (item of eNewsLetterColumn; track item) {
                    <tr>
                        <td data-label="電子信箱">{{item.email}}</td>
                        <td data-label="姓名">{{item.name}}</td>
                        <td data-label="抬頭職稱">{{item.name}}</td>
                        <td data-label="開信時間">{{item.firstOpenDateTime|date:'yyyy-MM-dd HH:mm:ss'}}</td>
                        <td data-label="點擊時間">{{item.firstClickDateTime|date:'yyyy-MM-dd HH:mm:ss'}}</td>
                        <td data-label="發送佇列時間">{{item.actuallySendDateTime|date:'yyyy-MM-dd HH:mm:ss'}}</td>
                    </tr>
                    }@empty {
                    <tr>
                        <td colspan="6" style="text-align: center;">查無資料</td>
                    </tr>
                    }
                </tbody>
            </table>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </div>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">取消</button>
        <button mat-flat-button (click)="send()">送出</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
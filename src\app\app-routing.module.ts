import { ManageComponent } from './features/manage/manage.component';
import { HomeComponent } from './features/components/home/<USER>';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ContentComponent } from './features/site/content/content.component';
import { NewsEditComponent } from './features/site/news/news-edit/news-edit.component';
import { NewsListComponent } from './features/site/news/news-list/news-list.component';
import { NewsComponent } from './features/site/news/news.component';
import { ImgsComponent } from './features/site/imgs/imgs.component';
import { ImgsListComponent } from './features/site/imgs/imgs-list/imgs-list.component';
import { ImgsEditComponent } from './features/site/imgs/imgs-edit/imgs-edit.component';
import { LineEditComponent } from './features/site/line/line-edit/line-edit.component';
import { LineListComponent } from './features/site/line/line-list/line-list.component';
import { LineComponent } from './features/site/line/line.component';
import { ShareResourceComponent } from './features/site/share-resource/share-resource.component';
import { ShareResourceListComponent } from './features/site/share-resource/share-resource-list/share-resource-list.component';
import { FaqComponent } from './features/site/faq/faq.component';
import { FaqListComponent } from './features/site/faq/faq-list/faq-list.component';
import { FaqEditComponent } from './features/site/faq/faq-edit/faq-edit.component';
import { HtmlComponent } from './features/site/html/html.component';
import { VideoComponent } from './features/site/video/video.component';
import { VideoListComponent } from './features/site/video/video-list/video-list.component';
import { MorePageSettingListComponent } from './features/site/more-page-setting/more-page-setting-list/more-page-setting-list.component';
import { CreateWebListComponent } from './features/site/create-web/create-web-list/create-web-list.component';
import { ForgetPwComponent } from './changePw/forget-pw/forget-pw.component';
import { ChangePwComponent } from './changePw/change-pw/change-pw.component';
import { SurveyComponent } from './features/site/survey/survey.component';
import { ReviewListComponent } from './features/site/review/review-list/review-list.component';
import { ViewComponent } from './features/site/view/view.component';
import { ENewsletterComponent } from './features/site/e-newsletter/e-newsletter.component';
import { ENewsletterListComponent } from './features/site/e-newsletter/e-newsletter-list/e-newsletter-list.component';
import { ENewsletterEditComponent } from './features/site/e-newsletter/e-newsletter-edit/e-newsletter-edit.component';
import { ENewsletterViewComponent } from './features/site/e-newsletter/e-newsletter-view/e-newsletter-view.component';
import { EbookComponent } from './features/site/ebook/ebook.component';
import { EbookListComponent } from './features/site/ebook/ebook-list/ebook-list.component';
import { EbookEditComponent } from './features/site/ebook/ebook-edit/ebook-edit.component';
import { TextCloudComponent } from './features/site/text-cloud/text-cloud.component';
import { AuditLogComponent } from './features/site/audit-log/audit-log.component';
import { CustomerServiceSettingComponent } from './features/site/customer-service-setting/customer-service-setting.component';
import { DonateItemManageComponent } from './features/site/donate/donate-item-manage/donate-item-manage.component';
import { DonateAccountReconciliationComponent } from './features/site/donate/donate-account-reconciliation/donate-account-reconciliation.component';
import { DonateBankDetailComponent } from './features/site/donate/donate-bank-detail/donate-bank-detail.component';
import { ClickLogListComponent } from './features/site/click-log-list/click-log-list.component';
import { GuestViewLogComponent } from './features/site/guest-view-log/guest-view-log.component';
import { SearchKeywordLogComponent } from './features/site/search-keyword-log/search-keyword-log.component';
import { ChartComponent } from './features/site/chart/chart.component';
import { DatasetManageComponent } from './features/site/chart/dataset-manage/dataset-manage.component';
import { EbookViewComponent } from './features/site/ebook/ebook-view/ebook-view.component';
import { VideoEditComponent } from './features/site/video/video-edit/video-edit.component';
import { VideoViewComponent } from './features/site/video/video-view/video-view.component';
import { MemberManageEditComponent } from './features/site/memberManage/member-manage-edit/member-manage-edit.component';
import { MemberManageListComponent } from './features/site/memberManage/member-manage-list/member-manage-list.component';


const routes: Routes = [
  {
    path: '',
    redirectTo: 'manage/home',
    pathMatch: 'full',
  },
  {
    path: 'manage',
    redirectTo: 'manage/home',
    pathMatch: 'full',
  },
  {
    path: 'manage',
    component: ManageComponent,
    children: [
      {
        path: 'home',
        component: HomeComponent,
      },
      {
        path: 'view',
        component: ViewComponent,
      },
      {
        path: 'viewENewsletter',
        component: ENewsletterViewComponent,
      },
      {
        path: 'viewEbook',
        component: EbookViewComponent,
      },
      {
        path: 'viewVideo',
        component: VideoViewComponent,
      },
      {
        // 最新消息
        path: ':menuItemId/news',
        component: NewsComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: NewsListComponent,
          },
          {
            path: 'edit',
            component: NewsEditComponent,
          },
          {
            path: 'edit/:id',
            component: NewsEditComponent,
          },
        ],
      },
      {
        // 圖片式
        path: ':menuItemId/img',
        component: ImgsComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: ImgsListComponent,
          },
          {
            path: 'edit',
            component: ImgsEditComponent,
          },
          {
            path: 'edit/:id',
            component: ImgsEditComponent,
          },
        ],
      },
      {
        // 條列式
        path: ':menuItemId/line',
        component: LineComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: LineListComponent,
          },
          {
            path: 'edit',
            component: LineEditComponent,
          },
          {
            path: 'edit/:id',
            component: LineEditComponent,
          },
        ],
      },
      {
        // 電子報
        path: ':menuItemId/eNewsletter',
        component: ENewsletterComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: ENewsletterListComponent,
          },
          {
            path: 'edit',
            component: ENewsletterEditComponent,
          },
          {
            path: 'edit/:id',
            component: ENewsletterEditComponent,
          },
        ],
      },
      {
        // 書櫃
        path: ':menuItemId/ebook',
        component: EbookComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: EbookListComponent,
          },
          {
            path: 'edit',
            component: EbookEditComponent,
          },
          {
            path: 'edit/:id',
            component: EbookEditComponent,
          },
        ],
      },
      {
        // 資源分享
        path: ':menuItemId/shareResource',
        component: ShareResourceComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: ShareResourceListComponent,
          },
        ],
      },
      {
        // FAQ
        path: ':menuItemId/faq',
        component: FaqComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: FaqListComponent,
          },
          {
            path: 'edit',
            component: FaqEditComponent,
          },
          {
            path: 'edit/:id',
            component: FaqEditComponent,
          },
        ],
      },
      {
        // 內容
        path: ':menuItemId/content',
        component: ContentComponent,
      },
      {
        // HTML
        path: ':menuItemId/html',
        component: HtmlComponent,
      },
      {
        // 問卷發布
        path: ':menuItemId/survey',
        component: SurveyComponent,
      },
      {
        // 宣傳影片
        path: ':menuItemId/video',
        component: VideoComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'list',
          },
          {
            path: 'list',
            component: VideoListComponent,
          },
          {
            path: 'edit',
            component: VideoEditComponent,
          },
          {
            path: 'edit/:id',
            component: VideoEditComponent,
          },
        ],
      },
      {
        path: 'createWeb',
        component: CreateWebListComponent,
      },
      {
        path: 'morePageSetting',
        component: MorePageSettingListComponent,
      },
      {
        path: 'reviewList',
        component: ReviewListComponent,
      },
      {
        path: 'textCloud',
        component: TextCloudComponent,
      },
      {
        // 後台操作紀錄
        path: 'auditLog',
        component: AuditLogComponent,
      },
      {
        // 頁面點擊統計
        path: 'clickLogList',
        component: ClickLogListComponent,
      },
      {
        // 訪客瀏覽紀錄
        path: 'guestViewLog',
        component: GuestViewLogComponent,
      },
      {
        // 會員管理
        path: 'memberManage',
        component: MemberManageListComponent,
      },
      {
        // 新增&編輯會員管理
        path: 'memberManageEdit',
        component: MemberManageEditComponent,
      },
      {
        // 搜尋關鍵字統計
        path: 'searchKeywordLog',
        component: SearchKeywordLogComponent,
      },
      {
        path: 'customerService',
        component: CustomerServiceSettingComponent,
      },
      {
        //捐款項目管理
        path: 'donateItemManage',
        component: DonateItemManageComponent,
      },
      {
        // 銀行明細
        path: 'donateBankDetail',
        component: DonateBankDetailComponent,
      },
      {
        // 銷帳作業
        path: 'donateAccountReconciliation',
        component: DonateAccountReconciliationComponent,
      },
      {
        // 圖表
        path: ':menuItemId/chart',
        component: ChartComponent,
      },
      {
        // 圖表
        path: ':menuItemId/datasetManage',
        component: DatasetManageComponent,
      },
    ],
  },

  { path: 'manage/changePW', component: ChangePwComponent },
  { path: 'manage/forgetPw', component: ForgetPwComponent },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }

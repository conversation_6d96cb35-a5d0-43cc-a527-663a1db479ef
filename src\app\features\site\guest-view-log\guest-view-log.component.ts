import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  getGustViewLogChartReq,
  getGustViewLogChartResp,
  getGustViewLogListReq,
  getGustViewLogListResp,
  guestViewLogItem,
} from '../../../interface/share.interface';
import { format } from 'date-fns';
import { EChartsOption } from 'echarts';

@Component({
  selector: 'app-guest-view-log',
  standalone: false,

  templateUrl: './guest-view-log.component.html',
  styleUrl: './guest-view-log.component.scss',
})
export class GuestViewLogComponent {
  loading: boolean = false;
  menuItemId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  startDate: string = '';
  endDate: string = '';

  guestViewLogList: guestViewLogItem[] = [];
  isTable: boolean = true;
  chartOption: EChartsOption = {};

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) { }

  ngOnInit(): void {
    this.getGustViewLogList();
    this.getGustViewLogChart();
  }

  searchlist() {
    this.nowPage = 1;
    this.getGustViewLogList();
    this.getGustViewLogChart();
  }

  getGustViewLogList() {
    let req: getGustViewLogListReq = {
      webSiteId: sessionStorage.getItem('webSiteId')!,
      dateStart: this.startDate ? format(new Date(this.startDate), 'yyyy-MM-dd') : '',
      dateEnd: this.endDate ? format(new Date(this.endDate), 'yyyy-MM-dd') : '',
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareService.getGustViewLogList(req).subscribe({
      next: (resp: getGustViewLogListResp) => {
        this.loading = false;
        this.guestViewLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  getGustViewLogChart() {
    let req: getGustViewLogChartReq = {
      startTime: this.startDate ? format(new Date(this.startDate), 'yyyy-MM-dd') : '',
      endTime: this.endDate ? format(new Date(this.endDate), 'yyyy-MM-dd') : '',
    };
    this.loading = true;
    this.shareService.getGustViewLogChart(req).subscribe({
      next: (resp: getGustViewLogChartResp) => {
        this.loading = false;
        this.chartOption = this.getOptions(resp.data);
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  getOptions(options: string): EChartsOption {
    return options ? (JSON.parse(options) as EChartsOption) : {};
  }



  resetsearchlist() {
    this.startDate = '';
    this.endDate = '';
    this.nowPage = 1;
    this.getGustViewLogList();
    this.getGustViewLogChart();
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getGustViewLogList();
  }
}

import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  getGustViewLogListReq,
  getGustViewLogListResp,
  guestViewLogItem,
} from '../../../interface/share.interface';

@Component({
  selector: 'app-guest-view-log',
  standalone: false,

  templateUrl: './guest-view-log.component.html',
  styleUrl: './guest-view-log.component.scss',
})
export class GuestViewLogComponent {
  loading: boolean = false;
  menuItemId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  startDate: string = '';
  endDate: string = '';

  guestViewLogList: guestViewLogItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) {}

  ngOnInit(): void {
    this.getGustViewLogList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getGustViewLogList();
  }

  getGustViewLogList() {
    let req: getGustViewLogListReq = {
      webSiteId: sessionStorage.getItem('webSiteId')!,
      dateStart: this.startDate,
      dateEnd: this.endDate,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareService.getGustViewLogList(req).subscribe({
      next: (resp: getGustViewLogListResp) => {
        this.loading = false;
        this.guestViewLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.startDate = '';
    this.endDate = '';
    this.nowPage = 1;
    this.getGustViewLogList();
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getGustViewLogList();
  }
}

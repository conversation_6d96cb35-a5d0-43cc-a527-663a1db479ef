import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UserGroup, UserList } from '../../shared/models/userGroup.model';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';
import { group } from '@angular/animations';

@Injectable({
  providedIn: 'root',
})
export class UserGroupService {
  private userGroupApi: string = '/api/Manage/UserGroup';

  constructor(private http: HttpClient) {}

  /**
   * 取得指定子網站的UserGroup列表
   *
   * @param webSiteId 子網站唯一識別號
   */
  list(webSiteId?: string): Observable<UserGroup[]> {
    let httpParams = new HttpParams();
    if (webSiteId) {
      httpParams = httpParams.set('webSiteId', webSiteId);
    }
    return this.http.get<UserGroup[]>(`${this.userGroupApi}`, {
      params: httpParams,
    });
  }

  createGroup(name: string): Observable<defaultItem> {
    return this.http.post<defaultItem>(`${this.userGroupApi}`, {
      webSiteId: sessionStorage.getItem('webSiteId') as string,
      name: name,
    });
  }

  deleteGroup(id: string) {
    return this.http.delete<any>(`${this.userGroupApi}/${id}`);
  }

  getUserGroupMembers(id: string): Observable<UserList[]> {
    return this.http.get<UserList[]>(`${this.userGroupApi}/${id}/members`);
  }

  updateUserGroupMembers(groupId: string, userIds: string[]) {
    return this.http.put<any>(
      `${this.userGroupApi}/${groupId}/members`,
      userIds
    );
  }
}

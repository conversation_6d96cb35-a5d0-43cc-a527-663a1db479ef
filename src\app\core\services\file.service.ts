import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  FileEntity,
  PagingOfFileEntity,
} from '../../shared/models/pagingOfFileEntity.model';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';
import { getFileDescriptionResp } from '../../interface/file.interface';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  constructor(private http: HttpClient) {}

  /**
   * 取得指定子網站的檔案庫
   *
   * @param webSiteId 子網站唯一識別號
   * @param tags 標籤
   * @param type 類型
   * @param keyword 關鍵字
   * @param skip 起始索引
   * @param take 取得筆數
   */
  getFiles(
    webSiteId: string,

    tags?: string[],

    type?: 'Image' | 'Video' | 'File' | 'Pdf',

    keyword?: string | null,

    skip: number = 0,

    take: number = 10
  ): Observable<PagingOfFileEntity> {
    let url = '/api/Manage/File/{webSiteId}';

    url = url.replace('{webSiteId}', webSiteId.toString());
    const queryList = [];

    if (tags !== null && tags !== undefined) {
      for (const item of tags) {
        if (item) {
          queryList.push('tags=' + encodeURIComponent(item.toString()));
        }
      }
    }

    if (type !== null && type !== undefined) {
      queryList.push('type=' + encodeURIComponent(type.toString()));
    }

    if (keyword !== null && keyword !== undefined) {
      queryList.push('keyword=' + encodeURIComponent(keyword.toString()));
    }

    if (skip !== null && skip !== undefined) {
      queryList.push('skip=' + encodeURIComponent(skip.toString()));
    }

    if (take !== null && take !== undefined) {
      queryList.push('take=' + encodeURIComponent(take.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<PagingOfFileEntity>(url);
  }

  /**
   * 取得檔案庫標籤列表
   *
   * @param webSiteId 子網站唯一識別號
   * @param type 類型
   */
  getTags(
    webSiteId: string,

    type?: 'Image' | 'Video' | 'File' | 'Pdf'
  ): Observable<string[]> {
    let url = '/api/Manage/File/{webSiteId}/tags';

    url = url.replace('{webSiteId}', webSiteId.toString());
    const queryList = [];

    if (type !== null && type !== undefined) {
      queryList.push('type=' + encodeURIComponent(type.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<string[]>(url);
  }

  /**
   * 上傳檔案
   *
   * @param webSiteId 子網站唯一識別號
   * @param tag
   * @param type 類型
   * @param files 檔案
   */
  uploadFiles(
    webSiteId: string,

    tag?: string,
    type?: 'Image' | 'Video' | 'File' | 'Pdf',
    files?: File[]
  ): Observable<FileEntity[]> {
    let url = '/api/Manage/File/{webSiteId}';

    url = url.replace('{webSiteId}', webSiteId.toString());
    const queryList = [];

    if (tag !== null && tag !== undefined) {
      queryList.push('tag=' + encodeURIComponent(tag.toString()));
    }

    if (type !== null && type !== undefined) {
      queryList.push('type=' + encodeURIComponent(type.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    const formData = new FormData();

    if (files) {
      for (var item of files) {
        formData.append('files', item);
      }
    }
    return this.http.post<FileEntity[]>(url, formData);
  }

  deleteFile(id: string) {
    return this.http.delete(`api/Manage/File`, {
      params: {
        id: id,
      },
    });
  }

  uploadFileDescription(
    id: string,
    description: string,
    enDescription: string
  ): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      `api/Manage/File/UpdateFileName`,
      {},
      {
        params: {
          id: id,
          description: description,
          enDescription: enDescription,
        },
      }
    );
  }

  getFileDescription(id: string): Observable<getFileDescriptionResp> {
    return this.http.get<any>(`api/Manage/File/GetFileDescription`, {
      params: {
        id: id,
      },
    });
  }
}

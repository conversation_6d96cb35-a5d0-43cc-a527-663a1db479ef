import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  createOrUpdateTextCloudReq,
  getTextCloudListReq,
  getTextCloudListResp,
} from '../../interface/textCloud.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class TextCloudService {
  constructor(private httpClient: HttpClient) {}

  getTextCloudList(req: getTextCloudListReq): Observable<getTextCloudListResp> {
    return this.httpClient.get<getTextCloudListResp>(
      'api/Manage/WordCloud/GetWordCloudList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  createOrUpdateTextCloud(
    req: createOrUpdateTextCloudReq
  ): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/WordCloud/CreateOrUpdateWordCloud',
      req
    );
  }

  deleteTextCloud(id: string): Observable<defaultItem> {
    return this.httpClient.delete<defaultItem>(
      'api/Manage/WordCloud/DeleteWordCloud',
      {
        params: {
          wordCloudId: id,
        },
      }
    );
  }
}

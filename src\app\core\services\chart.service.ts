import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  createOrUpdateBlockReq,
  getBlockListResp,
  getBlockResp,
  getDatasetListResp,
  getDatasetXListResp,
  getDatasetYListResp,
  updateBlockSortReq,
} from '../../interface/chart.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';
import { ShareService } from './share.service';

@Injectable({
  providedIn: 'root',
})
export class ChartService {
  constructor(
    private httpClient: HttpClient,
    private shareService: ShareService
  ) {}

  /**
   *  取得資料集列表
   * @param req
   * @returns
   */
  getDatasetList(req: {
    currentPage: number;
    pageSize: number;
  }): Observable<getDatasetListResp> {
    return this.httpClient.get<getDatasetListResp>(
      'api/Manage/Chart/GetDatasetList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   * 刪除資料集
   * @param id
   * @returns
   */
  deleteDataset(id: string): Observable<defaultItem> {
    return this.httpClient.delete<defaultItem>(
      'api/Manage/Chart/DeleteDataset',
      {
        params: {
          datasetId: id,
        },
      }
    );
  }

  uploadDataset(file: File): Observable<defaultItem> {
    let formData = new FormData();
    formData.append('file', file);
    return this.httpClient.post<defaultItem>(
      'api/Manage/Chart/UploadDataset',
      formData
    );
  }

  /**
   *  取得資料集X軸列表
   * @param id
   * @returns
   */
  getDatasetXList(id: string): Observable<getDatasetXListResp> {
    return this.httpClient.get<getDatasetXListResp>(
      'api/Manage/Chart/GetDatasetX',
      {
        params: {
          datasetId: id,
        },
      }
    );
  }

  /**
   *  取得資料集Y軸列表
   * @param id
   * @returns
   */
  getDatasetYList(id: string): Observable<getDatasetYListResp> {
    return this.httpClient.get<getDatasetYListResp>(
      'api/Manage/Chart/GetDatasetY',
      {
        params: {
          datasetId: id,
        },
      }
    );
  }

  /**
   *  取得區塊列表
   * @param menuitemId
   * @returns
   */
  getBlockList(menuitemId: string): Observable<getBlockListResp> {
    return this.httpClient.get<getBlockListResp>(
      'api/Manage/Chart/GetBlockList',
      {
        params: {
          menuitemId: menuitemId,
          lang: this.shareService.getLang(),
        },
      }
    );
  }

getBlock(id: string): Observable<getBlockResp> {
    return this.httpClient.get<getBlockResp>('api/Manage/Chart/GetBlock', {
      params: {
        blockId: id,
      },
    });
  }

  /**
   *  新增或更新區塊
   * @param req
   * @returns
   */
  createOrUpdateBlock(req: createOrUpdateBlockReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Chart/CreateOrUpdateBlock',
      req
    );
  }

  /**
   *  修改區塊排序
   * @param req
   * @returns
   */
  updateBlockSort(req: updateBlockSortReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Chart/ModifyBlockSort',
      req
    );
  }

  /**
   *  刪除區塊
   * @param id
   * @returns
   */
  deleteBlock(id: string): Observable<defaultItem> {
    return this.httpClient.delete<defaultItem>('api/Manage/Chart/DeleteBlock', {
      params: {
        blockId: id,
      },
    });
  }
}

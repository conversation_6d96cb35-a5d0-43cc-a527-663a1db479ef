<div class="main">
  <span class="tags">
    <button *ngFor="let tag of tagList" (click)="selectTag(tag)">{{ tag }}</button>
  </span>
  <div class="description-group">
    <mat-form-field appearance="outline">
      <input matInput placeholder="分類名稱" [(ngModel)]="tag" />
    </mat-form-field>
  </div>
  <div class="close-btn">
    <button mat-stroked-button (click)="close()">取消</button>
    <button mat-flat-button color="primary" (click)="submit()" [disabled]="!tag">建立</button>
  </div>
</div>
// 變數定義
$primary-blue: #005a9c;
$border-color: #e0e0e0;

.list-container {
    padding: 10px 20px;
    max-width: 1200px;
    width: 100%;
    align-items: center;
    margin: 0 auto;
}

.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;

    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;

        h1 {
            color: #2eaddb;
        }

        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;
        }
    }
}

.table-container {
    width: 100%;
    overflow-x: auto; // 在超大表格時提供水平滾動條
    background-color: #fff;
    border: 1px solid $border-color;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-table {
    width: 100%;
    border-collapse: collapse;
    th,
    td {
        padding: 12px 15px;
        text-align: left;
        vertical-align: middle;
    }

    // 表格標頭
    thead {
        background-color: $primary-blue;
        color: white;

        th {
            font-weight: 600;
        }
    }

    // 表格內容
    tbody tr {
        border-bottom: 1px solid $border-color;
        &:last-child {
            border-bottom: none;
        }
    }
}

// RWD - 手機版樣式 (寬度小於 768px)
@media screen and (max-width: 768px) {
    .review-table {
        thead {
            display: none; // 隱藏桌面版的標頭
        }

        tr {
            display: block;
            margin-bottom: 15px;
            border: 1px solid $border-color;
            border-radius: 4px;
        }

        td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            text-align: right; // 將內容推到右邊
            border-bottom: 1px solid #f5f5f5;
            &::before {
                content: attr(data-label); // 使用 data-label 作為標籤
                font-weight: bold;
                text-align: left;
                margin-right: 10px;
            }
            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.user-search {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.danger {
    background-color: red !important;
}

<div
  class="header-layout"
  [ngStyle]="{ 'background-color': style?.headerColor }"
>
  <div class="header-logo">
    <img *ngIf="logo" [src]="logo" />
    <app-setting-block
      requireFunctionPolicy="LogoManage"
      [isRight]="false"
      [isLayout]="false"
      [move]="false"
      (setting)="setting()"
    ></app-setting-block>
    <app-loading [loading]="loading"></app-loading>
  </div>
  <div class="navigation">
    <span
      class="name"
      *ngIf="false"
      (click)="goHome('/manage/home')"
      [ngStyle]="{ color: style?.navContentColor }"
      >首頁</span
    >
    <span
      *ngFor="let item of navigation"
      [ngStyle]="{ color: style?.navContentColor }"
    >
      <span
        class="name"
        [ngStyle]="{ color: style?.navContentColor }"
        *ngIf="false"
      >
        {{ item.menuItem?.name }}
      </span>
      <span
        class="name"
        *ngIf="
          item.menuItem?.type === 'Folder' || item.menuItem?.type === 'Tab'
        "
      >
        <span
          [matMenuTriggerFor]="menu"
          [ngStyle]="{ color: style?.navContentColor }"
          >{{ item.menuItem?.name }}</span
        >
        <mat-menu #menu="matMenu">
          <ng-template matMenuContent>
            <div *ngFor="let child of item.menuItem?.inverseParent">
              <div mat-menu-item *ngIf="child.visibility === 'Visable'">
                {{ child.name }}
              </div>
            </div>
          </ng-template>
        </mat-menu>
      </span>
    </span>
  </div>
</div>

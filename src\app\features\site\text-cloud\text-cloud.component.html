<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>文字雲設定-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
            <div>
                <button mat-flat-button (click)="add()">新增</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">名稱</th>
                            <th width="70px">權重</th>
                            <th width="70px">功能</th>
                    </thead>
                    <tbody>
                        @for (item of textCloudList; track item) {
                        <tr>
                            <td data-label="名稱">{{item.text}}</td>
                            <td data-label="權重">{{item.weight}}</td>
                            <td data-label="功能">
                                <button mat-flat-button (click)="edit(item)">編輯</button>&nbsp;
                                <button mat-flat-button class="danger" (click)="delete(item.wordCloudId)">刪除</button>
                            </td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="3" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                    (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
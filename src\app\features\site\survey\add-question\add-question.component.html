<h3 mat-dialog-title class="title-group">
    <span>新增問題</span>
    <span style="margin-left: auto; cursor: pointer;" (click)="close()">
        <mat-icon class="dialog-close-btn">close</mat-icon></span>
</h3>

<form class="Setting-layout" [formGroup]="form" (ngSubmit)="submit(form.value)">
    <div class="contents">
        <span class="block">
            <span class="title">標題</span>
            <input class="input" type="text" formControlName="name">
        </span>
        <span class="block">
            <span class="title">類型</span>
            <mat-select formControlName="type">
                <mat-option [value]="FieldType.Input">文字輸入框</mat-option>
                <mat-option [value]="FieldType.Select">下拉選單</mat-option>
                <mat-option [value]="FieldType.TextArea">文字區域</mat-option>
                <mat-option [value]="FieldType.Date">日期選擇框</mat-option>
                <mat-option [value]="FieldType.Radio">單選選項</mat-option>
                <mat-option [value]="FieldType.CheckBox">多選選項</mat-option>
                <mat-option [value]="FieldType.ImageCheckBox">圖片多選選項</mat-option>
                <mat-option [value]="FieldType.Address">地址</mat-option>
                <mat-option [value]="FieldType.Mail">信箱</mat-option>
                <mat-option [value]="FieldType.Phone">手機號碼</mat-option>
                <mat-option [value]="FieldType.IDnumber">身分證</mat-option>
                <mat-option [value]="FieldType.File">檔案</mat-option>
            </mat-select>
        </span>
        <span class="block row">
            <span class="column">
                <span class="title">必填</span>
                <mat-slide-toggle color="primary" formControlName="required"></mat-slide-toggle>
            </span>
        </span>
    </div>
    <div class="close-btn">
        <button mat-stroked-button mat-dialog-close type="button">取消</button>
        <button mat-flat-button type="submit" [disabled]="!form.valid">
            確定
        </button>
    </div>
</form>
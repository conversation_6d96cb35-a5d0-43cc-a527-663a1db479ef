import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import {
  getImgsListReq,
  getImgsListResp,
} from '../../interface/imgs.interface';

@Injectable({
  providedIn: 'root',
})
export class ImgsService {
  constructor(private httpClient: HttpClient) {}

  getImgsList(req: getImgsListReq): Observable<getImgsListResp> {
    return this.httpClient.get<getImgsListResp>(
      'api/Manage/New_News/GetNew_ImageDataList',
      {
        params: {
          ...req,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }
}

<div id="list-{{ menuItemId }}" class="news-layout news-list-custom-css">
  <img *ngIf="bannerUrl" [src]="bannerUrl" width="100%" />
  <span class="white">
    <h1 *ngIf="title">{{ title }}</h1>
    <div class="add-layout">
      <button class="add" mat-flat-button (click)="addNews()">
        <i class="material-icons">add</i>新增
      </button>
    </div>
    <div class="user-search">
      <div>
        <span>發布區間 :&nbsp;</span>
        <mat-form-field appearance="outline">
          <input matInput [matDatepicker]="pickerStart" [(ngModel)]="keywordStartDate"
            (dateChange)="keywordEndDate = ''" />
          <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
          <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
        <mat-form-field appearance="outline">
          <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="keywordEndDate" [min]="keywordStartDate" />
          <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
          <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
        <span>群組 :&nbsp;</span>
        <mat-form-field appearance="outline">
          <mat-select class="input" [(ngModel)]="userGroupId" placeholder="請選擇群組">
            @for (item of groupList; track item) {
            <mat-option [value]="item.userGroupId">
              {{ item.userGroupName }}
            </mat-option>
            }
          </mat-select>
        </mat-form-field>
        &nbsp;
      </div>
      <div>
        <span>類型 :&nbsp;</span>
        <mat-form-field appearance="outline">
          <mat-select [(ngModel)]="keywordtype">
            @for ( item of typeList; track $index) {
            <mat-option [value]="item.typeValue">{{item.typeName}}</mat-option>
            }
          </mat-select>
        </mat-form-field>
        &nbsp;
        <span>標題 :&nbsp;</span>
        <mat-form-field appearance="outline">
          <input matInput type="text" [(ngModel)]="keywordtitle">
        </mat-form-field>

        &nbsp;
        <button mat-flat-button (click)="searchlist()">搜尋</button>
        &nbsp;
        <button mat-flat-button (click)="resetsearchlist()">清空</button>
      </div>
    </div>
    <div class="contents">
      <section class="block" *ngFor="let item of newsList">
        <span class="titles">
          <img *ngIf="item?.coverUrl" [src]="item.coverUrl" />
        </span>
        <span class="cont">
          <span class="title">
            {{ item.title }}
          </span>
          <span class="time">
            {{item.startTime }}
          </span>
          <span class="description">
            消息狀態 :&nbsp;{{ item.newStatus }}
          </span>
          <span class="description"> 建立者 :&nbsp;{{ item.createUser }} </span>
          <span class="description"> 最後編輯者 :&nbsp;{{ item.editUser }} </span>
          <span style="margin-left: auto">
            <button mat-flat-button (click)="editNews(item.typeGroupId)">
              編輯</button>&nbsp;
            <button mat-flat-button (click)="delete(item.typeGroupId)">刪除</button>&nbsp;
          </span>
        </span>
      </section>
      <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true" (page)="changePage($event)">
      </mat-paginator>
    </div>
  </span>
  <app-loading [loading]="loading"></app-loading>
</div>
<!-- <app-context-menu (closeContextMenu)="closeContextMenu($event)"></app-context-menu> -->
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { ShareService } from '../../../core/services/share.service';
import { defaultItem } from '../../../interface/share.interface';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';

@Component({
  selector: 'app-add-keyword-dialog',
  standalone: false,
  templateUrl: './add-keyword-dialog.component.html',
  styleUrl: './add-keyword-dialog.component.scss',
})
export class AddKeywordDialogComponent {
  loading: boolean = false;
  keyword: string = '';
  constructor(
    public dialogRef: MatDialogRef<DialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { id: string, keyword: string },
    private shareService: ShareService
  ) {
    this.keyword = this.data.keyword;

  }

  ngOnInit(): void {
  }

  submit() {
    this.loading = true;
    this.shareService.setKeyword(this.data.id, this.keyword).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '設定成功', 'success').then(() => {
            this.dialogRef.close();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
    });
  }
}

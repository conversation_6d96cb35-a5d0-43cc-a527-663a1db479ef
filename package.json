{"name": "2025wmg-backstage", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 7777 -o", "build:dev": "ng build --configuration development --aot", "build:prod": "ng build --configuration production --aot", "build:all": "npm run build:dev && npm run build:prod", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.2", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.2", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@aspnet/signalr": "^1.1.4", "angular-froala-wysiwyg": "^4.4.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dompurify": "^3.2.4", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "froala-editor": "^4.4.0", "ngx-clipboard": "^16.0.0", "ngx-countup": "^13.2.0", "ngx-echarts": "^19.0.0", "ngx-hm-carousel": "^3.0.0", "ngx-image-cropper": "^9.0.0", "ngx-loading": "^17.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.14.5", "tslib": "^2.3.0", "uuid": "^11.0.3", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.2", "@angular/cli": "^19.0.2", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.2"}}
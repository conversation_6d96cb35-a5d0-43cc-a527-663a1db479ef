.popup-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 0.5em;
    border-bottom: 1px solid #ccc;

    h2 {
      display: flex;
      margin-right: auto;
    }

    .close-btn {
      cursor: pointer;
    }
  }
}

.menuSet-layout {
  display: flex;
  height: calc(100% - 72px);
  .wifi {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    &:hover{
      color: #fff; /* 改變字體顏色 */
      background-color: rgb(177, 173, 173); /* 改變背景顏色 */
      cursor: pointer; /* 游標變成指針 */
      border-radius: 15px;
    }
  }
  .tree {
    min-width: 400px;
    border-right: 1px solid #ccc;
    position: relative;
    overflow-y: auto;
  }

  .contents {
    width: 100%;
    height: 100%;
    padding: 0 1em;
    position: relative;
    overflow: auto;

    .space-between {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      max-height: 100%;

      .content-blocks {
        .block {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 3;

          .title {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .material-icons {
              color: #2eaddb;
              padding-right: 0.5em;

              &.notEdit {
                color: #ccc;
              }
            }
          }
        }
      }

      .close-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;

        button {
          margin: 0 10px;
        }
      }
    }
  }
}

import { Component } from '@angular/core';
import { WebSiteService } from '../../../../core/services/website.service';

@Component({
  selector: 'app-registration-setting',
  standalone: false,

  templateUrl: './registration-setting.component.html',
  styleUrl: './registration-setting.component.scss'
})
export class RegistrationSettingComponent {
  enable: boolean = false;
  enableLoading: boolean = true;
  requiredLoading: boolean = true;
  fieldList = [
    {
      name: 'E-mail',
      field: 'email',
      checked: false
    },
    {
      name: '電話',
      field: 'phone',
      checked: false
    },
    {
      name: '手機',
      field: 'mobile',
      checked: false
    },
    {
      name: '地址',
      field: 'address',
      checked: false
    },
    {
      name: '生日',
      field: 'birthday',
      checked: false
    },
    {
      name: '性別',
      field: 'gender',
      checked: false
    },
    {
      name: '學歷',
      field: 'education',
      checked: false
    },
    {
      name: '學校',
      field: 'school',
      checked: false
    },
    {
      name: '職稱',
      field: 'jobTitle',
      checked: false
    },
    {
      name: '職業',
      field: 'industry',
      checked: false
    },
    {
      name: '星座',
      field: 'constellation',
      checked: false
    },
    {
      name: '族別',
      field: 'ethnic',
      checked: false
    }
  ];

  constructor(
    private _webSiteService: WebSiteService
  ) {

  }

  ngOnInit(): void {
    this.getRegisterable();
    this.getRegistrationRequired();
  }

  /** 取得子網站註冊啟用狀態 */
  getRegisterable() {
    this._webSiteService.getRegisterable2(sessionStorage.getItem('webSiteId')!).subscribe(res => {
      this.enable = res;
      this.enableLoading = false;
    });
  }

  /** 取得子網站必填欄位 */
  getRegistrationRequired() {
    this._webSiteService.getRegistrationRequired2(sessionStorage.getItem('webSiteId')!).subscribe(res => {
      if (res) {
        this.fieldList.map(x => {
          if (res.includes(x.field)) {
            x.checked = true;
          }
        });
      }
      this.requiredLoading = false;
    });
  }

  /** 更新註冊啟用狀態 */
  updateEnable($event: any) {
    this.enableLoading = true;
    this.enable = $event.checked;
    this._webSiteService.updateRegisterable(sessionStorage.getItem('webSiteId')!, this.enable).subscribe(res => {
      this.enableLoading = false;
    });
  }

  /** 必填欄位變更 */
  changeField(field: any) {
    field.checked = !field.checked;
    this.requiredLoading = true;
    const temp = this.fieldList
      .filter(x => {
        return x.checked;
      })
      .map(y => y.field);
    this._webSiteService.updateRegistrationRequired(sessionStorage.getItem('webSiteId')!, temp).subscribe(res => {
      this.requiredLoading = false;
    });
  }
}

<form
  class="link-edit-layout"
  [formGroup]="form"
  (ngSubmit)="submit(form.value)"
>
  <div class="contents">
    <span class="block">
      <span class="title">網址</span>
      <input type="text" formControlName="url" />
    </span>
    <span class="block">
      <span class="title">標題</span>
      <input type="text" formControlName="name" />
    </span>
    <span class="block row">
      <span class="title">啟用</span>
      <mat-slide-toggle
        color="primary"
        formControlName="enable"
      ></mat-slide-toggle>
    </span>
    <span class="block row" *ngIf="false">
      <span class="title">顯示於首頁</span>
      <mat-slide-toggle
        color="primary"
        formControlName="onHomepage"
      ></mat-slide-toggle>
    </span>
    <span class="block">
      <span class="title">封面圖片&nbsp;※&nbsp;尺寸大小為214px * 86px</span>
      <span class="select-image">
        <button
          mat-flat-button
          color="primary"
          type="button"
          (click)="selectImage()"
        >
          選擇圖片
        </button>
        <span
          *ngIf="imageFile"
          class="preview"
          [ngStyle]="{
            'background-image': 'url(' + imageFile.previewImageUrl + ')'
          }"
        ></span>
      </span>
    </span>
  </div>
  <div class="btns">
    <button mat-stroked-button mat-dialog-close type="button">取消</button>
    <button
      mat-flat-button
      color="primary"
      type="submit"
      [disabled]="!form.valid"
    >
      {{ popupData ? "儲存" : "建立" }}
    </button>
  </div>
  <app-loading [loading]="loading"></app-loading>
</form>

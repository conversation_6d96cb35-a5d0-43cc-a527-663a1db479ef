import { Dialog } from '@angular/cdk/dialog';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';

import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { MatMenuTrigger } from '@angular/material/menu';
import { MorePageSettingContextMenuComponent } from '../more-page-setting-context-menu/more-page-setting-context-menu.component';
import { AppCategoryService } from '../../../../core/services/appCategory.service';
import { AppCategoryInfo } from '../../../../shared/models/appcategory.model';
import { HttpErrorResponse } from '@angular/common/http';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { MorePageSettingEditComponent } from '../more-page-setting-edit/more-page-setting-edit.component';

@Component({
  selector: 'app-more-page-setting-list',
  standalone: false,

  templateUrl: './more-page-setting-list.component.html',
  styleUrl: './more-page-setting-list.component.scss',
})
export class MorePageSettingListComponent {
  contextMenuPositionData!: MouseEvent;
  @ViewChild(MorePageSettingContextMenuComponent)
  contextMenuComponent!: MorePageSettingContextMenuComponent;

  morePageSetting?: AppCategoryInfo[];

  constructor(
    public dialog: MatDialog,
    private _appCategoryService: AppCategoryService
  ) {}

  ngOnInit() {
    this.getCategoryList();
  }

  getCategoryList() {
    this._appCategoryService.getCategoryList().subscribe({
      next: (res) => {
        this.morePageSetting = res;
        //console.log(this.morePageSetting);
      },
      error: (err: HttpErrorResponse) => {
        //console.log(err);
      },
    });
  }

  onContextMenu(event: MouseEvent, item: any) {
    event.preventDefault();
    // this.contextMenu.menu?.focusFirstItem('mouse');
    // console.log('contextMenu', this.contextMenu);
    this.contextMenuComponent.open();
    this.contextMenuPositionData = event;
    this.contextMenuComponent.contextMenuPosition.x = event.clientX + 'px';
    this.contextMenuComponent.contextMenuPosition.y = event.clientY + 'px';
    this.contextMenuComponent.selectedItemData = item;
  }

  closeContextMenu(emitData: { type: string; data: any }) {
    //console.log('output');
    switch (emitData.type) {
      case 'edit':
        //console.log('edit');
        this.edit(emitData.data)
        break;
      case 'delete':
        this.delete(emitData.data);
        break;
    }
  }

  /** 刪除連結 */
  delete(item: any) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this._appCategoryService.deleteCategory(item.categoryId).subscribe((x) => {
          Swal.fire('刪除成功', '', 'success').then(() => {
            this.getCategoryList();
          });
        });
      }
    });
  }

  add() {
    const createMorePageSettingDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        showHeader: true,
        title: '新增',
        contentTemplate: MorePageSettingEditComponent,
      },
    });

    createMorePageSettingDialog.afterClosed().subscribe((name) => {
      if (name) {
        this.getCategoryList();
      }
    });
  }

  edit(data:any) {
    const editMorePageSettingDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        showHeader: true,
        title: '編輯',
        contentTemplate: MorePageSettingEditComponent,
        data: data,
      },
    });
    editMorePageSettingDialog.afterClosed().subscribe((name) => {
      if (name) {
        this.getCategoryList();
      }
    });
  }
}

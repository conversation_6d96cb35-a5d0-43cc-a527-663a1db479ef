import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MemberManageChangePwDialogComponent } from './member-manage-change-pw-dialog.component';

describe('MemberManageChangePwDialogComponent', () => {
  let component: MemberManageChangePwDialogComponent;
  let fixture: ComponentFixture<MemberManageChangePwDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [MemberManageChangePwDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MemberManageChangePwDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

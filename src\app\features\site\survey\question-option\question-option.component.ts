import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { surveyItem } from '../../../../interface/survey.interface';
import { FieldType } from '../../../../enum/survey.enum';

@Component({
  selector: 'app-question-option',
  standalone: false,

  templateUrl: './question-option.component.html',
  styleUrl: './question-option.component.scss',
})
export class QuestionOptionComponent implements OnInit {
  @Input() surveyItem: surveyItem = {} as surveyItem;
  @Input() index: number = 0;

  FieldType = FieldType;
  fieldMeta: { value: string; src?: string }[] = [];

  constructor() {}

  ngOnInit(): void {
    if (this.surveyItem.fieldMeta) {
      this.fieldMeta = JSON.parse(this.surveyItem.fieldMeta);
    }
  }

  getFileUrl(fieldMeta: { value: string; src?: string }): string | null {
    return fieldMeta.src || null;
  }

  today() {
    const today = new Date();
    return today.toISOString().split('T')[0];
  }
}

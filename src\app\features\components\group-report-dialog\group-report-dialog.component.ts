import { Component, Inject, OnInit } from '@angular/core';
import { ENewsletterService } from '../../../core/services/e-newsletter.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  getGroupReportListResp,
  getGroupReportNewsListReq,
  getGroupReportRecruitListReq,
  groupReportItem,
} from '../../../interface/eNewsletter.interface';
import { HttpErrorResponse } from '@angular/common/http';
import { ShareService } from '../../../core/services/share.service';
import { getTypeListResp } from '../../../interface/share.interface';

@Component({
  selector: 'app-group-report-dialog',
  standalone: false,

  templateUrl: './group-report-dialog.component.html',
  styleUrl: './group-report-dialog.component.scss',
})
export class GroupReportDialogComponent implements OnInit {
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  menuType: number = 1;
  typeList: { typeValue: string; typeName: string }[] = [];
  type: string = '';
  keyword: string = '';
  selectAllStatus: boolean = false;
  selectList: { new_NewsId: string; title: string }[] = [];

  groupReportList: groupReportItem[] = [];
  groupReportRecruitStatus: string = '1';

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      data: {
        menuType: number;
      };
    },
    private dialogRef: MatDialogRef<GroupReportDialogComponent>,
    private eNewsLetterService: ENewsletterService,
    private shareService: ShareService
  ) {
    this.menuType = this.data.data.menuType;
    console.log(this.menuType);
  }

  ngOnInit(): void {
    console.log(this.menuType);
    if (this.menuType === 1) {
      this.getTypeList();
    } else {
      this.getGroupReportRecruitList();
    }
  }

  getTypeList() {
    this.shareService.getTypeList('前台最新消息').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
        if (resp.data.length > 0) {
          this.type = resp.data[0].typeValue;
        }
        this.getGroupReportNewsList();
      },
    });
  }

  searchlist(type?: string) {
    if (type) {
      this.type = type;
      this.groupReportRecruitStatus = type;
    }
    this.nowPage = 1;
    if (this.menuType === 1) {
      this.getGroupReportNewsList();
    } else {
      this.getGroupReportRecruitList();
    }
  }

  getGroupReportNewsList() {
    let req: getGroupReportNewsListReq = {
      keyword: this.keyword,
      type: this.type,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: sessionStorage.getItem('lang') || 'zh',
    };
    this.loading = true;
    this.eNewsLetterService.getGroupReportNewsList(req).subscribe({
      next: (resp: getGroupReportListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.groupReportList = resp.data.data;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  getGroupReportRecruitList() {
    let req: getGroupReportRecruitListReq = {
      keyword: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      type: this.groupReportRecruitStatus,
      lang: sessionStorage.getItem('lang') || 'zh',
    };
    this.loading = true;
    this.eNewsLetterService.getGroupReportRecruitList(req).subscribe({
      next: (resp: getGroupReportListResp) => {
        this.loading = false;
        this.totalCount = resp.data.totalCount;
        this.groupReportList = resp.data.data;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  selectAll(event: Event) {
    let target = event.target as HTMLInputElement;
    const checked = target.checked;
    if (checked) {
      this.selectAllStatus = true;
      // 確保只加入當前分頁的項目，且不重複
      this.groupReportList.forEach((item) => {
        // 檢查此項目是否已存在於 selectList 中
        const exists = this.selectList.some(
          (selectedItem) => selectedItem.new_NewsId === item.new_NewsId
        );

        if (!exists) {
          this.selectList.push({
            new_NewsId: item.new_NewsId,
            title: item.title,
          });
        }
      });
    } else {
      this.selectAllStatus = false;
      const currentReviewGroupIds = this.groupReportList.map(
        (item) => item.new_NewsId
      );
      this.selectList = this.selectList.filter(
        (item) => !currentReviewGroupIds.includes(item.new_NewsId)
      );
    }
  }

  changeSelect(event: Event, item: groupReportItem) {
    this.selectAllStatus = false;
    let target = event.target as HTMLInputElement;
    const checked = target.checked;
    if (checked) {
      this.selectList.push({
        new_NewsId: item.new_NewsId,
        title: item.title,
      });
    } else {
      this.selectList = this.selectList.filter(
        (selectListItem) => selectListItem.new_NewsId !== item.new_NewsId
      );
    }
  }

  isSelected(item: groupReportItem): boolean {
    return this.selectList.some((x) => x.new_NewsId === item.new_NewsId);
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    if (this.menuType === 1) {
      this.getGroupReportNewsList();
    } else {
      this.getGroupReportRecruitList();
    }
  }

  cancel() {
    this.dialogRef.close();
  }

  send() {
    this.dialogRef.close(this.selectList);
  }
}

@use "./scss/chart.scss";

.list-container {
    padding: 10px 20px;
    width: 95%;
    align-items: center;
    margin: 0 auto;
}

.news-layout {
    display: flex;
    justify-items: center;
    flex-direction: column;
    position: relative;

    .white {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin: 10px;
        background-color: #ffffff;
        padding: 10px;

        h1 {
            color: #2eaddb;
        }

        .contents {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1024px;
            border-top: 1px solid #ccc;
        }
    }
}

.add-block-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 10px 0;
    span {
        margin-left: 10px;
        font-size: 1.5em;
        color: #3f51b5;
        transition: 0.3s ease-in-out;
        border-radius: 5px;
        padding: 10px;
        background-color: #b8d3f3;
        cursor: pointer;
        &:hover {
            background-color: #3f51b5;
            color: #fff;
        }
    }
}
.main {
  position: relative;

  .input {
    text-align: center;
    margin: 1em 0;
    display: flex;
    flex-direction: column;
  }

  .close-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px;
  }
}

.toggle-block{
    text-align: left;
    margin-bottom: 15px ;
}
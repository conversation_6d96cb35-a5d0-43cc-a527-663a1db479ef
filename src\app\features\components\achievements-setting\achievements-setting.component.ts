import { Component } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { AchievementsService } from '../../../core/services/achievements.service';
import {
  getAchievementsResp,
  updateAchievementsReq,
} from '../../../interface/achievements.interface';
import { ShareService } from '../../../core/services/share.service';
import { defaultItem } from '../../../interface/share.interface';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-achievements-setting',
  standalone: false,

  templateUrl: './achievements-setting.component.html',
  styleUrl: './achievements-setting.component.scss',
})
export class AchievementsSettingComponent {
  form!: FormGroup;
  loading: boolean = false;
  constructor(
    private _fb: FormBuilder,
    public dialogRef: MatDialogRef<DialogComponent>,
    private achievementsService: AchievementsService,
    private shareService: ShareService
  ) {
    this.form = this._fb.group({
      forumEventCount: [],
      klokahReachCount: [],
      instructionalKit: [],
    });
  }

  ngOnInit(): void {
    this.getAchievements();
  }

  getAchievements() {
    this.loading = true;
    this.achievementsService.getAchievements().subscribe({
      next: (resp: getAchievementsResp) => {
        this.loading = false;
        resp.code === 200
          ? this.form.patchValue({
              forumEventCount: resp.data.forumEventCount,
              klokahReachCount: resp.data.klokahReachCount,
              instructionalKit: resp.data.instructionalKit,
            })
          : Swal.fire('錯誤', resp.message, 'error');
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', error.error.message, 'error');
      },
    });
  }

  submit() {
    let req: updateAchievementsReq = {
      forumEventCount: this.form.value.forumEventCount,
      klokahReachCount: this.form.value.klokahReachCount,
      instructionalKit: this.form.value.instructionalKit,
      lang: this.shareService.getLang(),
    };
    this.loading = true;
    this.achievementsService.updateAchievements(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        resp.code === 200
          ? Swal.fire('成功', `送出成功`, 'success').then(() => {
              this.dialogRef.close();
            })
          : Swal.fire('錯誤', resp.message, 'error');
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('錯誤', error.error.message, 'error');
      },
    });
  }
}

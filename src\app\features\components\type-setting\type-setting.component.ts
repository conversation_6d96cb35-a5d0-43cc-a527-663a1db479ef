import { Component, Inject } from '@angular/core';
import { MenuItemService } from '../../../core/services/menuItem.service';
import { UserDataService } from '../../../core/services/userData.service';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import {
  typeMenuItem,
  typeSettingDialog,
} from '../../../shared/models/dialog.model';
import { CreateMenuNameComponent } from '../create-menu-name/create-menu-name.component';
import { getHaveMenuResp, MenuItem } from '../../../shared/models/menuItem.model';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { TypeList } from '../../../enum/share.enum';

@Component({
  selector: 'app-type-setting',
  standalone: false,

  templateUrl: './type-setting.component.html',
  styleUrl: './type-setting.component.scss',
})
export class TypeSettingComponent {
  loading: boolean = false;
  menuItem: typeMenuItem[] = TypeList;
  tagList: { name: string; selected: boolean }[] = [];

  get filtedMenuItem() {
    const tags = this.tagList.filter((x) => x.selected).map((x) => x.name);
    if (tags.length === 0) {
      return this.menuItem;
    }
    return this.menuItem.filter((x) => tags.includes(x.tag));
  }
  /** 只能唯一的選單 */
  // onlyOneMenuItem = ['Feedback', 'Registration'];
  submitBtnDisable = false;
  dialogData!: MenuItem;

  constructor(
    private _menuItemService: MenuItemService,
    private _userDataService: UserDataService,
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<TypeSettingComponent>,
    @Inject(MAT_DIALOG_DATA) public data: typeSettingDialog
  ) { }

  ngOnInit(): void {
    this.defaultMenu();

  }

  defaultMenu() {
    this._menuItemService.getHaveMenu().subscribe({
      next: (res: getHaveMenuResp) => {
        if (res.data.length > 0) {
          let without: string[] = ["DonateFolder", 'News', "FastMenu", "Recruit"];
          const withoutTypes = res.data.filter((x: string) => { return without.includes(x) });
          this.menuItem = this.menuItem.filter((x) => {
            return !withoutTypes.includes(x.type);
          });
        }
        this.dialogData = this.data.dataGroup;
        if (this.dialogData) {
          if (this.dialogData.type === 'Folder') {
            // 目錄暫定為兩層
            const withoutTypes = [''];
            this.menuItem = this.menuItem.filter((x) => {
              return !withoutTypes.includes(x.type);
            });
          } else if (this.dialogData.type === 'Tab') {
            // 頁籤裡不能有目錄及頁籤
            const withoutTypes = [
              'Folder',
              'Tab',
              'HyperLink',
              'Line',
              'Img',
              'News',
              "Recruit",
              "ENewsletter",
              "DonateFolder",
              "FastMenu",
              "Chart",
            ];
            this.menuItem = this.menuItem.filter((x) => {
              return !withoutTypes.includes(x.type);
            });
          }
        }
      },
    })
  }


  selectTag(item: { name: string; selected: boolean }) {
    item.selected = !item.selected;
  }

  submit() {
    const selected = this.menuItem.find((res) => res.selected)!;

    const createMenuNameDialog = this.dialog.open(DialogComponent, {
      data: {
        title: '輸入選單名稱',
        showHeader: true,
        width: '30%',
        contentTemplate: CreateMenuNameComponent,
        dataGroup: {
          selectedData: selected,
          method: 'add',
          parentId: this.dialogData ? this.dialogData.id : null,
        },
      },
    });

    createMenuNameDialog.afterClosed().subscribe(() => {
      // 更新選單權限
      this.updateFunctionPolicy();
      this.updateMenuPolicy();
    });
  }

  select(item: any) {
    this.menuItem.map((res) => {
      res.selected = false;
      return res;
    });
    item.selected = true;
    this.submitBtnDisable = true;
  }

  /** 取得目前的角色功能權限列表 */
  updateFunctionPolicy() {
    this._userDataService.getUserFunctionPolicy().subscribe((res: any) => {
      sessionStorage.setItem('functionPolicy', res);
    });
  }

  /** 取得目前的角色選單權限列表，如為null則表示為系統管理員 */
  updateMenuPolicy() {
    this._userDataService.getUserMenuPolicy().subscribe((res: any) => {
      sessionStorage.setItem('menuPolicy', res);
    });
  }
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  addOrUpdateDonateAccountReconciliationReq,
  addDonateItemReq,
  getDonateAccountReconciliationListReq,
  getDonateAccountReconciliationListResp,
  getDonateItemListReq,
  getDonateItemListResp,
  updateDonateItemReq,
  getDonateAccountReconciliationResp,
  exportDonateAccountReconciliationListReq,
  setDonateAccountReconciliationSortReq,
} from '../../interface/donate.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class DonateService {
  constructor(private http: HttpClient) {}

  getDonateItemList(
    req: getDonateItemListReq
  ): Observable<getDonateItemListResp> {
    return this.http.post<getDonateItemListResp>(
      'api/Manage/Donate/GetDonateItemDataList',
      req
    );
  }

  addDonateItem(req: addDonateItemReq): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      'api/Manage/Donate/InsertDonateItem',
      req
    );
  }

  updateDonateItem(req: updateDonateItemReq): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      '/api/Manage/Donate/updateDonateItem',
      req
    );
  }

  deleteDonateItem(id: string): Observable<defaultItem> {
    return this.http.delete<defaultItem>('api/Manage/Donate/DeleteDonateItem', {
      params: {
        id: id,
      },
    });
  }

  /**
   *  取得銷帳作業列表
   */
  getDonateAccountReconciliationList(
    req: getDonateAccountReconciliationListReq
  ): Observable<getDonateAccountReconciliationListResp> {
    return this.http.post<getDonateAccountReconciliationListResp>(
      'api/Manage/Donate/DonateInfoListBackstage',
      req
    );
  }

  /**
   *  取得銷帳作業資料
   * @param id
   * @returns
   */
  getDonateAccountReconciliation(
    id: string
  ): Observable<getDonateAccountReconciliationResp> {
    return this.http.get<getDonateAccountReconciliationResp>(
      'api/Manage/Donate/GetDonateDataBackstage',
      {
        params: {
          donateDataId: id,
        },
      }
    );
  }

  /**
   *  新增銷帳作業
   * @param req
   * @returns
   */
  addOrUpdateDonateAccountReconciliation(
    req: addOrUpdateDonateAccountReconciliationReq
  ): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      'api/Manage/Donate/InsertOrUpdateDonateInfoBackstage',
      req
    );
  }

  /**
   *  刪除銷帳作業
   * @param id
   * @returns
   */
  deleteDonateAccountReconciliation(id: string): Observable<defaultItem> {
    return this.http.delete<defaultItem>('api/Manage/Donate/DeleteDonateData', {
      params: {
        donateDataId: id,
      },
    });
  }

  importDonateAccountReconciliationList(file: File): Observable<defaultItem> {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post<defaultItem>(
      'api/Manage/Donate/ImportDonateDataExcel',
      formData
    );
  }

  exportDonateAccountReconciliationList(
    req: exportDonateAccountReconciliationListReq
  ): Observable<any> {
    return this.http.post('api/Manage/Donate/ExportDonateDataExcel', req, {
      responseType: 'blob', // 告訴 HttpClient 要 blob
      observe: 'response', // 取得完整 HttpResponse 以抓 headers
    });
  }

  isCheckDonateAccountReconciliation(id: string): Observable<defaultItem> {
    return this.http.put<defaultItem>(
      'api/Manage/Donate/UpdateDonateDataIsCheck',null,
      {
        params: {
          donateDataId: id,
        },
      }
    );
  }

  isPublishDonateAccountReconciliation(id: string): Observable<defaultItem> {
    return this.http.put<defaultItem>(
      'api/Manage/Donate/UpdateDonateDataIsShow',null,
      {
        params: {
          donateDataId: id,
        },
      }
    );
  }

  /**
   * 新增銷帳作業排序
   * @param req 
   * @returns 
   */
  setDonateAccountReconciliationSort(req: setDonateAccountReconciliationSortReq): Observable<defaultItem> {
    return this.http.post<defaultItem>(
      'api/Manage/Donate/UpdateNewsSort',
      req
    );
  }
}

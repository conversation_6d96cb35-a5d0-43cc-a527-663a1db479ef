import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ChartDialogComponent } from './dialog/chart-dialog/chart-dialog.component';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { ActivatedRoute, Router } from '@angular/router';
import { ChartService } from '../../../core/services/chart.service';
import {
  blockListItem,
  getBlockListResp,
  updateBlockSortReq,
} from '../../../interface/chart.interface';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../interface/share.interface';
import { EChartsOption } from 'echarts';
import { AddChartKeywordComponent } from './add-chart-keyword/add-chart-keyword.component';

@Component({
  selector: 'app-chart',
  standalone: false,

  templateUrl: './chart.component.html',
  styleUrl: './chart.component.scss',
})
export class ChartComponent {
  menuItemId: string = '';
  keyword: string = '';
  startID: string = '';
  dataSource: blockListItem[] = [];
  constructor(
    private matDialog: MatDialog,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private chartService: ChartService,
    public dialog: MatDialog,
  ) {
    this.activatedRoute.url.subscribe(() => {
      this.menuItemId = this.activatedRoute.snapshot.params['menuItemId'];
      this.getBlockList();
    });
  }

  ngOnInit() { }

  getBlockList() {
    this.chartService.getBlockList(this.menuItemId).subscribe({
      next: (resp: getBlockListResp) => {
        this.dataSource = resp.data;
        this.dataSource.forEach((item) => {
          item.dataset = this.getOptions(item.options);
        });
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  getOptions(options: string): EChartsOption {
    return options ? (JSON.parse(options) as EChartsOption) : {};
  }

  addChart() {
    console.log(this.menuItemId);
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '85%',
          contentTemplate: ChartDialogComponent,
          showHeader: true,
          title: '新增',
          data: {
            menuitemId: this.menuItemId,
          },
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getBlockList();
      });
  }

  editBlock(item: blockListItem) {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '85%',
          contentTemplate: ChartDialogComponent,
          showHeader: true,
          title: '編輯',
          data: { data: item, menuitemId: this.menuItemId },
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getBlockList();
      });
  }

  removeBlock(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.chartService.deleteBlock(id).subscribe({
          next: (resp: defaultItem) => {
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                this.getBlockList();
              })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  addKeyword() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '500px',
        contentTemplate: AddChartKeywordComponent,
        showHeader: true,
        keyword: this.keyword,
        title: '新增關鍵字',
      },
    });
  }

  datasetManage() {
    this.router.navigate([`/manage/${this.menuItemId}/datasetManage`]);
  }

  // #region 區塊內容處理
  getCols() {
    const screenWidth = window.innerWidth;
    // 根据需要的屏幕宽度阈值来判断
    if (screenWidth < 1200 && screenWidth > 800) {
      return 2;
    }
    if (screenWidth < 800) {
      return 1;
    }
    return 4;
  }

  getColspan(colspan: number) {
    const screenWidth = window.innerWidth;

    if (screenWidth < 1200 && screenWidth > 800) {
      return colspan / 2;
    }
    if (screenWidth < 800) {
      return 1;
    }
    return colspan;
  }

  // #endregion

  // #region 圖表拖曳
  /**
   * 拖曳起始
   * @param id unber
   */
  dropStart(id: string) {
    this.startID = id;
  }

  /**
   * 拖曳結束
   * @param id number
   */
  drop(id: string) {
    if (this.startID !== id) {
      const startID = this.dataSource!.findIndex(
        (item) => item.blockId === this.startID
      );
      const endID = this.dataSource!.findIndex((item) => item.blockId === id);
      if (startID !== -1 && endID !== -1) {
        const data = this.dataSource[startID];
        this.dataSource.splice(startID, 1);
        this.dataSource.splice(endID, 0, data);
        this.changeBlockPostion({
          menuitemId: this.menuItemId,
          firstBlockId: this.startID,
          secondBlockId: id,
        });
      }
    }
  }

  changeBlockPostion(request: updateBlockSortReq) {
    this.chartService.updateBlockSort(request).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code !== 200) {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }
  // #endregion
}

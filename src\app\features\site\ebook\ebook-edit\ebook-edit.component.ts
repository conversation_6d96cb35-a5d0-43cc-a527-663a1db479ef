import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../../../components/file-box/file-box.component';
import Swal from 'sweetalert2';
import { EbookService } from '../../../../core/services/ebook.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  addEbookReq,
  createUpdateEbookResp,
  getEbookResp,
  updateEbookReq,
} from '../../../../interface/ebook.interface';
import { ShareService } from '../../../../core/services/share.service';
import {
  createUpdateResp,
  defaultItem,
} from '../../../../interface/share.interface';
import { format, toZonedTime } from 'date-fns-tz';
import { ApprovalStatus, SAVESTATUS } from '../../../../enum/share.enum';

@Component({
  selector: 'app-ebook-edit',
  standalone: false,

  templateUrl: './ebook-edit.component.html',
  styleUrl: './ebook-edit.component.scss',
})
export class EbookEditComponent {
  loading: boolean = false;
  title: string = '';
  author: string = '';
  introduction: string = '';
  outline: string = '';
  littleTitle: string = '';
  littleTitleZh: string = '';
  period: string = '';

  status: string = '新增';
  menuItemId: string = '';
  bookId: string = '';
  typeGroupId: string = '';

  startTime: string = '';
  isEnd: boolean = false;
  endTime: string = '';
  isTop: boolean = false;

  cover: string = '';
  coverId: string = '';

  file: File | null = null;
  fileName: string = '';
  ebookLink: string = '';

  createUser: string = '';
  editUser: string = '';
  levelDecision: number = 2;
  isPendingApproval: boolean = false;
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';
  saveStatus = SAVESTATUS

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private ebookService: EbookService,
    private shareService: ShareService
  ) { }

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id') && queryParams.get('typeGroupId')) {
        this.typeGroupId = queryParams.get('typeGroupId')!;
        this.bookId = queryParams.get('id')!;
        this.status = '編輯';
        this.getEbook();
      }
    });
  }

  getEbook() {
    this.loading = true;
    this.ebookService.getEbook(this.bookId).subscribe({
      next: (resp: getEbookResp) => {
        this.loading = false;
        this.title = resp.data.title;
        this.author = resp.data.author;
        this.introduction = resp.data.introduction;
        this.outline = resp.data.outline;
        this.littleTitle = resp.data.littleTitle;
        this.littleTitleZh = resp.data.littleTitleZh;
        this.period = resp.data.period;
        this.startTime = resp.data.startTime;
        this.endTime = resp.data.endTime;
        this.isEnd = !!this.endTime;
        this.isTop = resp.data.isTop;
        this.cover = resp.data.coverUrl;
        this.coverId = resp.data.coverDataId;
        this.ebookLink = resp.data.previewUrl;
        this.createUser = resp.data.creater;
        this.editUser = resp.data.editor;
        this.levelDecision = resp.data.levelDecision;
        this.reason = resp.data.reason;
        this.reviewFileName = resp.data.reviewFileName;
        this.reviewFileUrl = resp.data.reviewFileUrl;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  get maxStartTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.endTime, 'HH:mm');
    }
    return null
  }

  get minEndTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.startTime, 'HH:mm');
    }
    return null
  }

  changeIsEnd(newValue: boolean) {
    if (newValue === false) {
      this.endTime = '';
    }
  }

  selectGalleryImage() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.cover = resp.data.previewImageUrl;
          this.coverId = resp.data.previewImageDataId;
        }
      });
  }

  selectGalleryFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    // const maxFileSize = 30 * 1024 * 1024;
    // if (file.size > maxFileSize) {
    //   Swal.fire('檔案大小超過 30MB，請重新選擇', '', 'warning');
    //   this.loading = false;
    //   return;
    // }
    // if (
    //   file.type !== 'application/zip' &&
    //   file.type !== 'application/x-zip-compressed'
    // ) {
    //   Swal.fire('請選擇aio', '', 'warning');
    //   this.loading = false;
    //   return;
    // }
    this.file = file;
    this.ebookLink = '';
    this.fileName = file.name;
    this.loading = false;
  }

  cancel() {
    history.back();
  }

  save(status: SAVESTATUS) {
    if (!this.bookId) {
      this.add(status);
    } else {
      this.update(status);
    }
  }

  add(status: SAVESTATUS) {
    if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
      Swal.fire('警告', `請選擇正確的時間`, 'warning');
      return;
    }
    if (!this.file) {
      Swal.fire('請選擇檔案', '', 'warning');
      return;
    }
    if (!this.title) {
      Swal.fire('請填寫標題', '', 'warning');
      return;
    }
    if (!this.author) {
      Swal.fire('請填寫作者', '', 'warning');
      return;
    }

    let req: addEbookReq = {
      menuItemId: this.menuItemId,
      title: this.title,
      author: this.author,
      introduction: this.introduction,
      outline: this.outline,
      littleTitle: this.littleTitle,
      littleTitleZh: this.littleTitleZh,
      period: this.period,
      coverDataId: this.coverId,
      isTop: this.isTop,
      startTime: this.startTime
        ? format(
          toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      endTime: this.endTime
        ? format(
          toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      FromFile: this.file,
      lang: this.shareService.getLang(),
      levelDecision: this.levelDecision,
    };
    this.loading = true;
    this.ebookService.addEbook(req).subscribe({
      next: (resp: createUpdateEbookResp) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', `建立成功`, 'success').then(() => {
            if (status === SAVESTATUS.SAVELEAVE) {
              this.router.navigate([`/manage/${this.menuItemId}/ebook/list`]);
            } else {
              this.bookId = resp.data.bookId;
              this.typeGroupId = resp.data.typeGroupId;
              this.status = '編輯';
              this.router.navigate([], {
                relativeTo: this.activatedRoute,
                queryParams: {
                  id: this.bookId,
                  typeGroupId: this.typeGroupId
                },
                replaceUrl: true
              });
            }
          });
        } else {
          Swal.fire('失敗', `${resp.message}`, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  update(status: SAVESTATUS) {
    if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
      Swal.fire('警告', `請選擇正確的時間`, 'warning');
      return;
    }
    if (!this.title) {
      Swal.fire('請填寫標題', '', 'warning');
      return;
    }
    if (!this.author) {
      Swal.fire('請填寫作者', '', 'warning');
      return;
    }
    let req: updateEbookReq = {
      id: this.bookId,
      menuItemId: this.menuItemId,
      typeGroupId: this.typeGroupId,
      title: this.title,
      author: this.author,
      introduction: this.introduction,
      outline: this.outline,
      littleTitle: this.littleTitle,
      littleTitleZh: this.littleTitleZh,
      period: this.period,
      coverDataId: this.coverId,
      isTop: this.isTop,
      startTime: this.startTime
        ? format(
          toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      endTime: this.endTime
        ? format(
          toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      FromFile: this.file,
      lang: this.shareService.getLang(),
      levelDecision: this.levelDecision,
    };
    this.loading = true;
    this.ebookService.updateEbook(req).subscribe({
      next: (resp: createUpdateEbookResp) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', `更新成功`, 'success').then(() => {
            if (status === SAVESTATUS.SAVELEAVE) {
              this.router.navigate([`/manage/${this.menuItemId}/ebook/list`]);
            } else {
              this.bookId = resp.data.bookId;
              this.typeGroupId = resp.data.typeGroupId;
              this.status = '編輯';
              this.router.navigate([], {
                relativeTo: this.activatedRoute,
                queryParams: {
                  id: this.bookId,
                  typeGroupId: this.typeGroupId
                },
                replaceUrl: true
              });
            }
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/viewEbook'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          bookId: this.bookId,
          type: 'Ebook',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    if (this.bookId) {
      if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
        Swal.fire('警告', `請選擇正確的時間`, 'warning');
        return;
      }
      if (!this.title) {
        Swal.fire('請填寫標題', '', 'warning');
        return;
      }
      if (!this.author) {
        Swal.fire('請填寫作者', '', 'warning');
        return;
      }
      let req: updateEbookReq = {
        id: this.bookId,
        menuItemId: this.menuItemId,
        typeGroupId: this.typeGroupId,
        title: this.title,
        author: this.author,
        introduction: this.introduction,
        outline: this.outline,
        littleTitle: this.littleTitle,
        littleTitleZh: this.littleTitleZh,
        period: this.period,
        coverDataId: this.coverId,
        isTop: this.isTop,
        startTime: this.startTime
          ? format(
            toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
            "yyyy-MM-dd'T'HH:mm:ss",
            { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
          )
          : '',
        endTime: this.endTime
          ? format(
            toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
            "yyyy-MM-dd'T'HH:mm:ss",
            { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
          )
          : '',
        FromFile: this.file,
        lang: this.shareService.getLang(),
        levelDecision: this.levelDecision,
      };
      this.ebookService.updateEbook(req).subscribe({
        next: (resp: createUpdateEbookResp) => {
          if (resp.code === 200) {
            this.router.navigate(['manage/viewEbook'], {
              queryParams: {
                menuItemId: this.menuItemId,
                typeGroupId: resp.data.typeGroupId,
                bookId: resp.data.bookId,
                type: 'Ebook',
                status: ApprovalStatus.BeforeApproval,
              },
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
    } else {
      if (!this.file) {
        Swal.fire('請選擇檔案', '', 'warning');
        return;
      }
      if (!this.title) {
        Swal.fire('請填寫標題', '', 'warning');
        return;
      }
      if (!this.author) {
        Swal.fire('請填寫作者', '', 'warning');
        return;
      }
      let req: addEbookReq = {
        menuItemId: this.menuItemId,
        title: this.title,
        author: this.author,
        introduction: this.introduction,
        outline: this.outline,
        littleTitle: this.littleTitle,
        littleTitleZh: this.littleTitleZh,
        period: this.period,
        coverDataId: this.coverId,
        isTop: this.isTop,
        startTime: this.startTime
          ? format(
            toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
            "yyyy-MM-dd'T'HH:mm:ss",
            { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
          )
          : '',
        endTime: this.endTime
          ? format(
            toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
            "yyyy-MM-dd'T'HH:mm:ss",
            { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
          )
          : '',
        FromFile: this.file,
        lang: this.shareService.getLang(),
        levelDecision: this.levelDecision,
      };
      this.loading = true;
      this.ebookService.addEbook(req).subscribe({
        next: (resp: createUpdateEbookResp) => {
          this.loading = false;
          if (resp.code === 200) {
            this.router.navigate(['manage/viewEbook'], {
              queryParams: {
                menuItemId: this.menuItemId,
                typeGroupId: resp.data.typeGroupId,
                bookId: resp.data.bookId,
                type: 'Ebook',
                status: ApprovalStatus.BeforeApproval,
              },
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
    }
  }
}

.add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 0;
    background-color: rgba(255, 134, 30, 0.5);
    cursor: pointer;
    transition: 0.3s ease-in-out;

    &:hover {
        background-color: rgba(255, 134, 30, 0.8);
    }
}

input {
    width: 98%;
    font-size: 0.9em;
    line-height: 1.8;
    outline: 0;
    background-color: #fff;
    border: 1px solid #949494;
    padding: 8px 10px;
    border-radius: 5px;
}

.select {
    width: 98%;
    font-size: 0.9em;
    line-height: 1.8;
    outline: 0;
    background-color: #fff;
    border: 1px solid #949494;
    padding: 8px 10px;
    border-radius: 5px;
}

option {
    padding: 8px 10px;
    line-height: 1.8;
    font-size: 1em;
    background-color: #fff;
    color: #364250;
}

textarea {
    width: 98%;
    font-size: 0.9em;
    line-height: 1.8;
    outline: 0;
    background-color: #fff;
    border: 1px solid #949494;
    padding: 8px 10px;
    border-radius: 5px;
    resize: none;
    min-height: 80px;
}

.image-option-block-group {
    display: flex;
    flex-direction: row;
    padding: 15px 0;
    border-bottom: 1px solid #949494;
    .image-option-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;
        img {
            height: 60px;
        }
    }
}

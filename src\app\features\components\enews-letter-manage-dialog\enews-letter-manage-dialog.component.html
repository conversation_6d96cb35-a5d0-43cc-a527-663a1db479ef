@if(sendStatus===1){
<div class="contents">
    <div class="block">
        <div class="title-ctn">
            <span class="title">電子報名稱</span>
        </div>
        {{name}}
    </div>
    <div class="block">
        <div class="title-ctn">
            <span class="title">發送對象</span>
        </div>
        <div>
            <button mat-flat-button (click)="choseUser()"> 選擇</button>
        </div>
        <div class="group-layout">
            @for (item of userList; track item) {
            <div class="group-item" (click)="deleteUser(item)">
                <span>{{item.email}}</span>
                <i class="material-icons">close</i>
            </div>
            }
            @for (item of groupList; track item) {
            <div class="group-item" (click)="deleteGroup(item)">
                <span>{{item.name}}</span>
                <i class="material-icons">close</i>
            </div>
            }
        </div>
    </div>
    <div class="block">
        <div class="title-ctn">
            <span class="title">發布時間</span>
        </div>
        <div style="display: flex;align-items: center;">
            <mat-radio-group [(ngModel)]="sendType" (change)="sendDateTime='';selectedHour=0;selectedMinute=0">
                <mat-radio-button [value]="false">立即發送</mat-radio-button>
                <mat-radio-button [value]="true">排程發送</mat-radio-button>
            </mat-radio-group>
            @if(sendType){
            <div class="time-group-layout">
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerStart" [(ngModel)]="sendDateTime" [min]="today" readonly />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker>
                </mat-form-field>
                <div class="time-layout">
                    <span>時</span>
                    <mat-form-field appearance="outline">
                        <select matNativeControl required [(ngModel)]="selectedHour">
                            @for (item of hourList; track item) {
                            <option [value]="item">{{item}}</option>
                            }
                        </select>
                    </mat-form-field>
                </div>
                <div class="time-layout">
                    <span>分</span>
                    <mat-form-field appearance="outline">
                        <select matNativeControl required [(ngModel)]="selectedMinute">
                            @for (item of minuteList; track item) {
                            <option [value]="item">{{item}}</option>
                            }
                        </select>
                    </mat-form-field>
                </div>
            </div>
            }
        </div>
    </div>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">取消</button>
        <button mat-flat-button (click)="send()">送出</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
}@else{
<div class="contents">
    <div class="block">
        <div class="list-container">
            <div class="user-search">
                <div>
                    <span>關鍵字 :&nbsp;</span>
                    <mat-form-field appearance="outline">
                        <input matInput type="text" [(ngModel)]="keyword">
                    </mat-form-field> &nbsp; &nbsp;
                    <button mat-flat-button (click)="searchSendingList()">搜尋</button>
                </div>
            </div>
        </div>
        <div class="table-container">
            <div class="tabs-container">
                @for (item of sendingObjectList; track item) {
                <div class="tab" [class.active]="item === sendingObject"
                    (click)="searchSendingList(item);keyword='';selectAllStatus=false">
                    {{ item }}
                </div>
                }
            </div>
            @if(sendingObject === '個別'){
            <table class="review-table">
                <thead>
                    <tr>
                        <th width="60px"><input type="checkbox" id="select-all" (change)="selectAll($event)"
                                [checked]="selectAllStatus">全選</th>
                        <th>電子信箱</th>
                    </tr>
                </thead>
                <tbody>
                    @for (item of sendingObjectUserList; track item) {
                    <tr>
                        <td data-label="選取"><input type="checkbox" (change)="changeSelect($event,item)"
                                [checked]="isSelected(item)">
                        </td>
                        <td data-label="電子信箱">{{item.email}}</td>
                    </tr>
                    }@empty {
                    <tr>
                        <td colspan="3" style="text-align: center;">查無資料</td>
                    </tr>
                    }
                </tbody>
            </table>
            }@else {
            <table class="review-table">
                <thead>
                    <tr>
                        <th width="60px"><input type="checkbox" id="select-all" (change)="selectAll($event)"
                                [checked]="selectAllStatus">全選</th>
                        <th>類別會員</th>
                    </tr>
                </thead>
                <tbody>
                    @for (item of sendingObjectGroupList; track item) {
                    <tr>
                        <td data-label="選取"><input type="checkbox" (change)="changeSelect($event,item)"
                                [checked]="isSelected(item)">
                        </td>
                        <td data-label="類別會員">{{item.name}}</td>
                    </tr>
                    }@empty {
                    <tr>
                        <td colspan="3" style="text-align: center;">查無資料</td>
                    </tr>
                    }
                </tbody>
            </table>
            }
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </div>
    <div class="btn-group">
        <button mat-flat-button (click)="back()">取消</button>
        <button mat-flat-button (click)="add()">送出</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
}
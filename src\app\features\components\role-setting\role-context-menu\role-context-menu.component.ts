import { Component, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'app-role-context-menu',
  standalone: false,

  templateUrl: './role-context-menu.component.html',
  styleUrl: './role-context-menu.component.scss'
})
export class RoleContextMenuComponent {
  contextMenuPosition = { x: '0px', y: '0px' };
  // @Input() positionData: MouseEvent | null = null;
  @ViewChild(MatMenuTrigger) contextMenu!: MatMenuTrigger;
  selectedItemData: any;
  @Output() closeContextMenu = new EventEmitter();

  ngOnInit(): void {
    // this.contextMenuPosition.x = this.positionData?.clientX + 'px';
    // this.contextMenuPosition.y = this.positionData?.clientY + 'px';
  }

  // ngOnChanges(changes: SimpleChanges): void {
  //   console.log('changes', changes);
  //   // ngOnChanges is called whenever the input property changes
  //   if (changes['positionData']) {
  //     this.contextMenuPosition.x = this.positionData?.clientX + 'px';
  //     this.contextMenuPosition.y = this.positionData?.clientY + 'px';
  //     console.log('Data received or updated:', changes['data'].currentValue);
  //   }
  // }

  open() {
    this.contextMenu.openMenu();
  }

  edit() {
    this.closeContextMenu.emit({
      type: 'edit',
      data: this.selectedItemData
    });
  }

  menuRole() {
    this.closeContextMenu.emit({
      type: 'menu',
      data: this.selectedItemData
    });
  }

  functionRole() {
    this.closeContextMenu.emit({
      type: 'function',
      data: this.selectedItemData
    });
  }

  delete() {
    this.closeContextMenu.emit({
      type: 'delete',
      data: this.selectedItemData
    });
  }
}

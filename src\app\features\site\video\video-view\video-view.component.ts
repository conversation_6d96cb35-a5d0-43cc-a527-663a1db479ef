import { Component } from '@angular/core';
import { VideoService } from '../../../../core/services/video.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  SafeUrl,
  DomSanitizer,
  SafeResourceUrl,
} from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { ReviewService } from '../../../../core/services/review.service';
import { ApprovalStatus } from '../../../../enum/share.enum';
import { getEbookResp } from '../../../../interface/ebook.interface';
import { approvalReq } from '../../../../interface/review.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { getVideoResp } from '../../../../interface/video.interface';

@Component({
  selector: 'app-video-view',
  standalone: false,

  templateUrl: './video-view.component.html',
  styleUrl: './video-view.component.scss',
})
export class VideoViewComponent {
  loading: boolean = false;
  menuItemId: string = ''; //選單id
  typeGroupId: string = ''; //內容id
  type: string = ''; //選單類別
  status: ApprovalStatus = ApprovalStatus.BeforeApproval; //審核狀態 送審前or送審後
  videoUrl: SafeResourceUrl = '';
  approval: boolean = true;
  reason: string = '';
  ApprovalStatus = ApprovalStatus;
  openStatus: boolean = true;

  file: File | null = null;
  fileName: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private videoService: VideoService,
    private reviewService: ReviewService,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.menuItemId = queryParams.get('menuItemId') as string;
      this.typeGroupId = queryParams.get('typeGroupId') as string;
      this.type = queryParams.get('type') as string;
      this.status = Number(queryParams.get('status')) as ApprovalStatus;
    });
  }

  ngOnInit() {
    if (this.status === ApprovalStatus.EndApproval) {
      this.getVideo();
    } else {
      this.getVideoForView();
    }
  }

  getPolicy(policy: string) {
    const functionPolicies = sessionStorage.getItem('functionPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(policy) > -1) {
      return true;
    }
    return false;
  }
  getVideo() {
    this.videoService.getVideoAfterReviewForView(this.typeGroupId).subscribe({
      next: (resp: getVideoResp) => {
        if (resp.code === 200) {
          const url = new URL(resp.data.youtubeUrl);
          const videoId = url.searchParams.get('v');
          const embedUrl = 'https://www.youtube.com/embed/' + videoId;
          this.videoUrl =
            this.sanitizer.bypassSecurityTrustResourceUrl(embedUrl);
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  getVideoForView() {
    this.videoService.getVideoForView(this.typeGroupId).subscribe({
      next: (resp: getVideoResp) => {
        if (resp.code === 200) {
          const url = new URL(resp.data.youtubeUrl);
          const videoId = url.searchParams.get('v');
          const embedUrl = 'https://www.youtube.com/embed/' + videoId;
          this.videoUrl =
            this.sanitizer.bypassSecurityTrustResourceUrl(embedUrl);
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  cancel() {
    history.back();
  }

  submitForReview() {
    this.loading = true;
    this.reviewService
      .submitForReview({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '送審完成', 'success').then(() => {
              this.router.navigate([`/manage/${this.menuItemId}/video/list`]);
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }

  selectGalleryFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    // const maxFileSize = 3 * 1024 * 1024;
    // if (file.size > maxFileSize) {
    //   Swal.fire('檔案大小超過 3MB，請重新選擇', '', 'warning');
    //   this.loading = false;
    //   return;
    // }
    if (
      file.type !== 'image/jpeg' &&
      file.type !== 'image/png' &&
      file.type !== 'application/x-zip-compressed' &&
      file.type !== 'application/pdf'
    ) {
      Swal.fire('請選擇jpg或png或zip檔案', '', 'warning');
      this.loading = false;
      return;
    }
    this.file = file;

    if (file.name) {
      this.loading = false;
    }
    this.fileName = file.name;
  }

  send() {
    this.loading = true;
    let req: approvalReq = {
      typeGroupId: this.typeGroupId,
      approval: this.approval,
      menuItemType: this.type,
      reason: this.reason,
      file: this.file,
    };
    this.reviewService.approval(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '審核完成', 'success').then(() => {
            history.back();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  submitForPublish() {
    this.loading = true;
    this.reviewService
      .publish({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '發布完成', 'success').then(() => {
              this.router.navigate([`/manage/${this.menuItemId}/video/list`]);
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ShareService } from './share.service';
import {
  getAchievementsResp,
  updateAchievementsReq,
} from '../../interface/achievements.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class AchievementsService {
  constructor(
    private httpClient: HttpClient,
    private shareService: ShareService
  ) {}

  getAchievements(): Observable<getAchievementsResp> {
    return this.httpClient.get<getAchievementsResp>(
      'api/Manage/Achievements/GetAchievements',
      {
        params: {
          lang: this.shareService.getLang(),
        },
      }
    );
  }

  updateAchievements(req: updateAchievementsReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/Achievements/UpdateAchievements',
      req
    );
  }
}

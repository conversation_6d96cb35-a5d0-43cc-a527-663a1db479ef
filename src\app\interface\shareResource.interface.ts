import { defaultItem } from './share.interface';

export interface getShareResourceListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
}

export interface getShareResourceListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: shareResourceItem[];
  };
}
export interface shareResourceItem {
  sportType: string;
  name: string;
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  shareResourceId: string;
}

export interface createUpdateShareResourceReq {
  shareResourceId?: string;
  menuItemId: string;
  type?: string;
  name: string;
  fileId: string;
  lang: string;
}

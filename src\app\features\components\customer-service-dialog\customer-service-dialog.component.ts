import { Component, Inject } from '@angular/core';
import {
  addCustomerServiceReq,
  customerServiceItem,
} from '../../../interface/chat.interface';
import { ChatService } from '../../../core/services/chat.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { ShareService } from '../../../core/services/share.service';
import { defaultItem } from '../../../interface/share.interface';

@Component({
  selector: 'app-customer-service-dialog',
  standalone: false,

  templateUrl: './customer-service-dialog.component.html',
  styleUrl: './customer-service-dialog.component.scss',
})
export class CustomerServiceDialogComponent {
  text: string = '';
  aiQuestionId: string = '';
  aiQuestion: string = '';
  aiAnswer: string = '';
  enable: boolean = true;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { data: customerServiceItem },
    private dialogRef: MatDialogRef<CustomerServiceDialogComponent>,
    private shareService: ShareService,
    private chatService: ChatService
  ) {
    if (this.data.data && this.data.data.id) {
      this.aiQuestionId = this.data.data.id;
      this.aiQuestion = this.data.data.question;
      this.aiAnswer = this.data.data.answer;
      this.enable = this.data.data.enable;
    }
  }

  ngOnInit() {}

  submit() {
    if (this.aiQuestion && this.aiAnswer) {
      let status: string = '新增';
      let req: addCustomerServiceReq = {
        aiQuestion: this.aiQuestion,
        aiAnswer: this.aiAnswer,
        enable: this.enable,
      };
      if (this.aiQuestionId) {
        status = '編輯';
        req.aiQuestionId = this.aiQuestionId;
      }

      this.chatService.addCustomerService(req).subscribe({
        next: (resp: defaultItem) => {
          resp.code === 200
            ? Swal.fire('成功', `${status}成功`, 'success').then(() => {
                this.dialogRef.close();
              })
            : Swal.fire('錯誤', resp.message, 'error');
        },
      });
    } else {
      Swal.fire('警告', '請輸入問題和AI回答', 'warning');
    }
  }

  close() {
    this.dialogRef.close();
  }
}

import { EDITORTYPE } from '../enum/editor.enum';

export interface contentData {
  id: string;
  type: EDITORTYPE;
  data?:
    | contentItem
    | fileDataItem
    | titleItem
    | paragraphTitleItem
    | photoItem
    | photoListItem
    | videoItem
    | addressItem
    | buttonItem
    | HtmlDataItem
    | cardItem
    | tableItem
    | sportTableItem
    | palaSportTableItem
    | InboundTransferTableItem
    | PlaceTransferTableItem
    | PdfItem;
}

export interface contentItem {
  content: string;
}
export interface fileDataItem {
  filedatalist: {
    fileName: string;
    fileId: string;
    linkUrl: string;
    fileType: string;
  }[];
}

export interface fileData {
  fileName: string;
  linkUrl: string;
}

export interface titleItem {
  title: string;
}
export interface paragraphTitleItem {
  title: string;
}
export interface photoItem {
  fileUrl: string;
}
export interface photoListItem {
  photoDatas: { fileUrl: string }[];
}
export interface videoItem {
  type: string;
  fileUrl: string;
}
export interface addressItem {
  address: string;
}
export interface buttonItem {
  url: string;
  title: string;
}
export interface HtmlDataItem {
  htmlString: string;
}
export interface cardItem {
  cardUrl: string;
  cardUrlName: string;
  cardName: string;
}
export interface tableItem {
  filedatalist: { fileName: string; fileId: string }[];
}

export interface sportTableItem {
  filedatalist: { fileName: string; fileId: string }[];
}

export interface palaSportTableItem {
  filedatalist: { fileName: string; fileId: string }[];
}

export interface InboundTransferTableItem {}

export interface PlaceTransferTableItem {}

export interface PdfItem {
  pdfName: string;
  pdfDataUrl: string;
}

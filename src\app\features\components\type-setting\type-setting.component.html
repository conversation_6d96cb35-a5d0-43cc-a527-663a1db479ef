<div class="main">
  <!-- <h2>
    選擇樣式
    <i *ngIf="false" class="material-icons">help_outline</i>
  </h2> -->
  <div class="tagsBar" *ngIf="false">
    <span class="tags">
      <button
        *ngFor="let tag of tagList"
        [ngClass]="{ selected: tag.selected }"
        (click)="selectTag(tag)"
      >
        {{ tag.name }}
      </button>
    </span>
  </div>
  <div class="type-layout">
    <ng-container *ngFor="let item of filtedMenuItem">
      <section
        class="block"
        [ngClass]="{ selected: item.selected }"
        (click)="select(item)"
      >
        <img [src]="item.url" />
        <p>{{ item.name }}</p>
      </section>
    </ng-container>
  </div>
  <div class="close-btn">
    <button mat-stroked-button mat-dialog-close>取消</button>
    <button
      mat-flat-button
      color="primary"
      (click)="submit()"
      [disabled]="!submitBtnDisable"
      mat-dialog-close
    >
      選擇
    </button>
  </div>
  ,
  <app-loading [loading]="loading"></app-loading>
</div>

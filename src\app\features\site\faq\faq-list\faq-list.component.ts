import { Component, HostListener } from '@angular/core';
import { FaqService } from '../../../../core/services/faq.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { lineItem } from '../../../../interface/line.interface';
import {
  faqItem,
  getFaqListReq,
  getFaqListResp,
} from '../../../../interface/faq.interface';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-faq-list',
  standalone: false,

  templateUrl: './faq-list.component.html',
  styleUrl: './faq-list.component.scss',
})
export class FaqListComponent {
  loading: boolean = false;
  menuItemId: string = '';

  title: string = '';
  keyword: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  faqList: faqItem[] = [];

  constructor(
    private faqService: FaqService,
    private _route: ActivatedRoute,
    private _router: Router
  ) {}

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getFaqList();
    });

  }

  getFaqList() {
    let req: getFaqListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.faqService.getFaqList(req).subscribe({
      next: (resp: getFaqListResp) => {
        this.title = resp.data.title;
        this.faqList = resp.data.data;
        this.totalCount = resp.data.totalCount;
        this.loading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  addFaq() {
    this._router.navigate([`/manage/${this.menuItemId}/faq/edit`]);
  }

  editFaq(id: string) {
    this._router.navigate([`/manage/${this.menuItemId}/faq/edit`], {
      queryParams: { id: id },
    });
  }

  /** 刪除消息 */
  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.faqService.deleteFaq(id).subscribe({
          next: (res) => {
            this.loading = false;
            Swal.fire({
              title: '刪除成功',
              icon: 'success',
              showCancelButton: false,
              reverseButtons: true,
            }).then(() => {
              this.getFaqList();
            });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire({
              title: '刪除失敗',
              icon: 'error',
              showCancelButton: false,
              reverseButtons: true,
            });
          },
        });
      }
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getFaqList();
  }
}

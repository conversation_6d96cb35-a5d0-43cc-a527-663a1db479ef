<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>後台操作紀錄-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <span>日期 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startDate" (dateChange)="endDate = ''" />
                    <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                    <mat-datepicker #pickerStart></mat-datepicker> </mat-form-field>&nbsp;~&nbsp;
                <mat-form-field appearance="outline">
                    <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endDate" [min]="startDate" />
                    <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                    <mat-datepicker #pickerEnd></mat-datepicker> </mat-form-field>&nbsp;
                <span>名稱 :&nbsp;</span>
                <mat-form-field appearance="outline">
                    <input matInput type="text" [(ngModel)]="keyword">
                </mat-form-field> &nbsp; &nbsp;
                <button mat-flat-button (click)="searchlist()">搜尋</button>
                &nbsp;
                <button mat-flat-button (click)="resetsearchlist()">清空</button>
            </div>
        </div>
        <div class="contents">
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="100px">項次</th>
                            <th width="70px">使用者</th>
                            <th width="70px">位置</th>
                            <th width="70px">標題</th>
                            <th width="70px">動作</th>
                            <th width="70px">時間</th>
                    </thead>
                    <tbody>
                        @for (item of auditLogList;let index=$index ;track item) {
                        <tr>
                            <td data-label="項次">{{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}</td>
                            <td data-label="使用者">{{item.operatorAccount}}</td>
                            <td data-label="位置">{{item.operatorIp}}</td>
                            <td data-label="標題">{{item.description}}</td>
                            <td data-label="動作">{{item.type}}</td>
                            <td data-label="時間">{{item.creationTime|date:'yyyy/MM/dd'}}</td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="6" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize"
                    [hidePageSize]="true" (page)="changePage($event)">
                </mat-paginator>
            </div>
        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
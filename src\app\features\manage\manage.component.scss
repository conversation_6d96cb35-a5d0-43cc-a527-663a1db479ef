.manage-layout {
  position: relative;
  width: 100%;
  min-height: 100vh;

  .manage-header-layout {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 60px;
    background-color: #242424;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.75);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;

    .logo {
      padding: 0 10px;
    }

    .manage-menu {
      list-style: none;
      display: flex;
      align-items: center;

      li {
        color: #ffffff;
        padding: 0 20px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &.border-r {
          border-right: 1px solid #696969;
        }

        &.user {
          cursor: unset;
        }

        .material-icons {
          font-size: 30px;
        }

        span {
          padding-left: 5px;
        }

        &:hover {
          color: #ff861e;
        }
      }
    }
  }

  .manage-contents {
    margin-top: 60px;
    position: relative;

    .manage-main {
      // background-color: #ffffff;
      // margin: 10px;
    }
  }

  .goTop {
    position: fixed;
    bottom: 1em;
    right: 1em;
    background-color: #ccc;
    z-index: 100;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    box-shadow: 0 0 3px #585858;
    transition: 0.3s ease-in-out;

    &:hover {
      box-shadow: 0px 0px 9px 0px #585858;
    }
  }
}

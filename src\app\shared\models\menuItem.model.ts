import { defaultItem } from "../../interface/share.interface";

export interface createMenuItemReq {
  webSiteId?: string;
  type?: string;
  name?: string;
  englishName?: string;
  amisName: string;
  atayaiName: string;
  paiwanName: string;
  bununName: string;
  puyumaName: string;
  rukaiName: string;
  tsouName: string;
  saisiyatName: string;
  taoName: string;
  thaoName: string;
  kavalanName: string;
  trukuName: string;
  sakizayaName: string;
  seediqName: string;
  saaroaName: string;
  kanakanavuName: string;
  visibility?: string;
  parentId?: string;
  menuItem_R_Type: any[];
}

export interface MenuItem {
  id: string;
  parentId?: string;
  webSiteId?: string;
  name?: string;
  englishName?: string;
  amisName: string;
  atayaiName: string;
  paiwanName: string;
  bununName: string;
  puyumaName: string;
  rukaiName: string;
  tsouName: string;
  saisiyatName: string;
  taoName: string;
  thaoName: string;
  kavalanName: string;
  trukuName: string;
  sakizayaName: string;
  seediqName: string;
  saaroaName: string;
  kanakanavuName: string;
  type?: string;
  meta?: string;
  sort?: number;
  visibility?: string;
  onSiteMap?: boolean;
  moreItemText?: string;
  iconDataId?: string;
  linkMenuItemId?: string;
  hiddenTitle?: boolean;
  hiddenPageTitle?: boolean;
  bannerLdataId?: string;
  bannerMdataId?: string;
  bannerSdataId?: string;
  useChildrenBanner?: boolean;
  path?: string[];
  iconUrl?: string;
  bannerLUrl?: string;
  bannerMUrl?: string;
  bannerSUrl?: string;
  editable?: boolean;
  inverseParent?: MenuItem[];
  menuItem_R_Type: any[];
}

export interface getHaveMenuResp extends defaultItem {
  data: string[];
}

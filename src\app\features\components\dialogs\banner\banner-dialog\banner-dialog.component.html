<div class="auth-layout">
    <button class="add" mat-flat-button (click)="addBanner()">
        <i class="material-icons">add</i>新增
    </button>
    @for (item of bannerList; track $index) {
    <div style="border: solid gray;margin: 1em;border-radius: 10px;padding: 10px;">
        <div class="img-box">
            <div class="img">
                <span>PC:</span>
                <img [src]="item.pcUrl" [alt]="item.bannerContent" width="500">
            </div>
            <div class="img">
                <span>Mobile :</span>
                @if(item.mobileURL){

                <img [src]="item.mobileURL" [alt]="item.bannerContent" width="376">
                }
            </div>
        </div>

        <div class="btn-group">
            <button mat-flat-button (click)="delete(item.id)">刪除</button>
            <button mat-flat-button (click)="edit(item)">修改</button>
        </div>

    </div>
    }



    <app-loading [loading]="loading"></app-loading>
</div>
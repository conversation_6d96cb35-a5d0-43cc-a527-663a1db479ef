<div class="auth-layout">
    <div class="block">
        <div class="toggle-block">
            <mat-slide-toggle [(ngModel)]="bannerEnable" (change)="changeEnable()">
                <mat-label>啟用Banner</mat-label>
            </mat-slide-toggle>
        </div>
    </div>
    <button class="add" mat-flat-button (click)="addBanner()">
        <i class="material-icons">add</i>新增
    </button>
    @for (item of bannerList; track $index) {
    <div style="border: solid gray;margin: 1em;border-radius: 10px;padding: 10px;">
        <div class="img-box">
            @if(item.type === 'Image'){
            <div class="img">
                <img [src]="item.url" [alt]="item.title" width="500">
            </div>
            }@else {
            <video width="500" [src]="item.url" controls muted></video>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="delete(item.id)">刪除</button>
            <button mat-flat-button (click)="edit(item)">修改</button>
        </div>
    </div>
    }
    <app-loading [loading]="loading"></app-loading>
</div>
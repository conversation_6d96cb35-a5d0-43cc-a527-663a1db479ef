import { DialogRef } from '@angular/cdk/dialog';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { AddQuestionComponent } from './add-question/add-question.component';
import {
  getSurveyResp,
  saveSurveyReq,
  saveSurveyResp,
  surveyItem,
} from '../../../interface/survey.interface';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { EditQuestionComponent } from './edit-question/edit-question.component';
import { SurveyService } from '../../../core/services/survey.service';
import { ApiCode, ApprovalStatus } from '../../../enum/share.enum';
import Swal from 'sweetalert2';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ShareService } from '../../../core/services/share.service';
import { format, toZonedTime } from 'date-fns-tz';

@Component({
  selector: 'app-survey',
  standalone: false,

  templateUrl: './survey.component.html',
  styleUrl: './survey.component.scss',
})
export class SurveyComponent {
  loading: boolean = false;
  menuItemId: string = '';
  content: string = '';
  keyword: string = '';
  levelDecision: number = 2;
  surveyItem: surveyItem[] = [];
  typeGroupId: string = '';
  isPendingApproval: boolean = false;
  createUser: string = '';
  editUser: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';
  startTime: string = '';
  endTime: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private matDialog: MatDialog,
    private surveyService: SurveyService,
    private shareService: ShareService
  ) {
    this.activatedRoute.url.subscribe(() => {
      this.menuItemId = this.activatedRoute.snapshot.params['menuItemId'];
      this.getSurveyList();
    });
  }

  getSurveyList() {
    this.loading = true;
    this.surveyService
      .getSurvey({
        menuitemId: this.menuItemId,
        lang: sessionStorage.getItem('lang') || 'zh',
      })
      .subscribe({
        next: (resp: getSurveyResp) => {
          this.loading = false;
          if (resp.code === ApiCode.SUCCESS) {
            this.levelDecision = resp.data.levelDecision;
            this.startTime = resp.data.startTime;
            this.endTime = resp.data.endTime;
            this.typeGroupId = resp.data.typeGroupId;
            this.keyword = resp.data.keyword;
            this.isPendingApproval = resp.data.isPendingApproval;
            this.surveyItem = resp.data.surveyField || [];
            this.createUser = resp.data.creater;
            this.editUser = resp.data.editor;
            this.reason = resp.data.reason;
            this.reviewFileName = resp.data.reviewFileName;
            this.reviewFileUrl = resp.data.reviewFileUrl;
          }
        },
      });
  }

  get maxStartTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.endTime, 'HH:mm');
    }
    return null
  }

  get minEndTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.startTime, 'HH:mm');
    }
    return null
  }


  create() {
    this.matDialog
      .open(AddQuestionComponent, {
        width: '500px',
        maxHeight: '90vh',
        disableClose: true,
      })
      .afterClosed()
      .subscribe((surveyItem: surveyItem) => {
        if (surveyItem) {
          this.surveyItem.push(surveyItem);
        }
      });
  }

  edit(index: number) {
    this.matDialog
      .open(EditQuestionComponent, {
        width: '500px',
        maxHeight: '90vh',
        disableClose: true,
        data: this.surveyItem[index],
      })
      .afterClosed()
      .subscribe((surveyItem: surveyItem) => {
        if (surveyItem) {
          this.surveyItem[index] = surveyItem;
        }
      });
  }

  /** 拖拉結束 */
  drop($event: CdkDragDrop<surveyItem[]>) {
    moveItemInArray(this.surveyItem, $event.previousIndex, $event.currentIndex);
  }

  remove(index: number) {
    this.surveyItem.splice(index, 1);
  }

  saveSurvey() {
    if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
      Swal.fire('警告', `請選擇正確的時間`, 'warning');
      return;
    }
    let req: saveSurveyReq = {
      menuitemId: this.menuItemId,
      startTime: this.startTime
        ? format(
          toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      endTime: this.endTime
        ? format(
          toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      keyword: this.keyword,
      levelDecision: this.levelDecision,
      surveyField: this.surveyItem,
      lang: sessionStorage.getItem('lang') || 'zh',
    };
    this.surveyService.saveSurvey(req).subscribe({
      next: (resp: saveSurveyResp) => {
        resp.code === ApiCode.SUCCESS
          ? Swal.fire('成功', '儲存成功', 'success')
          : Swal.fire('失敗', '儲存失敗', 'error');
      },
      error: () => { },
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'Survey',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
      Swal.fire('警告', `請選擇正確的時間`, 'warning');
      return;
    }
    let req: saveSurveyReq = {
      menuitemId: this.menuItemId,
      startTime: this.startTime
        ? format(
          toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      endTime: this.endTime
        ? format(
          toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      keyword: this.keyword,
      levelDecision: this.levelDecision,
      surveyField: this.surveyItem,
      lang: sessionStorage.getItem('lang') || 'zh',
    };
    this.surveyService.saveSurvey(req).subscribe({
      next: (resp: saveSurveyResp) => {
        resp.code === ApiCode.SUCCESS
          ? this.router.navigate(['manage/view'], {
            queryParams: {
              menuItemId: this.menuItemId,
              typeGroupId: resp.data,
              type: 'Survey',
              status: ApprovalStatus.BeforeApproval,
            },
          })
          : Swal.fire('失敗', resp.message, 'error');
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  export() {
    this.loading = true;
    this.surveyService.exportSurvey(this.menuItemId).subscribe({
      next: (resp: HttpResponse<Blob>) => {
        this.loading = false;
        if (!resp.body) {
          Swal.fire('失敗', '沒有檔案可匯出', 'error');
          return;
        }
        // 使用新服務來取得檔名
        const filename = this.shareService.getFilenameFromHeaders(resp);

        const url = window.URL.createObjectURL(resp.body); // resp.body 已確認非 null
        const a = document.createElement('a');
        a.href = url;
        if (filename) {
          a.download = filename;
        }
        a.click();
        window.URL.revokeObjectURL(url);
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }
}

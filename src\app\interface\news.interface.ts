import {
  contentItem,
  fileDataItem,
  titleItem,
  paragraphTitleItem,
  photoItem,
  photoListItem,
  videoItem,
  addressItem,
  buttonItem,
  HtmlDataItem,
  cardItem,
  tableItem,
} from './editor.interface';
import { defaultItem } from './share.interface';

export interface getNewsListReq {
  menuItemId: string;
  currentPage: number;
  pageSize: number;
  startTime: string;
  endTime: string;
  userGroupId: string;
  type: string;
  keyword: string;
}

export interface getNewsListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: newsItem[];
  };
}
export interface newsItem {
  new_NewsId: string;
  typeGroupId: string;
  title: string;
  coverUrl: string;
  startTime: string;
  newStatus: string;
  createUser: string;
  editUser: string;
  sort: number;
  isTop: boolean;
}

export interface getNewsResp extends defaultItem {
  data: {
    menuitemId: string;
    new_NewsId: string;
    typeGroupId: string;
    userGroupId: string;
    isPendingApproval: boolean; //審核中
    publishDateTime: string; //是否有發布版及當前發布版發布時間null為無
    levelDecision: number; ///層決數
    titleName: string;
    type: string;
    sportType: string;
    startTime: string;
    endTime: string;
    enable: boolean;
    isTop: boolean;
    coverDataId: string;
    coverUrl: string;
    createUser: string;
    createTime: string;
    editUser: string;
    editTime: string;
    new_NewsTagDatas: new_NewsTagDataItem[];
    description: string;
    reason: string
    reviewFileName: string
    reviewFileUrl: string
  };
}

export interface new_NewsTagDataItem {
  tagId: string;
  tagName: string;
  tagData:
  | contentItem
  | fileDataItem
  | titleItem
  | paragraphTitleItem
  | photoItem
  | photoListItem
  | videoItem
  | addressItem
  | buttonItem
  | HtmlDataItem
  | cardItem
  | tableItem;
}
export interface createUpdateNewsReq {
  menuitemId: string;
  typeGroupId?: string;
  titleName: string;
  type?: string;
  startTime: string;
  endTime: string;
  isTop: boolean;
  coverDataId?: string;
  levelDecision: number; //層決數 2, 1
  userGroupId?: string; //群組Id
  new_NewsTagDatas: {
    tagName: string;
    dataString: string | null;
  }[];
  lang: string;
  isNews: boolean;
  description?: string;
}

export interface getNewsUserGroupResp extends defaultItem {
  data: {
    userGroupId: string;
    userGroupName: string;
  }[];
}

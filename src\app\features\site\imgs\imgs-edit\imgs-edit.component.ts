import { HttpErrorResponse } from '@angular/common/http';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { NewsService } from '../../../../core/services/news.service';
import { EDITORTYPE } from '../../../../enum/editor.enum';
import { contentData } from '../../../../interface/editor.interface';
import {
  getNewsResp,
  createUpdateNewsReq,
} from '../../../../interface/news.interface';
import {
  createUpdateResp,
  defaultItem,
} from '../../../../interface/share.interface';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { ContentEditorComponent } from '../../../components/content-editor/content-editor.component';
import { FileBoxComponent } from '../../../components/file-box/file-box.component';
import { v4 as uuidv4 } from 'uuid';
import { format, toZonedTime } from 'date-fns-tz';
import { ApprovalStatus, SAVESTATUS } from '../../../../enum/share.enum';

@Component({
  selector: 'app-imgs-edit',
  standalone: false,

  templateUrl: './imgs-edit.component.html',
  styleUrl: './imgs-edit.component.scss',
})
export class ImgsEditComponent implements OnInit, AfterViewInit {
  @ViewChild(ContentEditorComponent)
  ContentEditorComponent: ContentEditorComponent | undefined;

  menuItemId: string = '';
  contentData: contentData[] = [];
  typeGroupId: string = '';
  title: string = '';
  keyword: string = '';
  description: string = '';
  startTime: string = '';
  isEnd: boolean = false;
  endTime: string = '';
  isTop: boolean = false;
  createUser: string = '';
  editUser: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  cover: string = '';
  coverId: string = '';
  levelDecision: number = 2;
  isPendingApproval: boolean = false;
  saveStatus = SAVESTATUS

  constructor(
    private newsService: NewsService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.menuItemId = this.activatedRoute.parent?.snapshot.params['menuItemId'];
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams.get('id')) {
        const newTypeGroupId = queryParams.get('id')!;
        if (this.typeGroupId !== newTypeGroupId) {
          this.typeGroupId = newTypeGroupId;
          this.getNews();
        }
      }
    });
  }

  ngAfterViewInit(): void {
    if (!this.typeGroupId && this.ContentEditorComponent) {
      setTimeout(() => {
        this.ContentEditorComponent!.addEditorBlock('content');
      });
    }
  }

  getNews() {
    this.newsService.getNews(this.typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.title = resp.data.titleName;
        this.keyword = resp.data.keyword;
        this.description = resp.data.description;
        this.startTime = resp.data.startTime;
        this.isEnd = resp.data.endTime ? true : false;
        this.endTime = resp.data.endTime;
        this.isTop = resp.data.isTop;
        this.coverId = resp.data.coverDataId;
        this.cover = resp.data.coverUrl;
        this.editUser = resp.data.editUser;
        this.createUser = resp.data.createUser;
        this.reason = resp.data.reason;
        this.reviewFileName = resp.data.reviewFileName;
        this.reviewFileUrl = resp.data.reviewFileUrl;
        this.levelDecision = resp.data.levelDecision;
        this.isPendingApproval = resp.data.isPendingApproval;
        resp.data.new_NewsTagDatas.map((item) => {
          this.contentData.push({
            id: uuidv4(),
            type: item.tagName as EDITORTYPE,
            data: item.tagData,
          });
        });
        if (resp.data.new_NewsTagDatas.length > 0) {
          if (this.ContentEditorComponent) {
            this.ContentEditorComponent.initializeForm(this.contentData);
          }
        } else {
          this.ContentEditorComponent!.addEditorBlock('content');
        }
      },
      error: (err: HttpErrorResponse) => { },
    });
  }

  get maxStartTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.endTime, 'HH:mm');
    }
    return null
  }

  get minEndTime(): string | null {
    let startTime = this.startTime ? format(this.startTime, 'yyyy-MM-dd') : null;
    let endTime = this.endTime ? format(this.endTime, 'yyyy-MM-dd') : null;
    if (startTime && endTime && startTime === endTime) {
      return format(this.startTime, 'HH:mm');
    }
    return null
  }

  getContentData(contentData: contentData[]) {
    this.contentData = contentData;
  }

  changeIsEnd(newValue: boolean) {
    if (newValue === false) {
      this.endTime = '';
    }
  }

  selectGalleryImage() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.cover = resp.data.previewImageUrl;
          this.coverId = resp.data.previewImageDataId;
        }
      });
  }
  cancel() {
    history.back();
    // this.router.navigate([`/manage/${this.menuItemId}/img/list`]);
  }
  save(status: SAVESTATUS) {
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
    }).then((result) => {
      if (result.isConfirmed) {
        if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
          Swal.fire('警告', `請選擇正確的時間`, 'warning');
          return;
        }
        if (!this.title) {
          Swal.fire('警告', `尚未填寫標題`, 'warning');
          return;
        }
        const status: string = this.typeGroupId ? '編輯' : '新增';
        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateNewsReq = {
          menuitemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          titleName: this.title,
          keyword: this.keyword,
          description: this.description,
          startTime: this.startTime
            ? format(
              toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
              "yyyy-MM-dd'T'HH:mm:ss",
              { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
            )
            : '',
          endTime: this.endTime
            ? format(
              toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
              "yyyy-MM-dd'T'HH:mm:ss",
              { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
            )
            : '',
          isTop: this.isTop,
          coverDataId: this.coverId,
          levelDecision: this.levelDecision,
          new_NewsTagDatas: newsData,
          lang: sessionStorage.getItem('lang') || 'zh',
          isNews: false,
        };
        this.newsService.createUpdateNews(req).subscribe({
          next: (resp: createUpdateResp) => {
            if (resp.code === 200) {
              Swal.fire('成功', `儲存成功`, 'success').then(() => {
                if (status === SAVESTATUS.SAVELEAVE) {
                  this.router.navigate([`/manage/${this.menuItemId}/img/list`]);
                } else {
                  this.typeGroupId = resp.data;
                  this.router.navigate([], {
                    relativeTo: this.activatedRoute,
                    queryParams: {
                      id: this.typeGroupId,
                      menuItemId: this.menuItemId,
                    },
                    replaceUrl: true
                  });
                }
              });
            } else {
              Swal.fire('失敗', `${resp.message}`, 'error');
            }
          },
          error: (err: HttpErrorResponse) => {
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }
  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'Img',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }

    if (this.startTime && this.endTime && (this.endTime < this.startTime)) {
      Swal.fire('警告', `請選擇正確的時間`, 'warning');
      return;
    }

    if (!this.title) {
      Swal.fire('警告', `尚未填寫標題`, 'warning');
      return;
    }

    const status: string = this.typeGroupId ? '編輯' : '新增';
    let newsData: { tagName: string; dataString: string | null }[] = [];
    this.contentData.map((item) => {
      newsData.push({
        tagName: item.type,
        dataString: item.data ? JSON.stringify(item.data) : null,
      });
    });
    let req: createUpdateNewsReq = {
      menuitemId: this.menuItemId,
      typeGroupId: this.typeGroupId,
      keyword: this.keyword,
      titleName: this.title,
      description: this.description,
      startTime: this.startTime
        ? format(
          toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      endTime: this.endTime
        ? format(
          toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
          "yyyy-MM-dd'T'HH:mm:ss",
          { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
        )
        : '',
      isTop: this.isTop,
      coverDataId: this.coverId,
      levelDecision: this.levelDecision,
      new_NewsTagDatas: newsData,
      lang: sessionStorage.getItem('lang') || 'zh',
      isNews: false,
    };
    this.newsService.createUpdateNews(req).subscribe({
      next: (resp: createUpdateResp) => {
        if (resp.code === 200) {
          this.router.navigate(['manage/view'], {
            queryParams: {
              menuItemId: this.menuItemId,
              typeGroupId: resp.data,
              type: 'Img',
              status: ApprovalStatus.BeforeApproval,
            },
          });
        } else {
          Swal.fire('失敗', `${resp.message}`, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });

  }
  // view() {
  //   if (this.isPendingApproval) {
  //     this.router.navigate(['manage/view'], {
  //       queryParams: {
  //         menuItemId: this.menuItemId,
  //         typeGroupId: this.typeGroupId,
  //         type: 'Img',
  //         status: ApprovalStatus.ViewApproval,
  //       },
  //     });
  //     return;
  //   }
  //   Swal.fire({
  //     html: `
  //     <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
  //     <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
  //   `,
  //     showCancelButton: true,
  //     reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
  //   }).then((result) => {
  //     if (result.isConfirmed) {
  //       if (!this.title) {
  //         Swal.fire('警告', `尚未填寫標題`, 'warning');
  //         return;
  //       }

  //       const status: string = this.typeGroupId ? '編輯' : '新增';
  //       let newsData: { tagName: string; dataString: string | null }[] = [];
  //       this.contentData.map((item) => {
  //         newsData.push({
  //           tagName: item.type,
  //           dataString: item.data ? JSON.stringify(item.data) : null,
  //         });
  //       });
  //       let req: createUpdateNewsReq = {
  //         menuitemId: this.menuItemId,
  //         typeGroupId: this.typeGroupId,
  //         titleName: this.title,
  //         description: this.description,
  //         startTime: this.startTime
  //           ? format(
  //               toZonedTime(this.startTime, 'Asia/Taipei'), // 先轉換時區
  //               "yyyy-MM-dd'T'HH:mm:ss",
  //               { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
  //             )
  //           : '',
  //         endTime: this.endTime
  //           ? format(
  //               toZonedTime(this.endTime, 'Asia/Taipei'), // 先轉換時區
  //               "yyyy-MM-dd'T'HH:mm:ss",
  //               { timeZone: 'Asia/Taipei' } // 這裡要用 `timeZone` 屬性
  //             )
  //           : '',
  //         isTop: this.isTop,
  //         coverDataId: this.coverId,
  //         levelDecision: this.levelDecision,
  //         new_NewsTagDatas: newsData,
  //         lang: sessionStorage.getItem('lang') || 'zh',
  //         isNews: false,
  //       };
  //       this.newsService.createUpdateNews(req).subscribe({
  //         next: (resp: createUpdateResp) => {
  //           if (resp.code === 200) {
  //             this.router.navigate(['manage/view'], {
  //               queryParams: {
  //                 menuItemId: this.menuItemId,
  //                 typeGroupId: resp.data,
  //                 type: 'Img',
  //                 status: ApprovalStatus.BeforeApproval,
  //               },
  //             });
  //           } else {
  //             Swal.fire('失敗', `${resp.message}`, 'error');
  //           }
  //         },
  //         error: (err: HttpErrorResponse) => {
  //           Swal.fire('失敗', err.error.message, 'error');
  //         },
  //       });
  //     }
  //   });
  // }
}

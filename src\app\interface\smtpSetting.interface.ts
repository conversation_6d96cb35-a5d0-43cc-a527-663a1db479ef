import { defaultItem } from './share.interface';

export interface getSmtpSettingResp extends defaultItem {
  data: smtpItem;
}

export interface smtpItem extends defaultItem {
  host: string;
  port: number;
  enableSsl: boolean;
  account: string;
  password: string;
}

export interface updateSmtpSettingReq {
  webSiteId: string;
  host: string;
  port: number;
  enableSsl: boolean;
  account: string;
  password: string;
}

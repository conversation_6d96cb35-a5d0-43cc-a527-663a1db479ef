import { Component } from '@angular/core';
import { BannerService } from '../../../core/services/banner.service';
import { Banner } from '../../../shared/models/banner.model';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { BannerDialogComponent } from '../dialogs/banner/banner-dialog/banner-dialog.component';

@Component({
  selector: 'app-banner',
  standalone: false,
  templateUrl: './banner.component.html',
  styleUrl: './banner.component.scss',
})
export class BannerComponent {
  index: number = 0;
  infinite: boolean = true;
  direction: string = 'right';
  directionToggle: boolean = true;
  autoplay: boolean = true;
  banners: Banner[] = [];

  loading = false;

  constructor(
    private _bannerService: BannerService,
    private matDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getBanner();
  }

  getBanner() {
    this._bannerService
      .getBannerList(sessionStorage.getItem('webSiteId')!)
      .subscribe((x: Banner[]) => {
        this.banners = x as any;
        this.loading = false;
        this.banners.forEach((element: Banner) => {
          if (element.type == 'Youtube') {
            element.bannerDataId = element.bannerDataId!.replace(
              'watch?v=',
              'embed/'
            );
          }
        });
      });
  }

  setting() {
    this.matDialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '900px',
          showHeader: true,
          title: '首頁輪播設定',
          contentTemplate: BannerDialogComponent,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getBanner();
      });
  }

  videoLoad($event: any) {
    $event.target.muted = true;
    $event.target.play();
  }
}

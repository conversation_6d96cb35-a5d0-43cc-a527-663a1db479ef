import { Dialog } from '@angular/cdk/dialog';
import { Role } from './../../../shared/models/role.model';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { RoleService } from '../../../core/services/role.service';
import { EnumTypeInfo } from '../../../shared/models/enumTypeInfo.model';
import { MenuItemService } from '../../../core/services/menuItem.service';
import { Subscription } from 'rxjs';
import { CreateRoleComponent } from './create-role/create-role.component';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { MatMenuTrigger } from '@angular/material/menu';
import { RoleContextMenuComponent } from './role-context-menu/role-context-menu.component';
import { MenuItem } from '../../../shared/models/menuItem.model';
import { HttpErrorResponse } from '@angular/common/http';

interface randomObjType {
  [key: string]: any;
}

export interface treeNode {
  id: string;
  name: string;
  parentId: string;
  children?: treeNode[];
}

@Component({
  selector: 'app-role-setting',
  standalone: false,

  templateUrl: './role-setting.component.html',
  styleUrl: './role-setting.component.scss',
})
export class RoleSettingComponent {
  roleList: randomObjType[] = [];
  // roleList: Role[] = [];
  functionList: randomObjType[] = [];
  // functionList: EnumTypeInfo[] = [];
  loading: boolean = true;
  functionLoading: boolean = true;
  menuLoading: boolean = true;
  // treeNode: INodeModel = {};
  $sub!: Subscription;
  dataSource: treeNode[] = [];
  content: MenuItem | null = null;

  menuX = 0;
  menuY = 0;
  contextMenuPositionData!: MouseEvent;
  @ViewChild(RoleContextMenuComponent)
  contextMenuComponent!: RoleContextMenuComponent;
  childrenAccessor = (node: treeNode) => node.children ?? [];
  hasChild = (_: number, node: treeNode) =>
    !!node.children && node.children.length > 0;

  constructor(
    private _roleService: RoleService,
    private _menuItemService: MenuItemService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getRoleList();
    this.getFunctionList();
  }

  onContextMenu(event: MouseEvent, item: any) {
    event.preventDefault();
    this.contextMenuComponent.open();
    this.contextMenuPositionData = event;
    this.contextMenuComponent.contextMenuPosition.x = event.clientX + 'px';
    this.contextMenuComponent.contextMenuPosition.y = event.clientY + 'px';
    this.contextMenuComponent.selectedItemData = item;
  }

  closeContextMenu(emitData: { type: string; data: any }) {
    switch (emitData.type) {
      case 'edit':
        this.editRole(emitData.data);
        break;
      case 'menu':
        this.toggleMenu(emitData.data);
        break;
      case 'function':
        this.toggleFunction(emitData.data);
        break;
      case 'delete':
        this.delete(emitData.data);
        break;
    }
  }

  /** 選單權限 */
  toggleMenu(data: any) {
    this.roleList.map((x) => {
      x['menuShow'] = false;
      x['functionShow'] = false;
    });
    data.menuShow = !data.menuShow;
    if (data.menuShow) {
      this.getTree(data);
    }
  }

  /** 取得分類tree */
  getTree(data: any) {
    this._menuItemService
      .getMenu2(sessionStorage.getItem('webSiteId')!)
      .subscribe((tree: MenuItem[]) => {
        this._roleService.getRoleMenuPolicy(data.id).subscribe((res) => {
          this.dataSource = this.transformToTreeData(tree, res);
          if (tree.length) {
            this.menuLoading = false;
          } else {
            this.content = null;
          }
        });
      });
  }

  transformToTreeData(menuData: any, menuPolicy: string[]) {
    return menuData.map((menuItem: MenuItem) => {
      return {
        id: menuItem.id,
        name: menuItem.name,
        parentId: menuItem.parentId,
        children: menuItem.inverseParent?.length
          ? this.transformToTreeData(menuItem.inverseParent, menuPolicy)
          : [],
        isCheck: menuPolicy.includes(menuItem.id),
      };
    });
  }

  getRoleList() {
    this.loading = true;
    this._roleService
      .list(sessionStorage.getItem('webSiteId')!)
      .subscribe((res: Role[]) => {
        this.roleList = res;
        this.loading = false;
      });
  }

  getFunctionList() {
    this.functionLoading = true;
    this._roleService
      .getRoleFunctionPolicy()
      .subscribe((res: EnumTypeInfo[]) => {
        this.functionList = res;
        this.functionLoading = false;
      });
  }

  /** 更新啟用/停用狀態 */
  updateEnable($event: any, role: randomObjType) {
    this.loading = true;
    let req = {
      enable: $event.checked,
      id: role['id'],
      isDeleted: role['isDeleted'],
      name: role['name'],
      sort: role['sort'],
      webSiteId: role['webSiteId'],
    };

    this._roleService.update(req).subscribe({
      next: () => {
        this.loading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        this.getRoleList();
      },
    });
  }

  /** 新增 */
  createRole() {
    const createRoleDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        showHeader: true,
        title: '建立角色',
        contentTemplate: CreateRoleComponent,
      },
    });

    createRoleDialog.afterClosed().subscribe((name) => {
      if (name) {
        this.loading = true;
        this._roleService
          .create({
            webSiteId: sessionStorage.getItem('webSiteId')!,
            name: name,
            enable: true,
          })
          .subscribe({
            next: () => {
              this.getRoleList();
            },
            error: (err: HttpErrorResponse) => {
              this.loading = false;
            },
          });
      }
    });
  }

  /** 編輯 */
  editRole(data: any) {
    const createRoleDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        showHeader: true,
        title: '編輯角色',
        contentTemplate: CreateRoleComponent,
        data: data,
      },
    });
    createRoleDialog.afterClosed().subscribe((name) => {
      if (name) {
        this.loading = true;
        data.name = name;
        this._roleService.update(data).subscribe({
          next: () => {
            this.getRoleList();
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
          },
        });
      }
    });
  }

  /** 功能選單縮小 */
  closeFuction(data: any) {
    data.functionShow = false;
  }

  /** 選單權限縮小 */
  closeMenu(data: any) {
    data.menuShow = false;
  }

  /** 刪除 */
  delete(data: any) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this._roleService.delete(data.id).subscribe({
          next: () => {
            this.getRoleList();
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
          },
        });
      }
    });
  }

  /** 功能選單 */
  toggleFunction(data: any) {
    this.roleList.map((x) => {
      x['menuShow'] = false;
      x['functionShow'] = false;
    });
    data.functionShow = !data.functionShow;
    if (data.functionShow) {
      this.setFunctionCkecked(data);
    }
  }

  setFunctionCkecked(role: any) {
    this.functionList.map((x) => {
      x['checked'] = false;
    });
    this.functionLoading = true;
    if (this.$sub) {
      this.$sub.unsubscribe();
    }
    this._roleService.getRoleFunctionPolicyAll(role.id).subscribe((policy) => {
      for (const p of policy) {
        for (const func of this.functionList) {
          if (p === func['type']) {
            func['checked'] = true;
          }
        }
      }
      this.functionLoading = false;
    });
  }

  /** 功能選單勾選 */
  functionChecked($event: any, func: any, role: any) {
    this.functionLoading = true;
    const originalChecked = func.checked; // 原始狀態
    const newChecked = $event.checked;

    // 先更新 UI 狀態
    func.checked = newChecked;

    const action = newChecked
      ? this._roleService.addFunctionPolicy(role.id, func.type)
      : this._roleService.deleteFunctionPolicy(role.id, func.type);

    action.subscribe({
      next: () => {
        this.functionLoading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.functionLoading = false;
        // API 失敗，還原勾選狀態
        func.checked = originalChecked;
      },
    });
  }

  /** 選單權限勾選 */
  menuPolicyChecked($event: any, menu: any, role: any) {
    this.menuLoading = true;
    $event.checked
      ? this._roleService
          .addRoleMenuPolicy(role.id, menu.id)
          .subscribe((res) => {
            this.menuLoading = false;
          })
      : this._roleService
          .deleteRoleMenuPolicy(role.id, menu.id)
          .subscribe((res) => {
            this.menuLoading = false;
          });
  }

  selectAllFunc(role: any) {
    this.functionLoading = true;
    this._roleService
      .updateRoleFunctionPolicy(
        role.id,
        this.functionList.map((x) => x['type'])
      )
      .subscribe((x) => {
        this._roleService
          .getRoleFunctionPolicyAll(role.id)
          .subscribe((policy) => {
            for (const p of policy) {
              for (const func of this.functionList) {
                if (p === func['type']) {
                  func['checked'] = true;
                }
              }
            }
            this.functionLoading = false;
          });
      });
  }
}

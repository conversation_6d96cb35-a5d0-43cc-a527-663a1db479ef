import { Component, HostListener, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { NewsService } from '../../../../core/services/news.service';
import { MenuItem } from '../../../../shared/models/menuItem.model';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import {
  getNewsListReq,
  getNewsListResp,
  getNewsUserGroupResp,
  newsItem,
} from '../../../../interface/news.interface';
import { ShareService } from '../../../../core/services/share.service';
import { defaultItem, getTypeListResp, setSortReq } from '../../../../interface/share.interface';
import { UserGroupService } from '../../../../core/services/userGroup.service';
import { UserGroup } from '../../../../shared/models/userGroup.model';
import { format } from 'date-fns';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { SetSortDialogComponent } from '../../../components/set-sort-dialog/set-sort-dialog.component';

@Component({
  selector: 'app-news-list',
  standalone: false,
  templateUrl: './news-list.component.html',
  styleUrl: './news-list.component.scss',
})
export class NewsListComponent implements OnInit {
  loading: boolean = false;
  attachmentsArr: File[] = [];
  menuItemId: string = '';

  title: string = '';
  menuItem!: MenuItem;
  bannerUrl: string = '';
  hiddenPageTitle: boolean = false;
  keywordstring: string = '';
  keywordtype: string = '';
  keywordtitle: string = '';
  userGroupId: string = '';
  keywordStartDate: string = '';
  keywordEndDate: string = '';
  groupList: {
    userGroupId: string;
    userGroupName: string;
  }[] = [];
  typeList: { typeValue: string; typeName: string }[] = [];

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  newsList: newsItem[] = [];
  sort?: number;

  constructor(
    private newsService: NewsService,
    private _route: ActivatedRoute,
    private _router: Router,
    private userGroupService: UserGroupService,
    private shareService: ShareService,
    public dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getGroupList();
      this.getTypeList('類型選項');
      this.getNewsList();
    });
  }

  searchlist() {
    this.nowPage = 1;
    this.getNewsList();
  }

  getGroupList() {
    this.newsService
      .getNewsUserGroup()
      .subscribe((resp: getNewsUserGroupResp) => {
        this.groupList = resp.data;
      });
  }

  getTypeList(type: string) {
    this.shareService.getTypeList(type).subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
      },
      error: (err: HttpErrorResponse) => { },
    });
  }

  getNewsList() {
    let req: getNewsListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      startTime: this.keywordStartDate ? format(new Date(this.keywordStartDate), 'yyyy-MM-dd') : '',
      endTime: this.keywordEndDate ? format(new Date(this.keywordEndDate), 'yyyy-MM-dd') : '',
      userGroupId: this.userGroupId,
      type: this.keywordtype,
      keyword: this.keywordtitle,
    };
    this.loading = true;
    this.newsService.getNewsList(req).subscribe({
      next: (resp: getNewsListResp) => {
        this.title = resp.data.title;
        this.newsList = resp.data.data;
        this.totalCount = resp.data.totalCount;
        this.loading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.userGroupId = '';
    this.keywordtitle = '';
    this.keywordtype = '';
    this.keywordEndDate = '';
    this.keywordStartDate = '';
    this.nowPage = 1;
    this.getNewsList();
  }

  addNews() {
    this._router.navigate([`/manage/${this.menuItemId}/news/edit`]);
  }

  editNews(id: string) {
    // this._router.navigate(['../', 'edit', id], {
    //   relativeTo: this._route,
    // });
    this._router.navigate([`/manage/${this.menuItemId}/news/edit`], {
      queryParams: { id: id },
    });
  }

  /** 刪除消息 */
  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.newsService.deleteNews(id).subscribe({
          next: (res) => {
            this.loading = false;
            Swal.fire({
              title: '刪除成功',
              icon: 'success',
              showCancelButton: false,
              reverseButtons: true,
            }).then(() => {
              this.resetsearchlist();
            });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire({
              title: '刪除失敗',
              icon: 'error',
              showCancelButton: false,
              reverseButtons: true,
            });
          },
        });
      }
    });
  }

  setSort(item: newsItem) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        title: '設定排序',
        typeGroupId: item.typeGroupId,
        menuitemId: this.menuItemId,
        sort: item.sort,
        totalCount: this.totalCount,
        contentTemplate: SetSortDialogComponent,
      },
    }).afterClosed().subscribe(() => {
      this.getNewsList();
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getNewsList();
  }

  @HostListener('window:resize')
  onResize() {
    if (this.menuItem) {
      if (window.innerWidth >= 1366) {
        this.bannerUrl = this.menuItem.bannerLUrl!;
      } else if (window.innerWidth <= 768) {
        this.bannerUrl = this.menuItem.bannerSUrl!;
      } else {
        this.bannerUrl = this.menuItem.bannerMUrl!;
      }
    }
  }
}

import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { catchError } from "rxjs/operators";
import { AppCategoryInfo, AppCategoryRequest } from "../../shared/models/appcategory.model"

@Injectable({
  providedIn: "root",
})
export class AppCategoryService {
  baseUrl = `api/AppCategory`;

  constructor(private http: HttpClient) {}

  // 抓取category裡面的資料
  getCategoryInfo(categoryId: string): Observable<AppCategoryRequest> {
    return this.http.get<AppCategoryRequest>(
      `${this.baseUrl}/GetCategoryInfo?categoryId=${categoryId}`
    );
  }

  // 抓取category列表
  getCategoryList(): Observable<AppCategoryInfo[]> {
    return this.http.get<AppCategoryInfo[]>(
      `${this.baseUrl}/GetCategoryList`
    );
  }

  //儲存修改分類資料
  insertOrUpdate(appCategoryRequest: AppCategoryRequest):Observable<boolean> {
    return this.http.post<boolean>(
      `${this.baseUrl}/SaveCategoryInfo`,
      appCategoryRequest
    );
  }

  //刪除分類資料
  deleteCategory(categoryId:string):Observable<boolean> {
    return this.http.get<boolean>(
      `${this.baseUrl}/DeleteCategoryInfo?categoryId=${categoryId}`
    );
  }

}

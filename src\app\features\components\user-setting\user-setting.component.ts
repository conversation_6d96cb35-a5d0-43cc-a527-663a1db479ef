import { Component } from '@angular/core';
import { UserDataService } from '../../../core/services/userData.service';
import { MatDialog } from '@angular/material/dialog';
import { RoleService } from '../../../core/services/role.service';
import { Role } from '../../../shared/models/role.model';
import { PagingOfUserData } from '../../../shared/models/pagingOfUserData.model';
import { UserData } from '../../../shared/models/userData.model';
import { CreateUserComponent } from '../create-user/create-user.component';
import Swal from 'sweetalert2';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { RegistrationSettingComponent } from './registration-setting/registration-setting.component';
import { LoginCountComponent } from './login-count/login-count.component';
import { defaultItem } from '../../../interface/share.interface';

const eachFileUploadlimit = 31457280;
@Component({
  selector: 'app-user-setting',
  standalone: false,

  templateUrl: './user-setting.component.html',
  styleUrl: './user-setting.component.scss',
})
export class UserSettingComponent {
  keyword: string = '';
  roleList: Role[] = [];
  userList: UserData[] = [];
  loading: boolean = false;
  skip: number = 0;
  totalCount: number = 0;
  attachmentsArr: File[] = [];

  constructor(
    private _userDataService: UserDataService,
    private dialog: MatDialog,
    private _roleService: RoleService
  ) {}

  ngOnInit(): void {
    this.getUserList();
    this.getRoleList();
  }

  //抓取腳色列表
  getRoleList() {
    this.loading = true;
    this._roleService
      .list(sessionStorage.getItem('webSiteId')!)
      .subscribe((res: Role[]) => {
        this.roleList = res;
        this.loading = false;
      });
  }

  getUserList() {
    this.loading = true;
    this._userDataService
      .list(sessionStorage.getItem('webSiteId')!)
      .subscribe((res: PagingOfUserData) => {
        this.userList = res.result!;
        this.totalCount = res.totalCount!;
        this.loading = false;
      });
  }

  // 根據使用者角色搜尋使用者
  selectROLE(value: string) {
    console.log('ROLE : ', value);
    this.loading = true;
    this.skip = 0;
    this._userDataService
      .list(
        sessionStorage.getItem('webSiteId')!,
        value,
        ['roleId'],
        null,
        null,
        this.skip,
        10
      )
      .subscribe((res) => {
        this.userList = res.result!;
        this.totalCount = res.totalCount!;
        this.loading = false;
      });
  }

  /** 搜尋 */
  search() {
    this.loading = true;
    this.skip = 0;
    this._userDataService
      .list(
        sessionStorage.getItem('webSiteId')!,
        this.keyword,
        ['name'],
        null,
        null,
        this.skip,
        10
      )
      .subscribe((res) => {
        this.userList = res.result!;
        this.totalCount = res.totalCount!;
        this.loading = false;
      });
  }

  /** 建立 */
  createUser() {
    const createUserDig = this.dialog.open(DialogComponent, {
      data: {
        width: '800px',
        height: '700px',
        showHeader: true,
        contentTemplate: CreateUserComponent,
        title: '建立使用者',
      },
    });
    createUserDig.afterClosed().subscribe((res) => {
      if (res) {
        this.loading = true;
        delete res.id;
        res.memberId = Math.floor(new Date().getTime() / 1000);
        res.birthday = Math.floor(new Date().getTime() / 1000);
        this._userDataService.create(res).subscribe((x) => {
          this.getUserList();
        });
      }
    });
  }

  /** 註冊設置 */
  registrationSetting() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '500px',
        showHeader: true,
        title: '註冊設置',
        contentTemplate: RegistrationSettingComponent,
      },
    });
  }

  /** 登入次數 */
  login(data: any) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '700px',
        showHeader: true,
        title: '登入次數查詢',
        contentTemplate: LoginCountComponent,
        data: data,
      },
    });
  }

  /** 編輯 */
  editUser(data: any) {
    this.getUserList();
    data.birthday = data.birthday ? new Date(data.birthday * 1000) : null;
    const createUserDig = this.dialog.open(DialogComponent, {
      data: {
        width: '800px',
        height: '700px',
        showHeader: true,
        title: '編輯使用者',
        contentTemplate: CreateUserComponent,
        data: data,
      },
    });

    createUserDig.afterClosed().subscribe((res) => {
      if (res) {
        this.loading = true;
        this._userDataService.update22(res).subscribe((x) => {
          this.getUserList();
        });
      }
    });
  }

  /** 載入下一頁 */
  loadMore() {
    this.loading = true;
    this.skip = this.skip + 10;
    this._userDataService
      .list(
        sessionStorage.getItem('webSiteId')!,
        this.keyword,
        ['name'],
        null,
        null,
        this.skip,
        10
      )
      .subscribe((res) => {
        res.result?.map((x) => {
          this.userList.push(x);
        });
        this.loading = false;
      });
  }

  /** 刪除 */
  delete(data: any) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this._userDataService.delete(data.id).subscribe((x) => {
          this.getUserList();
        });
      }
    });
  }

  /** 更新啟用狀態 */
  updateEnable($event: any, data: any) {
    this.loading = true;
    data.enable = $event.checked;
    this._userDataService.update22(data).subscribe((x) => {
      this.loading = false;
    });
  }

  inputAttachments(event: any) {
    this.attachmentsArr = [];
    const files = event.target.files;
    for (const file of files) {
      let filename = file.name;
      //副檔名
      let ext = file.name.substring(
        filename.lastIndexOf('.') + 1,
        filename.length
      );
      // console.log('ext', ext);
      if (file.size > eachFileUploadlimit) {
        let warningText =
          "'檔案超過'+(eachFileUploadlimit / 1024 / 1024)+'M, 不允許上傳!'";
        Swal.fire(warningText);
        // this.fileInput.nativeElement.value = "";
        return;
      } else if (ext != 'xls' && ext != 'xlsx') {
        // console.log(file.type)

        Swal.fire('檔案類型必須為excel檔案');
        // this.fileInput.nativeElement.value = "";
        return;
      }

      this.attachmentsArr.push(file);
      //console.log(this.attachmentsArr);
    }
  }

  /** 建立 */
  importUser() {
    this.loading = true;
    if (this.attachmentsArr[0] == null) {
      this.loading = false;
      Swal.fire('匯入失敗', '請選擇檔案', 'error');
    } else {
      this._userDataService
        .import(sessionStorage.getItem('webSiteId')!, this.attachmentsArr[0])
        .subscribe((resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('匯入成功', '使用者資料成功匯入', 'success').then(() => {
              this.getUserList();
            });
          } else {
            Swal.fire('匯入失敗', resp.message, 'error');
          }
        });
    }
  }
}

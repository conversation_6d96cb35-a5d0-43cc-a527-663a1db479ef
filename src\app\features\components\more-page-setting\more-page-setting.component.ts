import {
  Component,
  OnInit,
  ComponentFactoryResolver,
  Injector,
  On<PERSON><PERSON>roy,
  Input,
  HostListener,
} from "@angular/core";
//import { ContextMenuService, ContextMenuComponent } from "ngx-contextmenu";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from "@angular/platform-browser";
//import { NgxPopupService } from "@hamastar/ngx-popup";
//import { MoreSettingComponent } from "../../../manage/components/more-setting/more-setting.component";
//import { AppCategoryInfo } from "src/app/api-sdk/models/appCategory";
//import { AppCategoryService } from "src/app/api-sdk/services/appCategory.service";
import { HttpErrorResponse } from "@angular/common/http";
import swal from "sweetalert2";

@Component({
  selector: 'app-more-page-setting',
  standalone: false,
  
  templateUrl: './more-page-setting.component.html',
  styleUrl: './more-page-setting.component.scss'
})
export class MorePageSettingComponent {
  //@Input() contextMenu: ContextMenuComponent;

  constructor(
    // private contextMenuService: ContextMenuService,
    // private _appCategoryService: AppCategoryService,
    // private _pop: NgxPopupService,
    private _injector: Injector
  ) {}

  //morePageSetting!: AppCategoryInfo[];

  ngOnInit() {
    this.getCategoryList();
  }

  public onContextMenu($event: MouseEvent, item: any): void {
    // this.contextMenuService.show.next({
    //   contextMenu: this.contextMenu,
    //   event: $event,
    //   item: item,
    // });
    $event.preventDefault();
    $event.stopPropagation();
  }

  getCategoryList() {
    // this._appCategoryService.getCategoryList().subscribe({
    //   next: (res:any) => {
    //     this.morePageSetting = res;
    //     //console.log(this.morePageSetting);
    //   },
    //   error: (err: HttpErrorResponse) => {
    //     //console.log(err);
    //   },
    // });
  }

  closeContextMenu($event:any) {
    switch ($event.type) {
      case "edit":
        //console.log("編輯");
        //console.log($event.data);
        //this.edit($event.data);
        break;
      case "delete":
        //console.log("刪除");
        //console.log($event.data.categoryId);
        //this.delete($event.data.categoryId);
        break;
    }
  }

  // edit(data:any) {
  //   this._pop
  //     .open(
  //       MoreSettingComponent,
  //       {
  //         resolver: this._resolve,
  //         injector: this._injector,
  //       },
  //       {
  //         title: "編輯",
  //         width: "1000px",
  //         showHeader: true,
  //         data: data,
  //       }
  //     )
  //     .subscribe((res:any) => {
  //       console.log(res);
  //       if (res) {
  //         this.getCategoryList();
  //       }
  //     });
  // }

  // /** 刪除連結 */
  // delete(item:any) {
  //   swal({
  //     title: "請問確定要刪除?",
  //     text: "您將無法恢復這筆資訊!",
  //     type: "warning",
  //     showCancelButton: true,
  //   }).then((result:any) => {
  //     if (result.value) {
  //       this._appCategoryService.deleteCategory(item).subscribe((x:any) => {
  //         swal.fire("刪除成功", "", "success").then(() => {
  //           this.getCategoryList();
  //         });
  //       });
  //     }
  //   });
  // }

  // add() {
  //   this._pop
  //     .open(
  //       MoreSettingComponent,
  //       {
  //         resolver: this._resolve,
  //         injector: this._injector,
  //       },
  //       {
  //         title: "新增",
  //         width: "1000px",
  //         showHeader: true,
  //       }
  //     )
  //     .subscribe((res:any) => {
  //       console.log("新增" + res);
  //       if (res) {
  //         this.getCategoryList();
  //       }
  //     });
  // }
}

import { typeMenuItem } from '../shared/models/dialog.model';

export enum METHODSTATUS {
  ADD = 'add',
  EDIT = 'edit',
  DELETE = 'delete',
}

export enum SAVESTATUS {
  SAVE = 'save',
  SAVELEAVE = 'saveLeave',
}

export enum ApprovalStatus {
  BeforeApproval = 0, //審核前
  AfterApproval = 1, //審核後
  ViewApproval = 2, //預覽
  EndApproval = 3, //審核完成
}

export enum ApiCode {
  SUCCESS = 200,
}

export const TypeList: typeMenuItem[] = [
  {
    name: '目錄',
    type: 'Folder',
    tag: '選單設計',
    url: 'assets/images/menuItemType/folder.jpg',
    linkUrl: '',
    selected: false,
  },
  {
    name: '頁籤',
    type: 'Tab',
    tag: '選單設計',
    url: 'assets/images/menuItemType/tab.jpg',
    selected: false,
    linkUrl: '',
  },
  {
    name: '圖片式',
    type: 'Img',
    tag: '訊息發布',
    url: 'assets/images/menuItemType/image.jpg',
    selected: false,
    MenuItemType: 'Img',
    linkUrl: 'img',
  },
  {
    name: '條列式',
    type: 'Line',
    tag: '訊息發布',
    url: 'assets/images/menuItemType/list.jpg',
    selected: false,
    MenuItemType: 'List',
    linkUrl: 'line',
  },
  {
    name: 'FAQ',
    type: 'FAQ',
    tag: '通知模組',
    url: 'assets/images/menuItemType/faq.jpg',
    selected: false,
    linkUrl: 'faq',
  },
  {
    name: 'HTML',
    type: 'HTML',
    tag: 'HTML模組',
    url: 'assets/images/menuItemType/HTML.jpg',
    selected: false,
    linkUrl: 'html',
  },
  {
    name: '靜態網頁ZIP檔案',
    type: 'HtmlZip',
    tag: '靜態網頁ZIP檔案',
    url: 'assets/images/menuItemType/HTML-index.png',
    selected: false,
    linkUrl: '',
  },
  {
    name: '內容頁',
    type: 'Content',
    tag: '影音圖文',
    url: 'assets/images/menuItemType/content.jpg',
    selected: false,
    MenuItemType: 'Content',
    linkUrl: 'content',
  },
  {
    name: '資源分享',
    type: 'ShareResource',
    tag: '資源分享',
    url: 'assets/images/menuItemType/resource.jpg',
    selected: false,
    linkUrl: 'shareResource',
  },
  {
    name: '超連結',
    type: 'HyperLink',
    tag: '連結模組',
    url: 'assets/images/menuItemType/link.png',
    selected: false,
    linkUrl: 'link',
  },
  {
    name: '影片',
    type: 'Video',
    tag: '影片',
    url: 'assets/images/menuItemType/video.jpg',
    selected: false,
    linkUrl: 'video',
  },
  {
    name: '最新消息',
    type: 'News',
    tag: '選單設計',
    url: 'assets/images/menuItemType/news.jpg',
    selected: false,
    linkUrl: 'news',
  },
  {
    name: '問卷',
    type: 'Survey',
    tag: '訊息發布',
    url: 'assets/images/menuItemType/Questionnaire.png',
    selected: false,
    linkUrl: 'survey',
  },
  {
    name: '徵才訊息',
    type: 'Recruit',
    tag: '訊息發布',
    url: 'assets/images/menuItemType/JOB POSTING.png',
    selected: false,
    MenuItemType: 'List',
    linkUrl: 'line',
  },
  {
    name: '書櫃',
    type: 'Ebook',
    tag: '書櫃',
    url: 'assets/images/menuItemType/E-PAPER.png',
    selected: false,
    MenuItemType: 'List',
    linkUrl: 'ebook',
  },
  {
    name: '電子報',
    type: 'ENewsletter',
    tag: '電子報',
    url: 'assets/images/menuItemType/news.jpg',
    selected: false,
    MenuItemType: 'List',
    linkUrl: 'eNewsletter',
  },
  {
    name: '捐款系列',
    type: 'DonateFolder',
    tag: '捐款',
    url: 'assets/images/menuItemType/DONATE.png',
    selected: false,
    linkUrl: 'donate',
  },
  {
    name: '快速選單',
    type: 'FastMenu',
    tag: '快速選單',
    url: 'assets/images/menuItemType/QUICK MENU.png',
    selected: false,
    linkUrl: 'fastMenu',
  },
  {
    name: '圖表',
    type: 'Chart',
    tag: '快速選單',
    url: 'assets/images/menuItemType/charts.png',
    selected: false,
    linkUrl: 'chart',
  },
];

export enum MenuType {
  Folder = 'Folder',
  Img = 'Img',
  Line = 'Line',
  FAQ = 'FAQ',
  Html = 'HTML',
  HtmlZip = 'HtmlZip',
  Content = 'Content',
  ShareResource = 'ShareResource',
  HyperLink = 'HyperLink',
  Tab = 'Tab',
  Video = 'Video',
  News = 'News',
  Survey = 'Survey',
}

@charset "UTF-8";
/* Sass Document */
/*最新消息 */
/* Sass Document */
/*最新消息 */
/*首頁 */
@media screen and (max-width: 500px) {

  .news-home-custom-css .contents .block,
  .news-list-custom-css .contents .block {
    display: block !important;
    min-width: calc(100% - 22px) !important;
  }

  .news-home-custom-css .contents .block img,
  .news-list-custom-css .contents .block img {
    width: 100% !important;
  }
}

/*活動相簿 */
/* Sass Document */
/*畫廊 */
/*首頁 */
@media screen and (max-width: 600px) {

  .gallery-layout .block,
  .gallery-home-custom-css .block {
    width: calc(100% / 2 - 22px) !important;
  }
}

@media screen and (max-width: 500px) {

  .gallery-layout .block,
  .gallery-home-custom-css .block {
    width: calc(100% - 22px) !important;
  }
}

/*畫廊-V2 */
/*首頁 */
@media screen and (max-width: 600px) {

  .folder-layout .contents .folder,
  .folder-list-custom-css .contents .folder {
    width: calc(100% / 2 - 22px) !important;
  }
}

@media screen and (max-width: 500px) {

  .folder-layout .contents .folder,
  .folder-list-custom-css .contents .folder {
    width: calc(100% - 22px) !important;
  }
}

/*最新消息 */
/* Sass Document */
/*最新消息 */
/*首頁 */
@media screen and (max-width: 500px) {

  .news-home-custom-css .contents .block,
  .news-list-custom-css .contents .block {
    display: block !important;
    min-width: calc(100% - 22px) !important;
  }

  .news-home-custom-css .contents .block img,
  .news-list-custom-css .contents .block img {
    width: 100% !important;
  }
}

/*版型-資源分享 */
/* Sass Document */
/*資源分享 */
@media screen and (max-width: 600px) {
  .share-home-custom-css .contents .file-block {
    display: block !important;
  }

  .share-home-custom-css .contents .file-block img {
    float: left !important;
    width: 30px !important;
    height: auto !important;
  }

  .share-home-custom-css .contents .file-block .name {
    display: block;
  }

  .share-home-custom-css .contents .file-block .downloadCount {
    margin-top: 0px;
    display: block;
  }
}

/*版型-分類分頁 */
/* Sass Document */
/*分類分頁 */
/*首頁 */
/*影片 */
/* Sass Document */
/*影片 */
/*首頁 */
@media screen and (max-width: 768px) {
  .video-layout .white .video-block {
    display: block !important;
  }

  .video-layout .white .video-view {
    min-width: calc(100% - 20px) !important;
  }

  .video-layout .white .video-list {
    width: 100%;
    min-width: 100% !important;
  }
}

/*相關連結 */
/* Sass Document */
/*相關連結 */
@media screen and (max-width: 820px) {

  .link-layout .contents .link,
  .link-home-custom-css .contents .link {
    width: calc(100% / 2 - 22px) !important;
    min-width: calc(100% / 2 - 22px) !important;
  }
}

@media screen and (max-width: 500px) {

  .link-layout .contents .link,
  .link-home-custom-css .contents .link {
    width: calc(100% - 22px) !important;
    min-width: calc(100% - 22px) !important;
  }
}

/*通用版型 */
/*表格 */
/* Sass Document */
.mat-table .mat-header-cell {
  /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#fe9265+0,db4040+100 */
  background: #fe9265;
  background: -moz-linear-gradient(top, #fe9265 0%, #db4040 100%);
  background: -webkit-linear-gradient(top, #fe9265 0%, #db4040 100%);
  background: linear-gradient(to bottom, #fe9265 0%, #db4040 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fe9265', endColorstr='#db4040', GradientType=0);
  padding: 10px !important;
  font-size: 1rem;
}

.mat-table .mat-header-cell:first-child {
  -webkit-border-top-left-radius: 5px;
  -moz-border-radius-topleft: 5px;
  border-top-left-radius: 5px;
}

.mat-table .mat-header-cell:last-child {
  -webkit-border-top-right-radius: 5px;
  -moz-border-radius-topright: 5px;
  border-top-right-radius: 5px;
}

.mat-table .mat-row .mat-cell {
  padding: 5px 10px !important;
  font-size: 1rem !important;
}

.required-star {
  color: red;
}
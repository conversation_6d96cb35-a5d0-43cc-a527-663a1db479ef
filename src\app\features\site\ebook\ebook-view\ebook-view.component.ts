import { Component } from '@angular/core';
import { ApprovalStatus } from '../../../../enum/share.enum';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml, SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ReviewService } from '../../../../core/services/review.service';
import { EbookService } from '../../../../core/services/ebook.service';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { getEbookResp } from '../../../../interface/ebook.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { approvalReq } from '../../../../interface/review.interface';

@Component({
  selector: 'app-ebook-view',
  standalone: false,

  templateUrl: './ebook-view.component.html',
  styleUrl: './ebook-view.component.scss',
})
export class EbookViewComponent {
  loading: boolean = false;
  bookId: string = '';
  menuItemId: string = ''; //選單id
  typeGroupId: string = ''; //內容id
  type: string = ''; //選單類別
  status: ApprovalStatus = ApprovalStatus.BeforeApproval; //審核狀態 送審前or送審後
  url: SafeUrl = '';
  approval: boolean = true;
  reason: string = '';
  ApprovalStatus = ApprovalStatus;
  openStatus: boolean = true;

  file: File | null = null;
  fileName: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private ebookService: EbookService,
    private reviewService: ReviewService,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.menuItemId = queryParams.get('menuItemId') as string;
      this.typeGroupId = queryParams.get('typeGroupId') as string;
      this.bookId = queryParams.get('bookId') as string;
      this.type = queryParams.get('type') as string;
      this.status = Number(queryParams.get('status')) as ApprovalStatus;
    });
  }

  ngOnInit() {
    this.getEbookForView();
  }

  getPolicy(policy: string) {
    const functionPolicies = sessionStorage.getItem('functionPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(policy) > -1) {
      return true;
    }
    return false;
  }

  getEbookForView() {
    this.ebookService.getEbook(this.bookId).subscribe({
      next: (resp: getEbookResp) => {
        if (resp.code === 200) {
          this.url = this.sanitizer.bypassSecurityTrustResourceUrl(
            resp.data.previewUrl
          );
          console.log(this.url);
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  cancel() {
    history.back();
  }

  submitForReview() {
    this.loading = true;
    this.reviewService
      .submitForReview({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '送審完成', 'success').then(() => {
              this.router.navigate([`/manage/${this.menuItemId}/ebook/list`]);
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }

  selectGalleryFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    // const maxFileSize = 3 * 1024 * 1024;
    // if (file.size > maxFileSize) {
    //   Swal.fire('檔案大小超過 3MB，請重新選擇', '', 'warning');
    //   this.loading = false;
    //   return;
    // }
    if (
      file.type !== 'image/jpeg' &&
      file.type !== 'image/png' &&
      file.type !== 'application/x-zip-compressed' &&
      file.type !== 'application/pdf'
    ) {
      Swal.fire('請選擇jpg或png或zip檔案', '', 'warning');
      this.loading = false;
      return;
    }
    this.file = file;

    if (file.name) {
      this.loading = false;
    }
    this.fileName = file.name;
  }

  send() {
    this.loading = true;
    let req: approvalReq = {
      typeGroupId: this.typeGroupId,
      approval: this.approval,
      menuItemType: this.type,
      reason: this.reason,
      file: this.file,
    };
    this.reviewService.approval(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '審核完成', 'success').then(() => {
            history.back();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  submitForPublish() {
    this.loading = true;
    this.reviewService
      .publish({
        typeGroupId: this.typeGroupId,
        menuItemType: this.type,
      })
      .subscribe({
        next: (resp: defaultItem) => {
          this.loading = false;
          if (resp.code === 200) {
            Swal.fire('成功', '發布完成', 'success').then(() => {
              this.router.navigate([`/manage/${this.menuItemId}/ebook/list`]);
            });
          } else {
            Swal.fire('失敗', resp.message, 'error');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.loading = false;
          Swal.fire('失敗', err.error.message, 'error');
        },
      });
  }
}

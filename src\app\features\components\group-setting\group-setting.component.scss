.Setting-layout {
    .new-btns {
        margin-top: 20px;
        .btns {
            display: flex;
            justify-content: flex-end;
        }
    }

    .role-list {
        height: 100%;
        overflow-y: auto;

        .role-block {
            display: flex;
            flex-direction: column;
            margin: 15px 20px;
            transition: 0.3s ease-in-out;

            // &:hover {
            //     box-shadow: 0 0 10px #ccc;
            // }

            .role-name {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 1.125em;
                line-height: 1.8;
                outline: 0;
                background-color: #fff;
                border: 1px solid #949494;
                padding: 8px 10px;
                border-radius: 5px;
                cursor: pointer;
            }

            .group-user {
                border-bottom: 1px solid #949494;
                position: relative;
                min-height: 100px;
                display: flex;
                flex-direction: column;
                padding: 0 1em;
                line-height: 2;
                &:hover {
                    background-color: #fff;
                }
                .group-btn {
                    display: flex;
                    justify-content: space-between;
                    margin: 10px 0;
                }
                .text-name {
                    font-size: 1.125em;
                    color: #232127;
                    background-color: #c2efff;
                    border: 1px solid #00aeef;
                    border-radius: 5px;
                    padding: 5px;
                    margin: 5px 0;
                }
                .text_c {
                    font-size: 1.125em;
                    color: #949494;
                }

                .zoomout {
                    text-align: center;

                    .material-icons {
                        cursor: pointer;
                    }
                }
            }
        }
    }
}

//=====================================

.popup-layout {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #0000004d;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;

    .popup-block {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 50px 0 #9c9c9c;
        padding: 1em;
        display: flex;
        flex-direction: column;
        max-width: 90%;
        max-height: 90%;

        .popup-close {
            font-size: 1.5em;
            font-weight: 700;
            line-height: 1.5;
            font-family: Microsoft JhengHei;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            min-height: 36px;
            .close-btn {
                // display: flex;
                // flex-direction: column;
                // width: 36px;
                // height: 36px;
                cursor: pointer;
                // opacity: 0.5;
                span {
                    height: 3px;
                    margin: 3px;
                    .left {
                        transform: rotate(45deg) translate(8px, 7.5px);
                    }
                }

                span.right {
                    transform: rotate(-45deg) translate(-1px, 2px);
                }
            }
        }

        .popup-content {
            height: 100%;
            overflow-y: auto;
        }
    }
}

.popup-layout .popup-block .popup-close .close-btn span.left {
    transform: rotate(45deg) translate(8px, 7.5px);
}

.danger {
    background-color: red;
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  changeSortReq,
  createUpdateContentReq,
  createUpdateResp,
  defaultItem,
  getAchievementsResp,
  getAuditLogChartReq,
  getAuditLogChartResp,
  getAuditLogListReq,
  getAuditLogListResp,
  getClickLogChartResp,
  getClickLogListReq,
  getClickLogListResp,
  getContentResp,
  getGustViewLogChartReq,
  getGustViewLogChartResp,
  getGustViewLogListReq,
  getGustViewLogListResp,
  getSearchKeywordLogChartResp,
  getSearchKeywordLogListReq,
  getSearchKeywordLogListResp,
  getTextCloudResp,
  getTypeListResp,
  setSortReq,
} from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ShareService {
  constructor(private httpClient: HttpClient) { }

  getLang() {
    return sessionStorage.getItem('lang') || 'zh';
  }

  getTextCloud(): Observable<getTextCloudResp> {
    return this.httpClient.get<getTextCloudResp>(
      'api/WordCloud/GetWordCloudList',
      {
        params: {
          pageSize: -1,
          lang: this.getLang(),
        },
      }
    );
  }

  getTypeList(type: string): Observable<getTypeListResp> {
    return this.httpClient.get<getTypeListResp>(
      'api/Manage/New_News/GetTypeList',
      {
        params: {
          type: type,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }

  getContent(id: string): Observable<getContentResp> {
    return this.httpClient.get<getContentResp>(
      'api/Manage/New_News/GetNew_News_Content',
      {
        params: {
          new_menuitemId: id,
          lang: sessionStorage.getItem('lang') || 'zh',
        },
      }
    );
  }

  createUpdateContent(
    req: createUpdateContentReq
  ): Observable<createUpdateResp> {
    return this.httpClient.post<createUpdateResp>(
      'api/Manage/New_News/UpdateOrInsertNew_News_Content',
      req
    );
  }

  getPolicy(policy: string) {
    const functionPolicies = sessionStorage.getItem('functionPolicy');
    if (functionPolicies && functionPolicies.split(',').indexOf(policy) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 設定(條列式/圖片式/最新消息)排序的數字
   * @param req 
   * @returns 
   */
  setSort(req: setSortReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>('api/Manage/New_News/UpdateNewsSort', req);
  }

  /**
   * 排序項目功能
   * type = menu, news, video
   * @param req
   * @returns
   */
  changeSort(req: changeSortReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>('api/Manage/Video/VideoSort', req);
  }

  /**
   * 推動成果資訊
   * @returns
   */
  getAchievements(): Observable<getAchievementsResp> {
    return this.httpClient.get<getAchievementsResp>(
      'api/Achievements/GetAchievements',
      {
        params: {
          lang: this.getLang(),
        },
      }
    );
  }

  /**
   * 點擊次數統計
   * @param req
   * @returns
   */
  getClickLogList(req: getClickLogListReq): Observable<getClickLogListResp> {
    return this.httpClient.get<getClickLogListResp>(
      'api/Manage/WebSite/GetClickLogList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   * 點擊次數統計圖表
   * @returns 
   */
  getClickLogChart() {
    return this.httpClient.get<getClickLogChartResp>(
      'api/Manage/WebSite/GetClickLogChart',
    );
  }

  /**
   * 訪客人次統計
   * @param req
   * @returns
   */
  getGustViewLogList(
    req: getGustViewLogListReq
  ): Observable<getGustViewLogListResp> {
    return this.httpClient.get<getGustViewLogListResp>(
      'api/Manage/WebSite/GetWebSiteViewLogList',
      {
        params: {
          ...req,
        },
      }
    );
  }


  /**
   * 訪客人次統計圖表
   * @param req 
   * @returns 
   */
  getGustViewLogChart(
    req: getGustViewLogChartReq
  ): Observable<getGustViewLogChartResp> {
    return this.httpClient.get<getGustViewLogChartResp>(
      'api/Manage/WebSite/GetWebSiteViewLogChart',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   * 搜尋關鍵字統計
   * @param req
   * @returns
   */
  getSearchKeywordLogList(
    req: getSearchKeywordLogListReq
  ): Observable<getSearchKeywordLogListResp> {
    return this.httpClient.post<getSearchKeywordLogListResp>(
      'api/Manage/WordCloud/GetKeywordList',
      req
    );
  }

  /**
   * 搜尋關鍵字統計圖表
   * @returns
   */
  getSearchKeywordLogChart(): Observable<getSearchKeywordLogChartResp> {
    return this.httpClient.get<getSearchKeywordLogChartResp>(
      'api/Manage/WordCloud/GetKeywordChart',
      {
        params: {
          lang: this.getLang(),
        }
      }
    );
  }

  exportSearchKeywordLog() {
    return this.httpClient.post(
      'api/Manage/WordCloud/ExportKeywordExcel',
      {
        lang: this.getLang(),
      },
      {
        responseType: 'blob', // 告訴 HttpClient 要 blob
        observe: 'response', // 取得完整 HttpResponse 以抓 headers
      }
    );
  }

  /**
   *  後台操作紀錄
   * @param req
   * @returns
   */
  getAuditLogList(req: getAuditLogListReq): Observable<getAuditLogListResp> {
    return this.httpClient.get<getAuditLogListResp>(
      'api/Manage/AuditTrail/GetAuditTrailList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  /**
   *  後台操作紀錄圖表
   * @param req 
   * @returns 
   */
  getAuditLogListChart(req: getAuditLogChartReq): Observable<getAuditLogChartResp> {
    return this.httpClient.get<getAuditLogChartResp>(
      'api/Manage/AuditTrail/GetAuditTrailChart',
      {
        params: {
          ...req,
        },
      }
    );
  }

  getFilenameFromHeaders(response: HttpResponse<Blob>): string | undefined {
    const contentDisposition = response.headers.get('content-disposition');
    let filename: string | undefined;

    if (contentDisposition) {
      // 優先解析 filename* (UTF-8 編碼)
      const filenameStarMatch = contentDisposition.match(
        /filename\*=(?:UTF-8'')?([^;]+)/i
      );
      if (filenameStarMatch && filenameStarMatch[1]) {
        try {
          // 使用 decodeURIComponent 解析 URL 編碼
          filename = decodeURIComponent(filenameStarMatch[1]);
        } catch (e) {
          console.error('檔名解析失敗：', e);
          filename = filenameStarMatch[1];
        }
      } else {
        // 解析一般的 filename
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/i);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }
    }
    return filename;
  }

  /**
   * 設定關鍵字
   * @param id 
   * @param keyword 
   * @returns 
   */
  setKeyword(id: string, keyword: string): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>('api/Manage/MenuItem/UpdateMenuitemKeyword', {
      id: id,
      keyword: keyword,
    });
  }
}

.header-layout {
  width: 100vw;
  height: 100px;
  box-shadow: 0px 0px 10px 0px rgba(204, 204, 204, 1);
  background-color: #f1f1f1;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    margin: 0 20px;
    cursor: pointer;
    position: relative;

    img {
      width: 200px;
      max-height: 100px;
    }
  }

  .navigation {
    overflow-x: auto;
    display: block;
    white-space: nowrap;
    padding: 10px 0;

    .name {
      margin: 0 1em;
      color: #2eaddb;
      font-weight: bold;
      padding: 5px 0;
      cursor: pointer;
      transition: 0.3s ease-in-out;

      &:hover {
        text-shadow: 3px 3px 5px #ccc;
      }
    }

    /* width */
    &::-webkit-scrollbar {
      height: 3px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      background: transparent;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: transparent;
      border-radius: 10px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #2eaddb;
    }
  }

  .header-menu-button {
    width: 50px;
    height: 50px;
    margin: 0 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #add023;
    font-size: 10px;
    cursor: pointer;

    .line {
      height: 3px;
      width: 30px;
      background-color: #12a3d6;
      margin: 3px 0;
      transition: 0.3s ease-in-out;
    }

    .menu-title {
      transition: 0.3s ease-in-out;
    }

    &:hover {
      .line {
        &.top {
          transform: rotate3d(0, 0, 1, -45deg) translateX(-5px);
        }

        &.mid {
          transform: rotate3d(0, 0, 1, 30deg);
        }

        &.bottom {
          transform: rotate3d(0, 0, 1, -45deg) translateX(5px);
        }
      }

      .menu-title {
        transform: scale(1.2, 1.2);
      }
    }
  }
}

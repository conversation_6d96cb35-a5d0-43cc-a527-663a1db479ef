import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from '@angular/forms';
import { AnalysisService } from '../../../../core/services/analysis.service';
import { PagingOfLoginHistoryItem } from '../../../../shared/models/pagingOfLoginHistoryItem.model';
import Swal from 'sweetalert2';
import { UserDataService } from '../../../../core/services/userData.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { Config } from '../../../../core/services/config';
import { environment } from '../../../../../environments/environment';
import { AuthService } from '../../../../core/services/auth.service';
import { defaultItem } from '../../../../interface/share.interface';
import { googleLoginResp } from '../../../../shared/models/loginInfo.model';
import { HttpErrorResponse } from '@angular/common/http';

// declare var setWebSiteToken: any;
declare var getWebSiteHeaders: any;

@Component({
  selector: 'app-auth',
  standalone: false,
  templateUrl: './auth.component.html',
  styleUrl: './auth.component.scss',
})
export class AuthComponent {
  // @Output() popupOutput = new EventEmitter();
  form: FormGroup;
  loading = false;
  FailCount = 0;
  Account: string = '';
  EndTime = 0;

  captchaUrl: string = '/api/Captcha/GetCaptcha';

  private verifyCode: any;

  constructor(
    private _fb: FormBuilder,
    private _analysis: AnalysisService,
    private _userDataService: UserDataService,
    private dialogRef: MatDialogRef<DialogComponent>,
    private authService: AuthService
  ) {
    let password = environment.production ? '' : environment.pd;
    this.form = this._fb.group({
      account: [null, Validators.required],
      password: [password, Validators.required],
      reCAPTCHA: [null, Validators.required],
    });
  }

  ngOnInit(): void {}

  signInWithGoogle() {
    this.authService.GooglePopup().subscribe({
      next: (res: string) => {
        this.googleLogin(res);
      },
      error: (error) => {
        console.error(error);
      },
    });
  }

  refreshCaptcha() {
    this.captchaUrl = `/api/Captcha/GetCaptcha?t=${new Date().getTime()}`;
  }

  googleLogin(token: string) {
    this._userDataService.googleLogin(token).subscribe({
      next: (res: googleLoginResp) => {
        if (res.code === 200) {
          sessionStorage.setItem('token', res.data);
          Config.defaultOptions = {
            headers: null,
          };
          // 顯示登入成功提示
          Swal.fire('登入成功', '', 'success').then(() => {
            location.reload();
          });
          this.loading = false;
          this.dialogRef.close(res); // 關閉對話框
        } else {
          Swal.fire('登入失敗', `${res.message}`, 'error');
          this.loading = false;
        }
      },
      error: (error: HttpErrorResponse) => {
        // 失敗的處理
        Swal.fire('登入失敗', `${error.error.message}`, 'error');
        this.loading = false;
      },
    });
  }

  submit(formVal: any) {
    this.loading = true;
    this.Account = formVal.account;
    this._userDataService
      .login4(
        {
          account: this.form.value.account,
          password: this.form.value.password,
          reCAPTCHA: this.form.value.reCAPTCHA,
        },
        sessionStorage.getItem('webSiteId')!
      )
      .subscribe({
        next: (res) => {
          // 成功的處理
          sessionStorage.setItem('token', res);
          Config.defaultOptions = {
            headers: null,
          };
          // 顯示登入成功提示
          Swal.fire('登入成功', '', 'success').then(() => {
            location.reload();
          });
          this.loading = false;
          this.dialogRef.close(res); // 關閉對話框
        },
        error: (error: HttpErrorResponse) => {
          console.error(error);
          // 失敗的處理
          Swal.fire('登入失敗', error.error.message, 'error').then(() => {
            this.loading = false;
            this.refreshCaptcha();
          });
        },
      });
  }
}

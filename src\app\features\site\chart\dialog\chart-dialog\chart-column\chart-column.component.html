<form [formGroup]="customerChartForm">
    <div style="display: flex;align-items: center;justify-content: left;margin:-2em 2em 2em 2em;">
        <mat-form-field style="margin-right: 2em;width: 50%;" appearance="outline">
            <mat-label>選擇資料來源</mat-label>
            <mat-select formControlName="datasetId" (selectionChange)="selectDataset($event)">
                @for (item of datasetList ; track item) {
                <mat-option [value]="item.datasetId"> {{item.name}}</mat-option>
                }
            </mat-select>
        </mat-form-field>
        <mat-form-field style="width: 50%;" appearance="outline">
            <mat-label>選擇圖表類型</mat-label>
            <mat-select formControlName="chartType" (selectionChange)="selectChartType($event)">
                @for ( item of chartTypeOptionList ; track item) {
                <mat-option [value]="item.value">
                    {{item.viewValue}}
                </mat-option>
                }
            </mat-select>
        </mat-form-field>
    </div>
    <div style="display: flex;align-items: center;justify-content: left;margin: 2em;">
        <mat-form-field style="width: 50%; margin-right: 2em;" appearance="outline">
            <mat-label>計算列(X軸)</mat-label>
            <input matInput formControlName="xAxis" placeholder="計算列(X軸)" readonly>
        </mat-form-field>
        <mat-form-field style=" width: 50%;" appearance="outline">
            <mat-label>選擇計算列(Y軸)</mat-label>
            <mat-select multiple formControlName="yAxis" (selectionChange)="selectChartYAxis($event,'multiple')">
                @for (item of datasetYItemList; track item) {
                <mat-option [value]="item.datasetYId">
                    {{item.nameY}}
                </mat-option>
                }
            </mat-select>
        </mat-form-field>
    </div>

    @if(calculateColumnList.length>0){
    <!-- @if(checkChartTypeUnitConfig(customerChartForm.value.chartType)){
    <div style="display: flex;align-items: center;justify-content: left;margin: 2em;">
        <mat-expansion-panel style="border: solid #9E9E9E 0.5px; box-shadow:none ;" class="w-100"
            (opened)="openSettingPanel = true" (closed)="openSettingPanel = false">
            <mat-expansion-panel-header>
                <mat-panel-title>單位設定</mat-panel-title>
            </mat-expansion-panel-header>
            <div style="display: flex;align-items: center;justify-content: left;">
                <mat-form-field style="margin-right: 2em; width: 50%;" appearance="outline">
                    <mat-label>單位</mat-label>
                    <mat-select formControlName="unit" (selectionChange)="checkCustomerChart()">
                        @for (option of Unit; track $index) {
                        <mat-option [value]="option.value">{{ option.label }}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <div style="display: flex;align-items: center;justify-content: left;">
                    <mat-form-field style="width:85%;" appearance="outline">
                        <mat-label>單位文字:</mat-label>
                        <input type="text" matInput formControlName="unitLabel" placeholder="ex:元"
                            (blur)="checkCustomerChart(false,true)">
                    </mat-form-field>
                </div>
            </div>
        </mat-expansion-panel>
    </div>
    } -->
    }
    <!-- <div style="display: flex;align-items: center;justify-content: left;margin: 2em;">
        <mat-expansion-panel style="border: solid #9E9E9E 0.5px; box-shadow:none ;" class="w-100"
            (opened)="openSettingPanel = true" (closed)="openSettingPanel = false">
            <mat-expansion-panel-header>
                <mat-panel-title>顯示方式</mat-panel-title>
            </mat-expansion-panel-header>
            <div style="display: flex;align-items: center;justify-content: left;">
                <mat-form-field style="margin-right: 2em; width: 50%;" appearance="outline">
                    <mat-label>圖例配置</mat-label>
                    <mat-select formControlName="chartLegendSettingValues"
                        (selectionChange)="checkCustomerChart(false,true)">
                        @for (option of chartLegendTypeSelectOptions; track $index) {
                        <mat-option [value]="option.value">{{ option.viewValue }}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <mat-form-field style="margin-right: 2em; width: 50%;" appearance="outline">
                    <mat-label>文字顯示方式</mat-label>
                    <mat-select formControlName="chartSeriesLabelSettingValues"
                        (selectionChange)="checkCustomerChart(false,true)">
                        @for (option of chartSeriesLabelPositionSelectOptions; track $index) {
                        <mat-option [value]="option.value">{{ option.viewValue }}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <div style="display: flex;align-items: center;justify-content: left;">
                    <mat-form-field style="width:85%;" appearance="outline">
                        <mat-label>字體大小:</mat-label>
                        <input type="number" matInput formControlName="chartSeriesLabelSettingFontSize"
                            placeholder="字體大小" (blur)="checkCustomerChart(false,true)">
                    </mat-form-field>
                </div>
            </div>
            @if(checkChartTypeBarWidthConfig(customerChartForm.value.chartType)){
            <div style="display: flex;align-items: center;justify-content: left;">
                <mat-form-field style="width:28%;" appearance="outline">
                    <mat-label>柱體寬度:</mat-label>
                    <input type="number" matInput formControlName="barChartBarWidth" placeholder="ex:最大輸入50,如果不輸入為auto"
                        max="50" (keydown)="checkInput($event)" (input)="onInputLimit($event)"
                        (keydown)="checkCustomerChart(false,true)">

                </mat-form-field>
            </div>
            }
        </mat-expansion-panel>
    </div> -->
</form>
import { Component, OnInit, Simple<PERSON>hang<PERSON>, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { contentData } from '../../../interface/editor.interface';
import {
  createUpdateContentReq,
  createUpdateResp,
  getContentResp,
} from '../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { v4 as uuidv4 } from 'uuid';
import { EDITORTYPE } from '../../../enum/editor.enum';
import { ApprovalStatus, METHODSTATUS } from '../../../enum/share.enum';
import { ContentEditorComponent } from '../../components/content-editor/content-editor.component';

@Component({
  selector: 'app-content',
  standalone: false,
  templateUrl: './content.component.html',
  styleUrl: './content.component.scss',
})
export class ContentComponent implements OnInit {
  @ViewChild(ContentEditorComponent)
  ContentEditorComponent: ContentEditorComponent | undefined;
  title: string = '內容編輯';
  loading: boolean = false;
  menuItemId: string = '';
  contentData: contentData[] = [];

  createUser: string = '';
  editUser: string = '';
  methodStatus: METHODSTATUS = METHODSTATUS.ADD;
  levelDecision: number = 2;
  isPendingApproval: boolean = false;
  typeGroupId: string = '';
  reason: string = '';
  reviewFileName: string = '';
  reviewFileUrl: string = '';

  constructor(
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.activatedRoute.url.subscribe(() => {
      this.menuItemId = this.activatedRoute.snapshot.params['menuItemId'];
      this.getContent();
    });
  }

  getContentData(contentData: contentData[]) {
    this.contentData = contentData;
  }

  getContent() {
    this.contentData = [];
    this.loading = true;
    this.shareService.getContent(this.menuItemId).subscribe({
      next: (resp: getContentResp) => {
        this.loading = false;
        this.title = resp.data.titleName;
        if (
          resp.data.new_NewsTagDatas &&
          resp.data.new_NewsTagDatas.length > 0
        ) {
          this.levelDecision = resp.data.levelDecision;
          this.typeGroupId = resp.data.typeGroupId;
          this.isPendingApproval = resp.data.isPendingApproval;
          this.methodStatus = METHODSTATUS.EDIT;
          resp.data.new_NewsTagDatas.map((item) => {
            this.contentData.push({
              id: uuidv4(),
              type: item.tagName as EDITORTYPE,
              data: item.tagData,
            });
          });
          if (this.ContentEditorComponent) {
            this.ContentEditorComponent.initializeForm(this.contentData);
          }
          this.createUser = resp.data.creater;
          this.editUser = resp.data.editor;
          this.reason = resp.data.reason;
          this.reviewFileName = resp.data.reviewFileName;
          this.reviewFileUrl = resp.data.reviewFileUrl;
        } else {
          if (this.ContentEditorComponent) {
            this.ContentEditorComponent.initializeForm(this.contentData);
          }
          this.ContentEditorComponent!.addEditorBlock('content');
        }
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  save() {
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,

      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateContentReq = {
          menuitemId: this.menuItemId,
          new_NewsTagDatas: newsData,
          titleName: '',
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
        };
        this.loading = true;
        this.shareService.createUpdateContent(req).subscribe({
          next: (resp: createUpdateResp) => {
            this.loading = false;
            Swal.fire('成功', `儲存成功`, 'success');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', `${err.error.message}`, 'error');
          },
        });
      }
    });
  }

  view() {
    if (this.isPendingApproval) {
      this.router.navigate(['manage/view'], {
        queryParams: {
          menuItemId: this.menuItemId,
          typeGroupId: this.typeGroupId,
          type: 'Content',
          status: ApprovalStatus.ViewApproval,
        },
      });
      return;
    }
    Swal.fire({
      html: `
      <div style="font-size: 1.5em; font-weight: bold;">請確認內文編部分已符合無障礙AA規範</div>
      <div style="margin-top: 8px;">請確認貼近內文區域文字是⌜已貼上純文字⌟貼上</div>
    `,
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        let newsData: { tagName: string; dataString: string | null }[] = [];
        this.contentData.map((item) => {
          newsData.push({
            tagName: item.type,
            dataString: item.data ? JSON.stringify(item.data) : null,
          });
        });
        let req: createUpdateContentReq = {
          menuitemId: this.menuItemId,
          new_NewsTagDatas: newsData,
          titleName: '',
          levelDecision: this.levelDecision,
          lang: sessionStorage.getItem('lang') || 'zh',
        };
        this.loading = true;
        this.shareService.createUpdateContent(req).subscribe({
          next: (resp: createUpdateResp) => {
            this.loading = false;
            this.router.navigate(['manage/view'], {
              queryParams: {
                menuItemId: this.menuItemId,
                typeGroupId: resp.data,
                type: 'Content',
                status: ApprovalStatus.BeforeApproval,
              },
            });
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', `${err.error.message}`, 'error');
          },
        });
      }
    });
  }
}

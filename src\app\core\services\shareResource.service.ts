import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';
import {
  createUpdateShareResourceReq,
  getShareResourceListReq,
  getShareResourceListResp,
} from '../../interface/shareResource.interface';

@Injectable({
  providedIn: 'root',
})
export class ShareResourceService {
  constructor(private httpClient: HttpClient) {}

  getShareResourceList(
    req: getShareResourceListReq
  ): Observable<getShareResourceListResp> {
    return this.httpClient.get<getShareResourceListResp>(
      'api/Manage/ShareResource/GetShareResourceList',
      {
        params: {
          ...req,
        },
      }
    );
  }

  deleteShareResource(id: string) {
    return this.httpClient.delete(
      'api/Manage/ShareResource/DeleteShareResource',
      {
        params: {
          shareResourceFileId: id,
        },
      }
    );
  }

  createUpdateShareResource(
    req: createUpdateShareResourceReq
  ): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/Manage/ShareResource/InsertOrUpdateShareResource',
      req
    );
  }
}

import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  auditLogItem,
  getAuditLogListReq,
  getAuditLogListResp,
} from '../../../interface/share.interface';
import { MatPaginator } from '@angular/material/paginator';

@Component({
  selector: 'app-audit-log',
  standalone: false,

  templateUrl: './audit-log.component.html',
  styleUrl: './audit-log.component.scss',
})
export class AuditLogComponent {
  @ViewChild('contentPaginator', { static: true })
  contentPaginator!: MatPaginator;
  lang: string = '中文';
  loading: boolean = false;
  menuItemId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  keyword: string = '';
  startDate: string = '';
  endDate: string = '';

  auditLogList: auditLogItem[] = [];

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.getAuditLogList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getAuditLogList();
  }

  getAuditLogList() {
    let req: getAuditLogListReq = {
      startTime: this.startDate,
      endTime: this.endDate,
      keyword: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.shareService.getAuditLogList(req).subscribe({
      next: (resp: getAuditLogListResp) => {
        this.loading = false;
        this.auditLogList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.startDate = '';
    this.endDate = '';
    this.nowPage = 1;
    this.getAuditLogList();
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getAuditLogList();
  }
}

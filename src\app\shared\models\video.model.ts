﻿export class Video {
  videoUrl?: string | null;
  previewImageUrl?: string;
  time?: number;
  viewCount?: number;
  id?: string;
  menuItemId?: string;
  type?: string;
  description?: string;
  videoDataId?: string | null;
  sort?: number;
  onHomepage?: boolean;
  enable?: boolean;
  youtubeUrl?: string | null;
  title?: string;
  coverDataId?: string;
  meta?: string;
  videoContent?: VideoContent[];
  videoDocument?: VideoDocument[];
}

export class VideoContent {
  id?: string;
  videoId?: string;
  title?: string;
  content?: string;
  sort?: number;
}

export class VideoDocument {
  documentUrl?: string;
  id?: string;
  videoId?: string;
  fileName?: string;
  documentDataId?: string;
  sort?: number;
  size?: number;
}

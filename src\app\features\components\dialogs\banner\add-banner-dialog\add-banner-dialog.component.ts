import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../../../file-box/file-box.component';
import {
  Banner,
  createrorUpdateBannerReq,
} from '../../../../../shared/models/banner.model';
import { BannerService } from '../../../../../core/services/banner.service';
import { ShareService } from '../../../../../core/services/share.service';
import { defaultItem } from '../../../../../interface/share.interface';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-banner-dialog',
  standalone: false,

  templateUrl: './add-banner-dialog.component.html',
  styleUrl: './add-banner-dialog.component.scss',
})
export class AddBannerDialogComponent {
  loading = false;
  status: string = '新增';
  id: string = '';
  bannerDataId: string = '';
  type: 'Image' | 'Video' = 'Image';
  cover: string = '';
  coverName: string = '';
  coverFile?: File;
  link: string = '';
  title: string = '';
  enable: boolean = true;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      status: string;
      title: number;
      menuItemId: string;
      item?: Banner;
    },
    private dialogRef: MatDialogRef<DialogComponent>,
    public dialog: MatDialog,
    private bannerService: BannerService,
    private shareService: ShareService
  ) {
    this.status = this.data.status;
    if (this.data.item) {
      console.log(this.data.item);
      this.id = this.data.item.id;
      this.cover = this.data.item.url;
      this.bannerDataId = this.data.item.bannerDataId;
      this.title = this.data.item.title;
      this.link = this.data.item.link;
      this.enable = this.data.item.enable;
      this.type = this.data.item.type as 'Image' | 'Video';

    }
  }

  ngOnInit(): void { }



  selectGalleryImage() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          type2: 'Video',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          console.log(resp);
          this.type = resp.type as 'Image' | 'Video';
          this.cover = resp.data.url;
          this.bannerDataId = resp.data.dataId;
        }
      });
  }

  create() {
    let status: string = this.id ? '編輯' : '新增';
    let req: createrorUpdateBannerReq = {
      id: this.id,
      bannerDataId: this.bannerDataId,
      title: this.title,
      link: this.link,
      type: this.type,
      enable: this.enable,
      lang: this.shareService.getLang(),
    };

    this.loading = true;
    this.bannerService.createrorUpdateBanner(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', `${status}成功`, 'success').then(() => {
            this.dialogRef.close(true);
          });
        } else {
          Swal.fire('失敗', `${status}失敗`, 'error');
        }
      },
      error: () => {
        this.loading = false;
        Swal.fire('失敗', `${status}失敗`, 'error');
      },
    });
  }
  close() {
    this.dialogRef.close(false);
  }
}

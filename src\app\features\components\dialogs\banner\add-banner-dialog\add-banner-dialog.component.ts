import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';
import { DialogComponent } from '../../../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../../../file-box/file-box.component';
import { CropperImageComponent } from '../../cropper-image/cropper-image.component';
import {
  Banner,
  createrBannerReq,
} from '../../../../../shared/models/banner.model';
import { BannerService } from '../../../../../core/services/banner.service';
import DOMPurify from 'dompurify';

export enum DeviceType {
  PC = 'pc',
  MOBLIE = 'moblie',
}
@Component({
  selector: 'app-add-banner-dialog',
  standalone: false,

  templateUrl: './add-banner-dialog.component.html',
  styleUrl: './add-banner-dialog.component.scss',
})
export class AddBannerDialogComponent {
  loading = false;
  status: string = '新增';
  croppedFile?: Blob;
  croppedImage?: string;
  cover: string = '';
  bannerId: string = '';
  coverName: string = '';
  coverFile?: File;
  coverId: string = '';
  croppedId: string = '';
  content: string = '';
  meta: string = '';
  deviceType = DeviceType;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      status: string;
      title: number;
      menuItemId: string;
      item?: Banner;
    },
    private dialogRef: MatDialogRef<DialogComponent>,
    public dialog: MatDialog,
    private bannerService: BannerService
  ) {
    this.status = this.data.status;
    if (this.data.item) {
      this.bannerId = this.data.item.id;
      this.cover = this.data.item.pcUrl;
      this.coverId = this.data.item.bannerDataId;
      this.croppedId = this.data.item.bannerMobileDataId;
      this.croppedImage = this.data.item.mobileURL;
      this.meta = this.data.item.meta;
      this.content = this.data.item.bannerContent;
    }
  }

  ngOnInit(): void {}

  selectGalleryImage(deviceType: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          if (deviceType === DeviceType.PC) {
            this.cover = resp.data.url;
            this.coverId = resp.data.dataId;
          } else {
            this.croppedImage = resp.data.url;
            this.croppedId = resp.data.dataId;
          }
        }
      });
  }
  cuteBannerImg() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: CropperImageComponent,
          fileId: this.croppedId,
          fileName: this.coverName,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.croppedFile = resp.file;
          this.croppedImage = resp.image;
        }
      });
  }

  create() {
    let req: createrBannerReq = {
      bannerId: this.bannerId,
      websiteId: sessionStorage.getItem('webSiteId')!,
      pcDataId: this.coverId,
      mobileDataId: this.croppedId,
      mobileFile: this.croppedFile,
      uploadType: this.croppedFile ? 'cut' : 'dataId',
      content: DOMPurify.sanitize(this.content),
      url: DOMPurify.sanitize(this.meta),
    };
    this.bannerService.createrBanner(req).subscribe({
      next: () => {
        this.dialogRef.close(true);
      },
      error: () => {},
    });
  }
  close() {
    this.dialogRef.close(false);
  }
}

import { Component, Inject } from '@angular/core';
import { FileBoxComponent } from '../file-box/file-box.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { LinkService } from '../../../core/services/link.service';
import { LangService } from '../../../core/services/lang.service';

@Component({
  selector: 'app-link-edit',
  standalone: false,

  templateUrl: './link-edit.component.html',
  styleUrl: './link-edit.component.scss'
})
export class LinkEditComponent {
  imageFile: any;
  form!: FormGroup;
  loading: boolean = false;
  popupData: any;
  selectedLang: any = 'null';

  constructor(
    private _fb: FormBuilder,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<DialogComponent>,
    private _linkService: LinkService,
    private _langService: LangService,
  ) {

  }

  ngOnInit(): void {
    this.popupData = this.data.data;
    this.initForm();
    if (this.popupData) {
      Object.keys(this.popupData).map(property => {
        if (this.form.get(property)) {
          this.form.get(property)?.setValue(this.popupData[property]);
        }
      });
      this.imageFile = {
        previewImageUrl: this.popupData.coverUrl,
      };
    }
  }

  initForm() {
    this.form = this._fb.group({
      id: '',
      name: ['', Validators.required],
      url: ['', Validators.required],
      coverDataId: '',
      enable: true,
      onHomepage: true,
      coverUrl: '',
      menuItemId: '',
      sort: null
    });
  }

  /** 選擇圖片 */
  selectImage() {
    const fileBoxDialog = this.dialog.open(DialogComponent, {
      data: {
        width: '1000px',
        height: '500px',
        contentTemplate: FileBoxComponent,
        data: {
          type: 'Image'
        }
      }
    });

    fileBoxDialog.afterClosed().subscribe((res) => {
      if (res) {
        this.imageFile = res.data;
        this.form.get('coverDataId')?.setValue(this.imageFile.dataId);
        this.form.get('coverUrl')?.setValue(this.imageFile.previewImageUrl);
      }
    });
  }

  setForm(value: any) {
    Object.keys(value).map(property => {
      if (this.form.get(property)) {
        this.form.get(property)?.setValue(value[property]);
      }
    });
    this.imageFile = {
      previewImageUrl: value.coverUrl
    };
  }

  submit(formVal: any) {
    if (!this.popupData) {
      this.dialogRef.close(formVal);
    } else {
      this.loading = true;
      let obs$: Observable<any>;
      obs$ = this.selectedLang === 'null' ?
        this._linkService.update(formVal) :
        this._langService.createOrUpdate(formVal.id, this.selectedLang['code'], formVal);
      obs$.subscribe(x => {
        this.setForm(x);
        this.loading = false;
        this.dialogRef.close();
      });
    }
  }
}

import { Role } from './role.model';

export interface UserData {
  id?: string;
  webSiteId?: string;
  account?: string;
  password?: string;
  name?: string;
  type?: string;
  enable?: boolean;
  gender?: string;
  roleId?: string;
  idNumber?: string;
  birthday?: number;
  memberId?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  emailBackup?: string;
  addressZip?: string;
  address?: string;
  ethnic?: string;
  constellation?: string;
  jobTitle?: string;
  industry?: string;
  education?: string;
  school?: string;
  role?: Role;
  selected?: boolean;
}

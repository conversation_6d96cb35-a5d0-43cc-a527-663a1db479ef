import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { linkEditorDialog } from '../../../../shared/models/dialog.model';
import Swal from 'sweetalert2';
import {
  getHtmlZipFileResp,
  HtmlService,
} from '../../../../core/services/html.service';
import { defaultItem } from '../../../../interface/share.interface';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-html-zip-editor',
  standalone: false,

  templateUrl: './html-zip-editor.component.html',
  styleUrl: './html-zip-editor.component.scss',
})
export class HtmlZipEditorComponent {
  url: string = '';
  menuItemId: string = '';
  fileName: string = '';
  file: File | null = null;
  loading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<HtmlZipEditorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: linkEditorDialog,
    private htmlService: HtmlService
  ) { }

  ngOnInit(): void {
    this.menuItemId = this.data.dataContent!.id!;
    this.url = this.data.dataContent!.meta!;
    this.getHtmlZipFile();
  }

  getHtmlZipFile() {
    this.htmlService.getHtmlZipFile(this.menuItemId).subscribe({
      next: (resp: getHtmlZipFileResp) => {
        this.fileName = resp.data;
      },
    });
  }

  selectGalleryFile(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    const maxFileSize = 300 * 1024 * 1024;
    if (file.size > maxFileSize) {
      Swal.fire('檔案大小超過 300MB，請重新選擇', '', 'warning');
      this.loading = false;
      return;
    }
    if (
      file.type !== 'application/zip' &&
      file.type !== 'application/x-zip-compressed'
    ) {
      Swal.fire('請選擇zip', '', 'warning');
      this.loading = false;
      return;
    }
    this.file = file;
    this.fileName = file.name;
    this.loading = false;
  }

  submit() {
    if (!this.file) {
      return;
    }
    this.loading = true;
    this.htmlService.uploadHtmlZipFile(this.menuItemId, this.file).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code === 200) {
          Swal.fire('成功', '上傳成功', 'success');
          this.dialogRef.close(this.data.dataContent);
        } else {
          Swal.fire('失敗', '上傳失敗', 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }
}

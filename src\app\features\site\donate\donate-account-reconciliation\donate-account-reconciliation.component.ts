import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { DonateService } from '../../../../core/services/donate.service';
import { ShareService } from '../../../../core/services/share.service';
import {
  donateAccountReconciliationItem,
  exportDonateAccountReconciliationListReq,
  getDonateAccountReconciliationListReq,
  getDonateAccountReconciliationListResp,
} from '../../../../interface/donate.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import { DonateAccountReconciliationItemDialogComponent } from './donate-account-reconciliation-item-dialog/donate-account-reconciliation-item-dialog.component';

@Component({
  selector: 'app-donate-account-reconciliation',
  standalone: false,

  templateUrl: './donate-account-reconciliation.component.html',
  styleUrl: './donate-account-reconciliation.component.scss',
})
export class DonateAccountReconciliationComponent {
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  donateItemList: donateAccountReconciliationItem[] = [];
  keyword: string = '';
  startDate: string = '';
  endDate: string = '';

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService,
    private donateService: DonateService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDonateItemList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getDonateItemList();
  }

  getDonateItemList() {
    let req: getDonateAccountReconciliationListReq = {
      startDate: this.startDate,
      endDate: this.endDate,
      donateItem: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.loading = true;
    this.donateService.getDonateAccountReconciliationList(req).subscribe({
      next: (resp: getDonateAccountReconciliationListResp) => {
        this.loading = false;
        this.donateItemList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.startDate = '';
    this.endDate = '';
    this.nowPage = 1;
    this.getDonateItemList();
  }

  add() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: DonateAccountReconciliationItemDialogComponent,
          showHeader: true,
          title: '新增',
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getDonateItemList();
      });
  }

  edit(item: donateAccountReconciliationItem) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: DonateAccountReconciliationItemDialogComponent,
          showHeader: true,
          title: '編輯',
          data: item,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getDonateItemList();
      });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.donateService.deleteDonateAccountReconciliation(id).subscribe({
          next: (resp: defaultItem) => {
            this.loading = false;
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                  this.getDonateItemList();
                })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  checkChange(item: donateAccountReconciliationItem) {
    this.donateService
      .isCheckDonateAccountReconciliation(item.donateInfoId)
      .subscribe({
        next: (resp: defaultItem) => {
          if (resp.code !== 200) {
            Swal.fire('失敗', resp.message, 'error');
            item.isCheck = !item.isCheck;
          } else {
            item.isCheck = !item.isCheck;
          }
        },
        error: (err: HttpErrorResponse) => {
          Swal.fire('失敗', err.error.message, 'error');
          item.isCheck = !item.isCheck;
        },
      });
  }
  publishChange(item: donateAccountReconciliationItem) {
    this.donateService
      .isPublishDonateAccountReconciliation(item.donateInfoId)
      .subscribe({
        next: (resp: defaultItem) => {
          if (resp.code !== 200) {
            Swal.fire('失敗', resp.message, 'error');
            item.isPublish = !item.isPublish;
          } else {
            item.isPublish = !item.isPublish;
          }
        },
        error: (err: HttpErrorResponse) => {
          Swal.fire('失敗', err.error.message, 'error');
          item.isPublish = !item.isPublish;
        },
      });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getDonateItemList();
  }

  downloadTemplate() {
    window.open('assets/files/銷帳作業匯入範例檔.xlsx', '_blank');
  }

  importExcel(event: Event) {
    const file = (event.target as HTMLInputElement).files![0];
    if (!file) {
      return;
    }
    this.loading = true;
    const maxFileSize = 30 * 1024 * 1024;
    if (file.size > maxFileSize) {
      Swal.fire('檔案大小超過 30MB，請重新選擇', '', 'warning');
      this.loading = false;
      return;
    }
    if (
      file.type !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      Swal.fire('請選擇xlsx', '', 'warning');
      this.loading = false;
      return;
    }
    this.donateService.importDonateAccountReconciliationList(file).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        resp.code === 200
          ? Swal.fire('成功', '匯入成功', 'success').then(() => {
              this.getDonateItemList();
            })
          : Swal.fire('失敗', resp.message, 'error');
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }

  export() {
    this.loading = true;
    let req: exportDonateAccountReconciliationListReq = {
      startDate: this.startDate,
      endDate: this.endDate,
      donateItem: this.keyword,
    };
    this.donateService.exportDonateAccountReconciliationList(req).subscribe({
      next: (resp: HttpResponse<Blob>) => {
        this.loading = false;
        if (!resp.body) {
          Swal.fire('失敗', '沒有檔案可匯出', 'error');
          return;
        }

        // 使用新服務來取得檔名
        const filename = this.shareService.getFilenameFromHeaders(resp);

        const url = window.URL.createObjectURL(resp.body); // resp.body 已確認非 null
        const a = document.createElement('a');
        a.href = url;
        if (filename) {
          a.download = filename;
        }
        a.click();
        window.URL.revokeObjectURL(url);
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', '匯出失敗', 'error');
      },
    });
  }
}

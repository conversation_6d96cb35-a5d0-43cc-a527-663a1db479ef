import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GeoLocation } from '../../shared/models/geoLocation.model';
import { Observable } from 'rxjs';
import { EnumTypeInfo } from '../../shared/models/enumTypeInfo.model';

@Injectable({
  providedIn: 'root'
})
export class GeoLocationService {
  private mngGeoApi: string = '/api/Manage/GeoLocation';

  constructor(
    private http: HttpClient
  ) { }

  /**
     * 取得指定選單項目的地理位置列表
     *
     * @param menuItemId 選單項目唯一識別號
     * @param enable 啟用停用過濾
     */
  list2(
    menuItemId?: string,

    enable?: boolean
  ): Observable<GeoLocation[]> {
    let url = '/api/Manage/GeoLocation';
    const queryList = [];

    if (menuItemId !== null && menuItemId !== undefined) {
      queryList.push('menuItemId=' + encodeURIComponent(menuItemId.toString()));
    }

    if (enable !== null && enable !== undefined) {
      queryList.push('enable=' + encodeURIComponent(enable.toString()));
    }

    if (queryList.length > 0) {
      url += '?' + queryList.join('&');
    }

    return this.http.get<GeoLocation[]>(url);
  }

  /**
     * 建立實例
     *
     * @param instance 實例內容
     */
  create(
    instance: GeoLocation
  ): Observable<GeoLocation> {
    return this.http.post<GeoLocation>(`${this.mngGeoApi}`, instance);
  }

  /**
     * 更新實例
     *
     * @param instance 實例內容
     */
  update(
    instance: GeoLocation
  ): Observable<GeoLocation> {
    let url = '/api/Manage/GeoLocation';

    return this.http.put<GeoLocation>(this.mngGeoApi, instance);
  }

  /**
     * 取得可用的地圖提供者
     *
     */
  getRoleFunctionPolicy(): Observable<EnumTypeInfo[]> {

    return this.http.get<EnumTypeInfo[]>(`${this.mngGeoApi}/geoProvider`);
  }
}

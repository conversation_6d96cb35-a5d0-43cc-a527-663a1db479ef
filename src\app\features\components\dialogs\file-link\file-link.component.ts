import { Component, Inject } from '@angular/core';
import { fileData, videoItem } from '../../../../interface/editor.interface';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';

@Component({
  selector: 'app-file-link',
  standalone: false,

  templateUrl: './file-link.component.html',
  styleUrl: './file-link.component.scss',
})
export class FileLinkComponent {
  title: string = '';
  linkUrl: string = '';
  loading = false;


  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      status: string;
      title: number;
      menuItemId: string;
      menuItemType: string;
      item?: videoItem;
    },
    private dialogRef: MatDialogRef<FileLinkComponent>,
    public dialog: MatDialog
  ) {}

  create() {
    let req: fileData = {
      linkUrl: this.linkUrl,
      fileName: this.title,
    };
    this.dialogRef.close(req);
    
  }

  close() {
    this.dialogRef.close();
  }
}

.link-edit-layout {
  position: relative;

  .contents {
    margin: 1em 0;
    display: flex;
    flex-direction: column;
    width: 100%;

    .block {
      margin: 5px 0;
      display: flex;
      flex-direction: column;
      padding: 10px;
      background-color: #cccccc33;

      &.row {
        flex-direction: row;
        align-items: center;
      }

      .title {
        line-height: 2;
      }

      input {
        font-size: 25px;
        line-height: 1.5;
        outline: 0;
        background-color: #ffffff;
        border: 1px solid #868585;
        padding: 0 10px;

        &:focus {
          border: 1px solid #3f51b5;
        }
      }

      .mat-slide-toggle {
        margin: 0 1em;
      }

      .select-image {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .preview {
          width: 200px;
          height: 100px;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          margin: 10px 0;
          border: 4px solid #ccc;
        }
      }
    }
  }

  .btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

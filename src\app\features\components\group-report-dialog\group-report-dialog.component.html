<div class="contents">
    <div class="block">
        <div class="list-container">
            <div class="user-search">
                <div>
                    <span>關鍵字 :&nbsp;</span>
                    <mat-form-field appearance="outline">
                        <input matInput type="text" [(ngModel)]="keyword">
                    </mat-form-field> &nbsp; &nbsp;
                    <button mat-flat-button (click)="searchlist()">搜尋</button>
                </div>
            </div>
        </div>
        <div class="table-container">
            @if(menuType == 1){
            <div class="tabs-container">
                @for (item of typeList; track item) {
                <div class="tab" [class.active]="item.typeValue === type" (click)="searchlist(item.typeValue)">
                    {{ item.typeName }}
                </div>
                }
            </div>
            }@else{
            <div class="tabs-container">
                <div class="tab " [class.active]="groupReportRecruitStatus ==='1'" (click)="searchlist('1')">
                    徵才公告
                </div>
                <div class="tab " [class.active]="groupReportRecruitStatus === '2'" (click)="searchlist('2')">
                    甄選結果
                </div>
            </div>
            }
            <table class="review-table">
                <thead>
                    <tr>
                        <th width="60px"><input type="checkbox" id="select-all" (change)="selectAll($event)"
                                [checked]="selectAllStatus">全選</th>
                        <th width="100px">主題</th>
                        <th width="70px">發布時間</th>
                    </tr>
                </thead>
                <tbody>
                    @for (item of groupReportList; track item) {
                    <tr>
                        <td data-label="選取"><input type="checkbox" (change)="changeSelect($event,item)"
                                [checked]="isSelected(item)">
                        </td>
                        <td data-label="主題">{{item.title}}</td>
                        <td data-label="發布時間">{{item.publishDateTime|date:'yyyy-MM-dd'}}</td>
                    </tr>
                    }@empty {
                    <tr>
                        <td colspan="3" style="text-align: center;">查無資料</td>
                    </tr>
                    }
                </tbody>
            </table>
            <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize" [hidePageSize]="true"
                (page)="changePage($event)">
            </mat-paginator>
        </div>
    </div>
    <div class="btn-group">
        <button mat-flat-button (click)="cancel()">取消</button>
        <button mat-flat-button (click)="send()">送出</button>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>
import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl } from '@angular/platform-browser';

const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/;

@Pipe({
  name: 'safe',
  standalone: false
})
export class SafePipe implements PipeTransform {

  constructor(
    private _sanitizer: DomSanitizer
  ) {

  }

  public transform(value: string, type: string = 'html'): SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl {
    switch (type) {
      case 'html': return this._sanitizer.bypassSecurityTrustHtml(value);
      case 'style': return this._sanitizer.bypassSecurityTrustStyle(value);
      case 'background-image': return this._sanitizer.bypassSecurityTrustStyle(
        `url('${value}')`
      );
      case 'youtu-background-image':
        const match = value.match(regExp);
        return this._sanitizer.bypassSecurityTrustStyle(
          `url('https://i3.ytimg.com/vi/${(match && match[7].length === 11) ? match[7] : ''}/maxresdefault.jpg')`
        );
      case 'script': return this._sanitizer.bypassSecurityTrustScript(value);
      case 'url': return this._sanitizer.bypassSecurityTrustUrl(value);
      case 'resourceUrl': return this._sanitizer.bypassSecurityTrustResourceUrl(value);
      default: throw new Error(`Invalid safe type specified: ${type}`);
    }
  }

}

import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareService } from '../../../../core/services/share.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  defaultItem,
  getTypeListResp,
} from '../../../../interface/share.interface';
import { ReviewService } from '../../../../core/services/review.service';
import {
  approvalItem,
  batchApprovalReq,
  getReviewListReq,
  getReviewListResp,
  reviewItem,
} from '../../../../interface/review.interface';
import { ApprovalStatus, TypeList } from '../../../../enum/share.enum';
import Swal from 'sweetalert2';
import { format } from 'date-fns';

@Component({
  selector: 'app-review-list',
  standalone: false,

  templateUrl: './review-list.component.html',
  styleUrl: './review-list.component.scss',
})
export class ReviewListComponent {
  lang: string = '中文';
  loading: boolean = false;
  menuItemId: string = '';

  startDate: string | null = '';
  endDate: string | null = '';
  levelDecisionList: { typeValue: string; typeName: string }[] = [];
  levelDecision: string = '';
  reviewStatusList: { typeValue: string; typeName: string }[] = [];
  reviewStatus: string = '';
  isActive: boolean | null = null;
  typeList: { typeValue: string; typeName: string }[] = [];

  type: string = '';
  TypeList = TypeList;

  keywordstring: string = '';
  keywordtype: string = '';
  keywordtitle: string = '';
  userGroupId: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  selectAllStatus: boolean = false;
  selectList: approvalItem[] = [];

  reviewList: reviewItem[] = [];
  statusClassMap: { [key: string]: string } = {
    審核中: 'status-pending',
    已通過: 'status-approved',
    已退回: 'status-rejected',
    編輯中: 'status-editing',
  };

  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
    private shareService: ShareService,
    private reviewService: ReviewService
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getTypeList('類型');
      this.getTypeList('審核層級');
      this.getReviewList();
    });
  }

  changeReviewStatus(status: string) {
    this.reviewStatus = status;
    this.searchlist();
  }

  searchlist() {
    this.nowPage = 1;
    this.getReviewList();
  }

  getPolicy(policy: string) {
    return this.shareService.getPolicy(policy);
  }

  getTypeList(type: string) {
    this.shareService.getTypeList(type).subscribe({
      next: (resp: getTypeListResp) => {
        switch (type) {
          case '審核層級':
            this.levelDecisionList = resp.data;
            break;
          case '類型':
            this.typeList = resp.data;
            break;
        }
      },
      error: (err: HttpErrorResponse) => { },
    });
  }

  getReviewList() {
    let req: getReviewListReq = {
      startDate: this.startDate ? format(new Date(this.startDate), 'yyyy-MM-dd') : '',
      endDate: this.endDate ? format(new Date(this.endDate), 'yyyy-MM-dd') : '',
      approvalLevel:
        this.levelDecision === '' ? null : Number(this.levelDecision),
      status: this.reviewStatus === '' ? null : Number(this.reviewStatus),
      enable: this.isActive === null ? null : this.isActive,
      type: this.type,
      title: this.keywordtitle,
      pageSize: this.pageSize,
      currentPage: this.nowPage,
      lang: sessionStorage.getItem('lang') || 'zh',
    };
    this.loading = true;
    this.reviewService.getReviewList(req).subscribe({
      next: (resp: getReviewListResp) => {
        this.loading = false;
        this.reviewList = resp.data.data;

        this.reviewList = this.reviewList.map((item) => {
          const match = this.TypeList.find((x) => x.type === item.menuType);
          item.menuTypeText = match?.name ?? '';
          return item;
        });

        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.nowPage = 1;
    this.startDate = '';
    this.endDate = '';
    this.levelDecision = '';
    this.isActive = null;
    this.type = '';
    this.keywordtitle = '';
    this.getReviewList();
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getReviewList();
  }

  activeChange(item: reviewItem) {
    const originalEnable = item.enable;
    let req: { itemId: string; type: string } = {
      itemId: item.id,
      type: item.menuType,
    };
    this.reviewService.activeReview(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code !== 200) {
          Swal.fire('失敗', resp.message, 'error');
          item.enable = !originalEnable;
        }
        this.getReviewList();
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error');
        item.enable = !originalEnable;
      },
    });
  }

  goPage(event: Event, item: reviewItem) {
    event.preventDefault();
    if (item.status === '編輯中' || item.status === '已退回') {
      this.goEditPage(item);
    } else {
      this.goViewPage(item);
    }
  }

  isDisabled(item: reviewItem): boolean {
    if (item.status !== '審核中') {
      return true;
    }

    if (item.levelDecision === '二層決') {
      return !this.getPolicy('ApprovalLevel2DecisionManage');
    }

    if (item.levelDecision === '一層決') {
      if (item.isApprovedByManager) {
        return !this.getPolicy('ApprovalLevel1DecisionManage');
      } else {
        return !this.getPolicy('ApprovalLevel2DecisionManage');
      }
    }

    return true;
  }

  goReiewPage(item: reviewItem) {
    if (this.isDisabled(item)) {
      return;
    }
    this.goViewPage(item);
  }

  goEditPage(item: reviewItem) {
    console.log(item);
    switch (item.menuType) {
      case 'Img':
        this._router.navigate([`/manage/${item.menuItemId}/img/edit`], {
          queryParams: { id: item.typeGroupId },
        });
        break;
      case 'Line':
        this._router.navigate([`/manage/${item.menuItemId}/line/edit`], {
          queryParams: { id: item.typeGroupId },
        });
        break;
      case 'FAQ':
        this._router.navigate([`/manage/${item.menuItemId}/faq/edit`], {
          queryParams: { id: item.typeGroupId },
        });
        break;
      case 'HTML':
        this._router.navigate([`/manage/${item.menuItemId}/html`]);
        break;
      case 'HtmlZip':
        break;
      case 'Content':
        this._router.navigate([`/manage/${item.menuItemId}/content`]);
        break;
      case 'ShareResource':
        break;
      case 'HyperLink':
        break;
      case 'Video':
        this._router.navigate([`/manage/${item.menuItemId}/video/edit`], {
          queryParams: { id: item.typeGroupId },
        });
        break;
      case 'News':
        this._router.navigate([`/manage/${item.menuItemId}/news/edit`], {
          queryParams: { id: item.typeGroupId },
        });
        break;

      case 'Survey':
        this._router.navigate([`/manage/${item.menuItemId}/survey`]);
        break;
      case 'Ebook':
        this._router.navigate([`/manage/${item.menuItemId}/ebook/edit`], {
          queryParams: { id: item.id, typeGroupId: item.typeGroupId },
        });
        break;
      case 'ENewsletter':
        this._router.navigate([`/manage/${item.menuItemId}/eNewsletter/edit`], {
          queryParams: { id: item.id },
        });
        break;
    }
  }

  goViewPage(item: reviewItem) {
    let status: ApprovalStatus = ApprovalStatus.AfterApproval;
    switch (item.status) {
      case '已退回':
        status = ApprovalStatus.BeforeApproval;
        break;
      case '審核中':
        status = ApprovalStatus.AfterApproval;
        break;
      case '已通過':
        status = ApprovalStatus.EndApproval;
        break;
    }

    if (item.menuType === 'ENewsletter') {
      this._router.navigate(['manage/viewENewsletter'], {
        queryParams: {
          menuItemId: item.menuItemId,
          typeGroupId: item.typeGroupId,
          type: item.menuType,
          status: status,
        },
      });
      return;
    }

    if (item.menuType === 'Ebook') {
      this._router.navigate(['manage/viewEbook'], {
        queryParams: {
          menuItemId: item.menuItemId,
          bookId: item.id,
          typeGroupId: item.typeGroupId,
          type: item.menuType,
          status: status,
        },
      });
      return;
    }
    if (item.menuType === 'Video') {
      this._router.navigate(['manage/viewVideo'], {
        queryParams: {
          menuItemId: item.menuItemId,
          typeGroupId: item.typeGroupId,
          type: item.menuType,
          status: status,
        },
      });
      return;
    }

    this._router.navigate(['manage/view'], {
      queryParams: {
        menuItemId: item.menuItemId,
        typeGroupId: item.typeGroupId,
        type: item.menuType,
        status: status,
      },
    });
  }

  selectAll(event: Event) {
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      this.selectAllStatus = true;
      // 確保只加入當前分頁的項目，且不重複
      this.reviewList.forEach((item) => {
        // 檢查此項目是否已存在於 selectList 中
        const exists = this.selectList.some(
          (selectedItem) => selectedItem.typeGroupId === item.typeGroupId
        );

        if (!exists) {
          this.selectList.push({
            typeGroupId: item.typeGroupId,
            menuItemType: item.menuType,
          });
        }
      });
    } else {
      this.selectAllStatus = false;
      // 只移除 selectList 中存在於 reviewList (當前分頁列表) 的項目
      const currentReviewGroupIds = this.reviewList.map(
        (item) => item.typeGroupId
      );

      this.selectList = this.selectList.filter(
        (item) => !currentReviewGroupIds.includes(item.typeGroupId)
      );
    }
  }

  isSelected(item: approvalItem): boolean {
    return this.selectList.some(
      (x) =>
        x.typeGroupId === item.typeGroupId &&
        x.menuItemType === item.menuItemType
    );
  }

  changeSelect(event: Event, item: reviewItem) {
    this.selectAllStatus = false;
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      this.selectList.push({
        typeGroupId: item.typeGroupId,
        menuItemType: item.menuType,
      });
    } else {
      this.selectList = this.selectList.filter(
        (item) => item.typeGroupId !== item.typeGroupId
      );
    }
  }

  batchApproval() {
    let req: batchApprovalReq = {
      batchApprovalContent: this.selectList,
    };
    this.loading = true;
    this.reviewService.batchApproval(req).subscribe({
      next: (resp: defaultItem) => {
        this.loading = false;
        if (resp.code === 200) {
          Swal.fire('成功', '批次核准成功', 'success').then(() => {
            this.getReviewList();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
        Swal.fire('失敗', err.error.message, 'error');
      },
    });
  }
}

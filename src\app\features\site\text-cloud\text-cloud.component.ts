import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ShareService } from '../../../core/services/share.service';
import { TextCloudService } from '../../../core/services/text-cloud.service';
import {
  getTextCloudListReq,
  getTextCloudListResp,
  textCloudItem,
} from '../../../interface/textCloud.interface';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { TextCloudSettingDialogComponent } from '../../components/text-cloud-setting-dialog/text-cloud-setting-dialog.component';
import Swal from 'sweetalert2';
import { defaultItem } from '../../../interface/share.interface';

@Component({
  selector: 'app-text-cloud',
  standalone: false,

  templateUrl: './text-cloud.component.html',
  styleUrl: './text-cloud.component.scss',
})
export class TextCloudComponent {
  lang: string = '中文';
  loading: boolean = false;
  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;

  textCloudList: textCloudItem[] = [];
  keyword: string = '';

  constructor(
    private _route: ActivatedRoute,
    private shareService: ShareService,
    private textCloudService: TextCloudService,
    private dialog: MatDialog
  ) {
    this.lang = this.shareService.getLang() === 'zh' ? '中文' : '英文';
  }

  ngOnInit(): void {
    this.getCloueTextList();
  }

  searchlist() {
    this.nowPage = 1;
    this.getCloueTextList();
  }

  getCloueTextList() {
    let req: getTextCloudListReq = {
      text: this.keyword,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.loading = true;
    this.textCloudService.getTextCloudList(req).subscribe({
      next: (resp: getTextCloudListResp) => {
        this.loading = false;
        this.textCloudList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: (err: HttpErrorResponse) => {
        this.loading = false;
      },
    });
  }

  resetsearchlist() {
    this.keyword = '';
    this.nowPage = 1;
    this.getCloueTextList();
  }

  add() {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: TextCloudSettingDialogComponent,
          showHeader: true,
          title: '新增',
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getCloueTextList();
      });
  }

  edit(item: textCloudItem) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '500px',
          contentTemplate: TextCloudSettingDialogComponent,
          showHeader: true,
          title: '編輯',
          data: item,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getCloueTextList();
      });
  }

  delete(id: string) {
    Swal.fire({
      title: '請問確定要刪除?',
      text: '您將無法恢復這筆資訊!',
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,
    }).then((result) => {
      if (result.value) {
        this.loading = true;
        this.textCloudService.deleteTextCloud(id).subscribe({
          next: (resp: defaultItem) => {
            this.loading = false;
            resp.code === 200
              ? Swal.fire('成功', '刪除成功', 'success').then(() => {
                  this.getCloueTextList();
                })
              : Swal.fire('失敗', resp.message, 'error');
          },
          error: (err: HttpErrorResponse) => {
            this.loading = false;
            Swal.fire('失敗', err.error.message, 'error');
          },
        });
      }
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.loading = true;
    this.nowPage = $event.pageIndex + 1;
    this.getCloueTextList();
  }
}

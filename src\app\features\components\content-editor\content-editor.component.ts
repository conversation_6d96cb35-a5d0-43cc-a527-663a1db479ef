import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { postTypeTabs } from '../../../shared/data/editor-page';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { FileBoxComponent } from '../file-box/file-box.component';
import { Gallery } from '../../../shared/models/gallery.model';
import {
  contentItem,
  VideoCreateComponent,
} from '../video-create/video-create.component';
import { environment } from '../../../../environments/environment';
import {
  addressItem,
  buttonItem,
  cardItem,
  contentData,
  fileDataItem,
  HtmlDataItem,
  paragraphTitleItem,
  photoItem,
  photoListItem,
  tableItem,
  titleItem,
  videoItem,
  sportTableItem,
  palaSportTableItem,
  InboundTransferTableItem,
  PlaceTransferTableItem,
  PdfItem,
} from '../../../interface/editor.interface';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { EDITORTYPE } from '../../../enum/editor.enum';
import { v4 as uuidv4 } from 'uuid';
import { DomSanitizer } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import 'froala-editor/js/plugins.pkgd.min.js';
import { FileLinkComponent } from '../dialogs/file-link/file-link.component';
@Component({
  selector: 'app-content-editor',
  standalone: false,

  templateUrl: './content-editor.component.html',
  styleUrl: './content-editor.component.scss',
})
export class ContentEditorComponent implements OnInit {
  @Input() editContentData: contentData[] = [];
  @Input() isENewsletter: boolean = false;
  @Output() contentData = new EventEmitter<contentData[]>();

  form: FormGroup;

  loading: boolean = false;
  postTypeTabs = postTypeTabs;
  editorArr: contentData[] = [];
  froalaOptions = environment.froalaConfig;
  TmpgalleryShow: Gallery[] = [];
  documents: File[] = [];

  createTime!: Date;
  editTime!: Date;
  create: string = '';
  edit1: string = '';
  private valueChangesSubscription?: Subscription;

  constructor(
    public dialog: MatDialog,
    private formBuilder: FormBuilder,
    private sanitizer: DomSanitizer
  ) {
    this.form = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.subscribeToFormChanges();
    this.form.valueChanges.subscribe(() => {
      this.outputContentData();
    });
  }

  ngOnDestroy(): void {
    this.valueChangesSubscription?.unsubscribe();
  }

  private subscribeToFormChanges() {
    this.valueChangesSubscription?.unsubscribe();
    this.valueChangesSubscription = this.form.valueChanges.subscribe(() => {
      this.outputContentData();
    });
  }

  initializeForm(data: contentData[]): void {
    this.editorArr = data;
    const formGroup = this.formBuilder.group({});
    this.editorArr.forEach((block) => {
      const controlName = block.id;
      switch (block.type as EDITORTYPE) {
        case EDITORTYPE.Content:
          {
            const typedBlock = block.data as contentItem;
            formGroup.addControl(
              controlName,
              this.formBuilder.control(
                typedBlock.content || '',
                Validators.required
              )
            );
          }
          break;
        // case EDITORTYPE.FileData:
        //   {
        //     const typedBlock = block.data as fileDataItem;
        //     formGroup.addControl(
        //       controlName,
        //       this.formBuilder.control(
        //         typedBlock.url || '',
        //         Validators.required
        //       )
        //     );
        //   }
        //   break;
        case EDITORTYPE.Title:
          {
            const typedBlock = block.data as titleItem;
            formGroup.addControl(
              controlName,
              this.formBuilder.control(
                typedBlock.title || '',
                Validators.required
              )
            );
          }
          break;
        case EDITORTYPE.ParagraphTitle:
          {
            const typedBlock = block.data as paragraphTitleItem;
            formGroup.addControl(
              controlName,
              this.formBuilder.control(
                typedBlock.title || '',
                Validators.required
              )
            );
          }
          break;
        // case EDITORTYPE.Photo:
        //   {
        //     const typedBlock = block.data as photoItem;
        //     formGroup.addControl(
        //       controlName,
        //       this.formBuilder.control(
        //         typedBlock.fileUrl || '',
        //         Validators.required
        //       )
        //     );
        //   }
        //   break;
        // case EDITORTYPE.PhotoList:
        //   {
        //     const typedBlock = block.data as photoListItem;
        //     formGroup.addControl(
        //       controlName,
        //       this.formBuilder.control(
        //         typedBlock.photoDatas || '',
        //         Validators.required
        //       )
        //     );
        //   }
        //   break;
        // case EDITORTYPE.Video:
        //   {
        //     const typedBlock = block.data as videoItem;
        //     formGroup.addControl(
        //       controlName,
        //       this.formBuilder.control(
        //         typedBlock.fileUrl || '',
        //         Validators.required
        //       )
        //     );
        //   }
        //   break;
        case EDITORTYPE.Address:
          {
            const typedBlock = block.data as addressItem;
            formGroup.addControl(
              controlName,
              this.formBuilder.control(
                typedBlock.address || '',
                Validators.required
              )
            );
          }
          break;
        case EDITORTYPE.Button:
          {
            const typedBlock = block.data as buttonItem;
            formGroup.addControl(
              controlName + '_title',
              this.formBuilder.control(
                typedBlock.title || '',
                Validators.required
              )
            );
            formGroup.addControl(
              controlName + '_url',
              this.formBuilder.control(
                typedBlock.url || '',
                Validators.required
              )
            );
          }
          break;
        case EDITORTYPE.Html:
          {
            const typedBlock = block.data as HtmlDataItem;
            formGroup.addControl(
              controlName,
              this.formBuilder.control(
                typedBlock.htmlString || '',
                Validators.required
              )
            );
          }
          break;
        case EDITORTYPE.Card:
          {
            const typedBlock = block.data as cardItem;
            formGroup.addControl(
              controlName + '_cardName',
              this.formBuilder.control(
                typedBlock.cardName || '',
                Validators.required
              )
            );
            formGroup.addControl(
              controlName + '_cardUrlName',
              this.formBuilder.control(
                typedBlock.cardUrlName || '',
                Validators.required
              )
            );
            formGroup.addControl(
              controlName + '_cardUrl',
              this.formBuilder.control(
                typedBlock.cardUrl || '',
                Validators.required
              )
            );
          }
          break;
      }
    });
    this.form = formGroup; // 替換舊的表單結構
    this.subscribeToFormChanges();
    this.outputContentData();
  }

  addEditorBlock(postType: string) {
    const id = `${uuidv4()}`;
    let newData: contentData = {
      id: id,
      type: postType as EDITORTYPE,
    };
    this.editorArr.unshift(newData);
    switch (postType) {
      case 'button':
        // 按鈕區需要兩個輸入框
        this.form.addControl(
          `${id}_title`,
          new FormControl('', Validators.required)
        );
        this.form.addControl(
          `${id}_url`,
          new FormControl('', Validators.required)
        );
        break;
      case 'table':
        // 按鈕區需要兩個輸入框
        this.form.addControl(
          `${id}_search`,
          new FormControl(false, Validators.required)
        );
        break;
      case 'card':
        this.form.addControl(
          `${id}_cardName`,
          new FormControl('', Validators.required)
        );
        this.form.addControl(
          `${id}_cardUrlName`,
          new FormControl('', Validators.required)
        );
        this.form.addControl(
          `${id}_cardUrl`,
          new FormControl('', Validators.required)
        );
        break;
      default:
        this.form.addControl(id, new FormControl('', Validators.required));
    }
    this.outputContentData();
  }

  moveUp(index: number) {
    if (index > 0) {
      [this.editorArr[index - 1], this.editorArr[index]] = [
        this.editorArr[index],
        this.editorArr[index - 1],
      ];
      this.outputContentData();
    }
  }

  moveDown(index: number) {
    if (index < this.editorArr.length - 1) {
      [this.editorArr[index], this.editorArr[index + 1]] = [
        this.editorArr[index + 1],
        this.editorArr[index],
      ];
      this.outputContentData();
    }
  }

  deleteBlock(index: number, id: string) {
    this.editorArr.splice(index, 1);
    this.form.removeControl(id);
    this.outputContentData();
  }

  getFileUrl(editorBlock: contentData): string | null {
    return (editorBlock.data as photoItem | videoItem).fileUrl;
  }

  sanitizeUrl(editorBlock: contentData) {
    const url = new URL((editorBlock.data as videoItem).fileUrl);
    const videoId = url.searchParams.get('v');
    const embedUrl = 'https://www.youtube.com/embed/' + videoId;
    return this.sanitizer.bypassSecurityTrustResourceUrl(embedUrl);
  }

  getFileType(editorBlock: contentData): string | null {
    return (editorBlock.data as videoItem).type;
  }

  getFileUrlList(editorBlock: contentData): { fileUrl: string }[] {
    return (editorBlock.data as photoListItem).photoDatas;
  }
  getFileDataList(
    editorBlock: contentData
  ): { fileName: string; fileId: string }[] {
    return (editorBlock.data as fileDataItem).filedatalist;
  }

  getTableDataList(
    editorBlock: contentData
  ): { fileName: string; fileId: string }[] {
    return (editorBlock.data as tableItem).filedatalist;
  }

  getsportTableDataList(
    editorBlock: contentData
  ): { fileName: string; fileId: string }[] {
    return (editorBlock.data as sportTableItem).filedatalist;
  }

  getpalaSportTableDataList(
    editorBlock: contentData
  ): { fileName: string; fileId: string }[] {
    return (editorBlock.data as palaSportTableItem).filedatalist;
  }

  getcultureTableDataList(
    editorBlock: contentData
  ): { fileName: string; fileId: string }[] {
    return (editorBlock.data as palaSportTableItem).filedatalist;
  }

  getPdfFileName(editorBlock: contentData): string {
    return (editorBlock.data as PdfItem).pdfName;
  }

  selectGalleryImage(id: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          this.editorArr.find((item) => {
            return item.id === id;
          })?.data;
          const item = this.editorArr.find((item) => item.id === id);
          if (item) {
            (item.data as photoItem) = { fileUrl: resp.data.previewImageUrl };
          }
          this.outputContentData();
        }
      });
  }

  selectMultipleGalleryImage(id: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Image',
          isMultiple: true,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (resp) {
          const editorItem = this.editorArr.find((item) => item.id === id);
          if (!editorItem) return;

          const photoData = editorItem.data as photoListItem;
          const existingFileIds = new Set(
            photoData?.photoDatas?.map((item) => item.fileUrl) || []
          );

          const newFiles = resp.data.filter(
            (item: { previewImageUrl: string }) =>
              !existingFileIds.has(item.previewImageUrl)
          );

          const dataList = newFiles.map(
            (item: { previewImageUrl: string }) => ({
              fileUrl: item.previewImageUrl,
            })
          );
          if (photoData && Array.isArray(photoData.photoDatas)) {
            photoData.photoDatas.push(...dataList);
          } else {
            editorItem.data = { photoDatas: dataList } as photoListItem;
          }
          this.outputContentData();
        }
      });
  }

  removeImageData(id: string, index: number) {
    const editorItem = this.editorArr.find((item) => item.id === id);
    if (!editorItem) return;
    const photoDatasList = (editorItem.data as photoListItem).photoDatas;
    if (photoDatasList && index >= 0 && index < photoDatasList.length) {
      photoDatasList.splice(index, 1);
    }
    this.outputContentData();
  }

  selectGalleryFile(id: string, selectType: string = '') {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'File',
          isMultiple: selectType === 'table' ? false : true,
        },
      })
      .afterClosed()
      .subscribe((resp: any) => {
        if (!resp) {
          return;
        }
        const editorItem = this.editorArr.find((item) => item.id === id);
        if (!editorItem) return;

        if (selectType === 'table') {
          const dataList = [
            {
              fileId: resp.data.dataId,
              fileName: resp.data.name,
            },
          ];
          editorItem.data = { filedatalist: dataList } as tableItem;
        } else {
          const fileData = editorItem.data as fileDataItem;
          const existingFileIds = new Set(
            fileData?.filedatalist?.map((item) => item.fileId) || []
          );

          const newFiles = resp.data.filter(
            (item: { dataId: string; name: string }) =>
              !existingFileIds.has(item.dataId)
          );

          const dataList = newFiles.map(
            (item: { dataId: string; name: string }) => ({
              fileId: item.dataId,
              fileName: item.name,
            })
          );
          if (fileData && Array.isArray(fileData.filedatalist)) {
            fileData.filedatalist.push(...dataList);
          } else {
            editorItem.data = { filedatalist: dataList } as fileDataItem;
          }
        }
        this.outputContentData();
      });
  }

  removeFileData(id: string, index: number) {
    const editorItem = this.editorArr.find((item) => item.id === id);
    if (!editorItem) return;

    const fileDataList = (editorItem.data as fileDataItem).filedatalist;
    if (fileDataList && index >= 0 && index < fileDataList.length) {
      fileDataList.splice(index, 1);
    }

    this.outputContentData();
  }

  selectPdfFile(id: string, selectType: string = '') {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '1000px',
          height: '500px',
          contentTemplate: FileBoxComponent,
          type: 'Pdf',
          isMultiple: false,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        if (!resp) {
          return;
        }
        const editorItem = this.editorArr.find((item) => item.id === id);
        if (!editorItem) return;

        editorItem.data = {
          pdfName: resp.data.name,
          pdfDataUrl: resp.data.previewImageUrl,
        } as PdfItem;

        this.outputContentData();
      });
  }

  inputLink(id: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '800px',
          height: '400px',
          contentTemplate: FileLinkComponent,
          type: 'Image',
          status: 'ADD',
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        // 確保有返回值
        if (!resp) {
          return;
        }

        // 查找對應的 editorItem
        const editorItem = this.editorArr.find((item) => item.id === id);
        if (!editorItem) {
          return;
        }

        // 初始化 editorItem.data 如果未定義
        if (!editorItem.data) {
          editorItem.data = {} as fileDataItem;
        }

        // 初始化 filedatalist 如果未定義
        if (!(editorItem.data as fileDataItem).filedatalist) {
          (editorItem.data as fileDataItem).filedatalist = [];
        }

        // 如果 resp 是單個對象，將其包裝成數組進行處理
        const responses = Array.isArray(resp) ? resp : [resp];

        // 將每個 item 加入到 filedatalist
        responses.forEach((item: { linkUrl: string; fileName: string }) => {
          (editorItem.data as fileDataItem).filedatalist.push({
            fileId: '', // 預留空間或根據業務邏輯填充
            fileName: item.fileName,
            linkUrl: item.linkUrl,
            fileType: 'link',
          });
        });

        this.outputContentData();
      });
  }

  /** 選擇檔案 */
  uploadFile($event: any) {
    const files = $event.target.files;
    if (files.length) {
      for (let i = 0; i < files.length; i++) {
        let ext = files[i].name.substring(
          files[i].name.lastIndexOf('.') + 1,
          files[i].name.length
        );
        if (
          ext != 'jpg' &&
          ext != 'jpeg' &&
          ext != 'png' &&
          ext != 'doc' &&
          ext != 'docx' &&
          ext != 'pdf' &&
          ext != 'xlsx' &&
          ext != 'xls' &&
          ext != 'odp' &&
          ext != 'odt' &&
          ext != 'ods'
        ) {
          alert(
            "'檔案類型必須為jpg、png、word、excel、odp、odt、ods或pdf檔案'"
          );
          return;
        }
        {
          this.documents.push(files[i]);
        }
      }
    }
  }

  createVideo(id: string) {
    this.dialog
      .open(DialogComponent, {
        data: {
          width: '70%',
          height: '50vh',
          showHeader: true,
          title: '新增影片',
          isENewsletter: this.isENewsletter,
          contentTemplate: VideoCreateComponent,
        },
      })
      .afterClosed()
      .subscribe((resp) => {
        this.editorArr.find((item) => {
          return item.id === id;
        })?.data;
        const item = this.editorArr.find((item) => item.id === id);
        if (item) {
          (item.data as videoItem) = {
            fileUrl:
              resp.data.type === 'Youtube'
                ? resp.data.youtubeUrl
                : resp.data.videoDataId,
            type: resp.data.type,
          };
          this.outputContentData();
        }
      });
  }

  outputContentData() {
    this.editorArr.map((item) => {
      switch (item.type) {
        case EDITORTYPE.Content:
          item.data = { content: this.form.get(item.id)?.value };
          break;
        case EDITORTYPE.Title:
        case EDITORTYPE.ParagraphTitle:
          item.data = { title: this.form.get(item.id)?.value };
          break;
        case EDITORTYPE.Address:
          item.data = { address: this.form.get(item.id)?.value };
          break;
        case EDITORTYPE.Button:
          item.data = {
            title: this.form.get(item.id + '_title')?.value,
            url: this.form.get(item.id + '_url')?.value,
          };
          break;
        case EDITORTYPE.Html:
          item.data = { htmlString: this.form.get(item.id)?.value };
          break;
        case EDITORTYPE.Card:
          item.data = {
            cardUrl: this.form.get(item.id + '_cardUrl')?.value,
            cardUrlName: this.form.get(item.id + '_cardUrlName')?.value,
            cardName: this.form.get(item.id + '_cardName')?.value,
          };
          break;
      }
    });
    this.contentData.emit(this.editorArr);
  }
}

import { Component } from '@angular/core';
import { VideoService } from '../../../../core/services/video.service';
import {
  getVideoListReq,
  getVideoListResp,
  videoItem,
} from '../../../../interface/video.interface';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { DialogComponent } from '../../../../shared/components/dialog/dialog.component';
import {
  changeSortReq,
  defaultItem,
} from '../../../../interface/share.interface';
import { ShareService } from '../../../../core/services/share.service';
import { SetSortDialogComponent } from '../../../components/set-sort-dialog/set-sort-dialog.component';
import { AddKeywordDialogComponent } from '../../../components/add-keyword-dialog/add-keyword-dialog.component';

@Component({
  selector: 'app-video-list',
  standalone: false,

  templateUrl: './video-list.component.html',
  styleUrl: './video-list.component.scss',
})
export class VideoListComponent {
  loading: boolean = false;
  menuItemId: string = '';
  title: string = '';

  nowPage: number = 1;
  pageSize: number = 10;
  totalCount: number = 0;
  videoList: videoItem[] = [];
  keyword: string = '';

  constructor(
    private _route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private videoService: VideoService,
    private shareService: ShareService,
  ) { }

  ngOnInit(): void {
    this._route.parent?.paramMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getVideoList();
    });
  }

  getVideoList() {
    let req: getVideoListReq = {
      menuItemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.loading = true;
    this.videoService.getVideoList(req).subscribe({
      next: (resp: getVideoListResp) => {
        this.loading = false;
        this.title = resp.data.title;
        this.keyword = resp.data.keyword;
        this.videoList = resp.data.data;
        this.totalCount = resp.data.totalCount;
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  addKeyword() {
    this.dialog.open(DialogComponent, {
      data: {
        width: '500px',
        contentTemplate: AddKeywordDialogComponent,
        showHeader: true,
        id: this.menuItemId,
        keyword: this.keyword,
        title: '新增關鍵字',
      },
    }).afterClosed().subscribe(() => {
      this.getVideoList();
    });
  }

  addVideo() {
    this.router.navigate([`/manage/${this.menuItemId}/video/edit`]);
  }

  editVideo(item: videoItem) {
    this.router.navigate([`/manage/${this.menuItemId}/video/edit`], {
      queryParams: { id: item.typeGroupId },
    });
  }

  deleteVideo(id: string) {
    this.videoService.deleteVideo(id).subscribe({
      next: () => {
        Swal.fire('成功', '刪除成功', 'success').then(() => {
          this.getVideoList();
        });
      },
      error: () => {
        Swal.fire('失敗', '刪除失敗', 'error');
      },
    });
  }

  changeSort(id: string, down: boolean) {
    let req: changeSortReq = {
      id: id,
      down: down,
      type: 'video',
    };
    this.shareService.changeSort(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.code === 0) {
          this.getVideoList();
        } else {
          Swal.fire('', resp.message, 'error');
        }
      },
      error: () => { },
    });
  }

  setSort(item: videoItem) {
    this.dialog.open(DialogComponent, {
      data: {
        width: '30%',
        title: '設定排序',
        typeGroupId: item.typeGroupId,
        menuitemId: this.menuItemId,
        sort: item.sort,
        totalCount: this.totalCount,
        type: 'video',
        contentTemplate: SetSortDialogComponent,
      },
    }).afterClosed().subscribe(() => {
      this.getVideoList();
    });
  }

  /** 換頁 */
  changePage($event: any) {
    this.nowPage = $event.pageIndex + 1;
    this.getVideoList();
  }
}

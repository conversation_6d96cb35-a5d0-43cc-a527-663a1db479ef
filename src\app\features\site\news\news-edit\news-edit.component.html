<div>
    <div class="news-layout">
        <span class="white">
            <h1>內容編輯</h1>
        </span>
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">標題</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>標題</mat-label>
                    <input matInput type="text" [(ngModel)]="title">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">類型</span>
                </div>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="type" placeholder="請選擇類型">
                        @for ( item of typeList; track $index) {
                        <mat-option [value]="item.typeValue">{{item.typeName}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="required-star">*</span>
                    <span class="title">群組</span>
                </div>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="userGroup" placeholder="請選擇群組">
                        @for ( item of groupList; track $index) {
                        <mat-option [value]="item.userGroupId">{{item.userGroupName}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">描述</span>
                </div>
                <mat-form-field class="example-form-field" appearance="outline">
                    <mat-label>描述</mat-label>
                    <input matInput type="text" [(ngModel)]="description">
                </mat-form-field>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">發布時間</span>
                </div>
                <div style="margin: 1em;">
                    <mat-form-field appearance="outline">
                        <mat-label>發布日期</mat-label>
                        <input matInput [matDatepicker]="pickerStart" [(ngModel)]="startTime"
                            (ngModelChange)="endTime = ''" readonly />
                        <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
                        <mat-datepicker #pickerStart></mat-datepicker>
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    <mat-form-field appearance="outline">
                        <mat-label>發布時間</mat-label>
                        <input matInput [matTimepicker]="startTimepicker" [(ngModel)]="startTime"
                            [ngModelOptions]="{updateOn: 'blur'}" [matTimepickerMax]="maxStartTime">
                        <mat-timepicker interval="1h" #startTimepicker />
                        <mat-timepicker-toggle [for]="startTimepicker" matSuffix />
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    <mat-slide-toggle class="example-margin" [(ngModel)]="isEnd" (ngModelChange)="changeIsEnd($event)">
                        設定到期日
                    </mat-slide-toggle>&nbsp;&nbsp;&nbsp;
                    @if(isEnd){
                    <mat-form-field appearance="outline">
                        <mat-label>到期日期</mat-label>
                        <input matInput [matDatepicker]="pickerEnd" [(ngModel)]="endTime" [min]="startTime" readonly />
                        <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                        <mat-datepicker #pickerEnd></mat-datepicker>
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    <mat-form-field appearance="outline">
                        <mat-label>到期時間</mat-label>
                        <input matInput [matTimepicker]="endTimepicker" [(ngModel)]="endTime"
                            [ngModelOptions]="{updateOn: 'blur'}" [matTimepickerMin]="minEndTime">
                        <mat-timepicker interval="1h" #endTimepicker />
                        <mat-timepicker-toggle [for]="endTimepicker" matSuffix />
                    </mat-form-field>&nbsp;&nbsp;&nbsp;
                    }
                    <mat-slide-toggle class="example-margin" [(ngModel)]="isTop">
                        置頂
                    </mat-slide-toggle>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">封面上傳</span>
                </div>
                <p>&nbsp;※&nbsp;只能上傳jpg、png檔案</p>
                <div>
                    <button mat-flat-button class="btn" (click)="selectGalleryImage()">
                        選擇圖片
                    </button>
                </div>
                @if(coverId){
                <div style="display: flex;flex-direction: column;">
                    <div>
                        <img [src]="cover" alt="" width="100px"><br />
                        <mat-icon style="margin-left:30px" (click)="cover='' " (click)="coverId='' ">delete</mat-icon>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
    <app-content-editor [editContentData]="contentData" (contentData)="getContentData($event)"></app-content-editor>
    <div class="news-layout">
        <div class="contents">
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核層級</span>
                    <mat-radio-group [(ngModel)]="levelDecision">
                        <mat-radio-button [value]="1">一層決</mat-radio-button>
                        <mat-radio-button [value]="2">二層決</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">消息建立者 {{createUser}}</span>
                </div>
            </div>
            <div class="block">
                <div class="title-ctn">
                    <span class="title">最後修改者 {{editUser}}</span>
                </div>
            </div>
            @if(reason){
            <div class="block">
                <div class="title-ctn">
                    <span class="title" style="white-space: pre-wrap;">審核意見 : {{reason}}</span>
                </div>
            </div>
            }
            @if(reviewFileUrl){
            <div class="block">
                <div class="title-ctn">
                    <span class="title">審核檔案 : <a [href]="reviewFileUrl" target="_blank">{{reviewFileName}}</a></span>
                </div>
            </div>
            }
        </div>
        <div class="btn-group">
            <button mat-flat-button (click)="cancel()">回上一頁</button>
            <button mat-flat-button (click)="save(saveStatus.SAVE)">存檔</button>
            <button mat-flat-button (click)="save(saveStatus.SAVELEAVE)">存檔並離開</button>
            <button mat-flat-button (click)="view()">預覽</button>
        </div>
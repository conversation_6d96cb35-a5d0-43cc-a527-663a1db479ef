<div class="news-layout news-list-custom-css">
    <div class="white">
        <h1>搜尋關鍵字統計-{{lang}}</h1>
    </div>
    <div class="list-container">
        <div class="user-search">
            <div>
                <button mat-flat-button (click)="export()">匯出</button>
                &nbsp;
                <button mat-flat-button (click)="isTable = !isTable">{{isTable?'列表':'圖表'}}</button>
            </div>
        </div>
        <div class="contents">
            @if(isTable){
            <div class="table-container">
                <table class="review-table">
                    <thead>
                        <tr>
                            <th width="40px">項次</th>
                            <th>關鍵字</th>
                            <th width="90px" (click)="changeSort(ColumType.userCount)">使用者
                                <span class="table-sort-list">
                                    @if(column===ColumType.userCount){
                                    <app-sort [isAsc]="isAsc"></app-sort>
                                    }@else{
                                    <img src="assets/images/arrow/arrow-drop-default.svg" alt="">
                                    }
                                </span>
                            </th>
                            <th width="90px" (click)="changeSort(ColumType.usageCount)">搜尋次數
                                <span class="table-sort-list">
                                    @if(column===ColumType.usageCount){
                                    <app-sort [isAsc]="isAsc"></app-sort>
                                    }@else{
                                    <img src="assets/images/arrow/arrow-drop-default.svg" alt="">
                                    }
                                </span>
                            </th>
                    </thead>
                    <tbody>
                        @for (item of searchKeywordLogList;let index=$index ;track item) {
                        <tr>
                            <td data-label="項次">{{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}</td>
                            <td data-label="關鍵字">{{item.keyWord}}</td>
                            <td data-label="使用者">{{item.userCount}}</td>
                            <td data-label="搜尋次數">{{item.usageCount}}</td>
                        </tr>
                        }@empty {
                        <tr>
                            <td colspan="3" style="text-align: center;">查無資料</td>
                        </tr>
                        }
                    </tbody>
                </table>
                <mat-paginator [pageIndex]="nowPage - 1" [length]="totalCount" [pageSize]="pageSize"
                    [hidePageSize]="true" (page)="changePage($event)">
                </mat-paginator>
            </div>
            }@else{
            <div echarts [options]="chartOption"></div>
            }

        </div>
    </div>
    <app-loading [loading]="loading"></app-loading>
</div>